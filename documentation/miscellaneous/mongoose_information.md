> Gemini 2.5 Pro (08.06.2025)
> 
> You've hit the nail on the head. Thank you for providing the exact code for `utility.schema.ts`. This is the final piece of the puzzle, and it confirms the diagnosis perfectly.

The problem is that this powerful, flexible `ObjectIdSchema` is designed for **input validation** (checking if an incoming value is a valid ObjectId string or already an ObjectId instance), but you are using it to define the shape of your **database output** from a `.lean()` query.

### The Core Mismatch

1.  **What `.lean()` does:** When you use `.lean()` on a Mongoose query, it skips creating full Mongoose documents and returns plain JavaScript objects. Crucially, it converts all `mongoose.Types.ObjectId` instances into simple **strings**.
2.  **What your schema does:** Your `ObjectIdSchema` is a complex `z.union` containing a `z.string().refine().transform()`. This `.refine().transform()` creates a `ZodEffects` wrapper.
3.  **The Conflict:** Your `company-directory.service.ts` gets a plain object from the database where `fkCompanyGroupId` is a simple `string`. It then tries to parse this with `CompanyDirectoryArraySchema`, which expects `fkCompanyGroupId` to conform to your complex `ZodEffects`-wrapped union schema. TypeScript sees this deep, recursive, and mismatched type and gives up, resulting in the "Type instantiation is excessively deep" error.

### The Solution: Separate Schemas for Separate Concerns

You need two different types of schemas for ObjectIds:

1.  **An Input Schema:** For validating incoming data before it goes into the database. This is what you have now.
2.  **An Output/Response Schema:** A very simple schema for parsing data that comes *out* of the database via `.lean()`. This schema must be "clean" (no `ZodEffects`) and match the data shape, which is just a string.

Here is the step-by-step fix.

#### Step 1: Refactor `utility.schema.ts` in `shared-models`

We will create two distinct, clearly named schemas.

**Corrected `excelytics.shared-models/src/schemas/utility.schema.ts`**

```typescript
import { z } from 'zod';
import mongoose from 'mongoose';

/**
 * A simple, "clean" schema for validating an ObjectId that has been converted
 * to a string. This is ideal for parsing the output of Mongoose `.lean()` queries.
 * It contains no ZodEffects, making it safe for type inference.
 */
export const ObjectIdStringSchema = z.string();

/**
 * A powerful schema for validating INPUT data. It accepts either a valid
 * ObjectId string (and transforms it to an ObjectId instance) or an
 * existing ObjectId instance. Use this for validating request bodies
 * before saving to the database.
 */
export const ObjectIdInputSchema = z
	.union([
		z
			.string()
			.refine(val => mongoose.Types.ObjectId.isValid(val), {
				message: 'Invalid ObjectId string',
			})
			.transform(val => new mongoose.Types.ObjectId(val)),
		z.instanceof(mongoose.Types.ObjectId),
	])
	.optional(); // Making it optional might be useful for reuse
```

#### Step 2: Update All Consuming Schemas in `shared-models`

Now, go through your other schemas (`company-directory.schema.ts`, etc.) and use the correct schema for the job. Since these schemas are used to parse database responses, they should use the clean `ObjectIdStringSchema`.

**Corrected `excelytics.shared-models/src/schemas/company-directory.schema.ts`**

```typescript
import z from 'zod';
// Import the NEW, clean schema
import { ObjectIdStringSchema } from './utility.schema';
import { CompanyDirectoryStringKeys } from '../types';

// This schema defines the shape of data coming from a .lean() query.
// Therefore, it must use the simple ObjectIdStringSchema.
export const CompanyDirectorySchema = z.object({
	fkCompanyGroupId: z.lazy(() => ObjectIdStringSchema).optional(),
	fkCompanyId: z.lazy(() => ObjectIdStringSchema).optional(),
	fkIndividualId: z.lazy(() => ObjectIdStringSchema).optional(),

	companyRegistrationNumber: z
		.string()
		.min(1, 'Company registration number is required'),
	companyName: z.string().min(1, 'Company name is required'),
});

// The rest of your file remains the same, as it builds on the clean base schema.
export const CompanyDirectoryCreateSchema = CompanyDirectorySchema.refine(
	// ...
);
export const CompanyDirectoryUpdateSchema = CompanyDirectorySchema.partial().extend(
	// ...
);
export const CompanyDirectoryResponseSchema = CompanyDirectorySchema.extend({
	_id: z.string(),
});
export const CompanyDirectoryArraySchema = z.array(
	CompanyDirectoryResponseSchema,
);
```

#### Step 3: Update Schemas in the `Calc` Repo

Your `FinanceRawSchema` in the `Calc` repo also needs to be updated to use the clean schema.

**Corrected `Introspection.Finance.Calc/src/schemas/finance-raw.schema.ts`**

```typescript
import z from 'zod';
import { EnumCategories } from 'excelytics.shared-models';
// Import the clean schema from your shared package
import { ObjectIdStringSchema } from 'excelytics.shared-models';

export const FinanceRawSchema = z.object({
	_id: z.any().optional(),
	id: z.string().optional(),

	// Use the clean schema for all ObjectId fields
	fkCompanyDirectoryId: ObjectIdStringSchema,
	fkCompanyGroupId: z.lazy(() => ObjectIdStringSchema).optional(),
	fkCompanyId: z.lazy(() => ObjectIdStringSchema).optional(),
	fkIndividualId: z.lazy(() => ObjectIdStringSchema).optional(),

	// ... rest of the schema
	transactionId: z
		.string({ required_error: 'Transaction ID is required' })
		.min(1, 'Transaction ID cannot be empty'),
	date: z.date({ required_error: 'Date is required' }),
	period: z
		.string({ required_error: 'Period is required' })
		.min(1, 'Period cannot be empty'),
	categoryCode: z.nativeEnum(EnumCategories),
	subCategoryCode: z.number({ required_error: 'SubCategoryCode is required' }),
	expected: z.number({ required_error: 'Expected is required' }),
	actual: z.number({ required_error: 'Actual is required' }),
});
```

### Summary of Actions

1.  Apply the changes to `utility.schema.ts` in your `shared-models` package.
2.  Apply the changes to `company-directory.schema.ts` (and any others) in `shared-models`.
3.  Publish the new version of `excelytics.shared-models`.
4.  In your `Calc` repo, run `bun install` to get the updated package.
5.  Update `finance-raw.schema.ts` in `Calc`.
6.  Restart your `Calc` dev server.

This pattern of separating input validation schemas from output/response parsing schemas is extremely robust and will prevent these kinds of subtle type inference errors. The errors in your `company-directory.service.ts` will now be resolved.