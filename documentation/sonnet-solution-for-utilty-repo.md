# ?? Extensive Solution & Recommendations

## ?? Option 1: Immediate Fix

### **Root Cause Analysis**
The error `Cannot find package 'excelytics.shared-models'` occurs because:
1. Your internal packages aren't properly authenticated/accessible in the CI environment
2. The `bunfig.toml` authentication isn't working correctly for Bun
3. The packages might not be published to your Azure Artifacts feed

### **Complete Fix Solution**

#### **1. Fix Authentication & Registry Setup**

Replace your `setup-environment.yml` with this enhanced version:

```yaml
# pipelines/steps/setup-environment.yml
steps:
  - checkout: self
    displayName: 'Checkout complete source'
    fetchDepth: 0

  - task: NodeTool@0
    displayName: 'Setup Node.js 22.11.0'
    inputs:
      versionSpec: '22.11.0'

  # Install Bun globally
  - task: Npm@1
    displayName: 'Setup Bun Globally'
    inputs:
      command: 'custom'
      customCommand: 'install -g bun'
      verbose: true

  # Create proper .npmrc for Azure Artifacts authentication
  - task: npmAuthenticate@0
    displayName: 'Authenticate with Azure Artifacts'
    inputs:
      workingFile: '$(System.DefaultWorkingDirectory)/.npmrc'

  # Create bunfig.toml with proper authentication
  - script: |
      echo "Setting up Bun authentication for Azure Artifacts..."
      
      # Read the authenticated .npmrc to get the token
      if [ -f ".npmrc" ]; then
        echo "Found .npmrc file, extracting token..."
        cat .npmrc
        
        # Extract registry URL and token from .npmrc
        REGISTRY_URL=$(grep -o 'registry=.*' .npmrc | cut -d'=' -f2- | tr -d '\r')
        AUTH_TOKEN=$(grep -o '_authToken=.*' .npmrc | cut -d'=' -f2- | tr -d '\r')
        
        echo "Registry URL: $REGISTRY_URL"
        echo "Auth token exists: $([ -n "$AUTH_TOKEN" ] && echo "yes" || echo "no")"
        
        # Create bunfig.toml with proper authentication
        cat > bunfig.toml << EOF
[install]
# Configure registry for your Azure Artifacts feed
registry = "$REGISTRY_URL"

[install.scopes]
# Map your organization scope to the Azure Artifacts feed
"@introspection" = "$REGISTRY_URL"
"excelytics.shared-internals" = "$REGISTRY_URL"
"excelytics.shared-models" = "$REGISTRY_URL"

[install.registry]
# Authentication token for Azure Artifacts
"$REGISTRY_URL" = { token = "$AUTH_TOKEN" }
EOF

        echo "Created bunfig.toml:"
        cat bunfig.toml
        
        # Copy bunfig.toml to mermaid-scripts directory
        cp bunfig.toml mermaid-scripts/bunfig.toml
        echo "Copied bunfig.toml to mermaid-scripts directory"
      else
        echo "? No .npmrc file found - authentication setup failed!"
        exit 1
      fi
    displayName: 'Setup Bun Authentication'
    env:
      FEED_PAT_READ: $(FEED_PAT_READ)

  # Verify authentication setup
  - script: |
      echo "Verifying authentication setup..."
      echo "Root directory bunfig.toml:"
      cat bunfig.toml || echo "No bunfig.toml in root"
      echo ""
      echo "mermaid-scripts bunfig.toml:"
      cat mermaid-scripts/bunfig.toml || echo "No bunfig.toml in mermaid-scripts"
      echo ""
      echo "Root .npmrc:"
      cat .npmrc || echo "No .npmrc in root"
    displayName: 'Verify Authentication Files'
```

#### **2. Enhanced Dependencies Installation**

Update your `install-dependencies.yml`:

```yaml
# pipelines/steps/install-dependencies.yml
steps:
  - script: |
      echo "=== Installing Root Dependencies ==="
      echo "Current directory: $(pwd)"
      echo "Contents:"
      ls -la
      
      # Clean any existing lock files
      rm -f bun.lockb package-lock.json yarn.lock
      
      # Install with verbose output for debugging
      echo "Installing dependencies with Bun..."
      bun install --verbose --frozen-lockfile=false
      
      echo "? Root dependencies installed"
    displayName: 'Install Root Dependencies (Bun)'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

  - script: |
      echo "=== Installing mermaid-scripts Dependencies ==="
      cd mermaid-scripts
      echo "Current directory: $(pwd)"
      echo "Contents:"
      ls -la
      
      # Ensure bunfig.toml exists
      if [ ! -f "bunfig.toml" ]; then
        echo "Copying bunfig.toml from parent directory..."
        cp ../bunfig.toml . || echo "Could not copy bunfig.toml"
      fi
      
      # Clean any existing lock files
      rm -f bun.lockb package-lock.json yarn.lock
      
      # Install dependencies with verbose output
      echo "Installing mermaid-scripts dependencies..."
      bun install --verbose --frozen-lockfile=false
      
      echo "? mermaid-scripts dependencies installed"
    displayName: 'Install mermaid-scripts Dependencies (Bun)'
    workingDirectory: '$(System.DefaultWorkingDirectory)'
```

#### **3. Fix the Structure Analysis Step**

Update your `analyze-structure.yml`:

```yaml
# pipelines/steps/analyze-structure.yml
steps:
  - script: |
      echo "=== Running Structure Analysis ==="
      echo "Current directory: $(pwd)"
      echo "Available scripts in root package.json:"
      cat package.json | grep -A 10 '"scripts"'
      echo ""
      
      # Check if mermaid-scripts has the required script
      echo "Available scripts in mermaid-scripts/package.json:"
      cat mermaid-scripts/package.json | grep -A 10 '"scripts"' || echo "No scripts found"
      echo ""
      
      # Try different approaches to run the script
      echo "Attempting to run structure analysis..."
      
      # Method 1: Use the root script if available
      if grep -q '"map:sonnet:hide"' package.json; then
        echo "?? Running via root package.json script..."
        bun run map:sonnet:hide
      # Method 2: Run directly from mermaid-scripts
      elif [ -f "mermaid-scripts/scripts/cli.ts" ]; then
        echo "?? Running directly from mermaid-scripts..."
        cd mermaid-scripts
        bun run scripts/cli.ts map:sonnet . --show-all-with-hide-list
        cd ..
      # Method 3: Run from root targeting mermaid-scripts
      elif [ -f "mermaid-scripts/scripts/cli.js" ]; then
        echo "?? Running JavaScript version..."
        cd mermaid-scripts  
        bun run scripts/cli.js map:sonnet . --show-all-with-hide-list
        cd ..
      else
        echo "? Could not find CLI script to run structure analysis"
        echo "Available files in mermaid-scripts/scripts/:"
        ls -la mermaid-scripts/scripts/ || echo "Scripts directory not found"
        exit 1
      fi
      
      # Find the generated structure file
      echo "Looking for generated structure files..."
      find . -maxdepth 2 -name "*structure*.md" -type f -exec ls -la {} \;
      
      STRUCTURE_FILE=$(find . -maxdepth 2 -name "*structure*.md" -type f | head -1)
      
      if [ -z "$STRUCTURE_FILE" ]; then
        echo "? No structure file generated!"
        echo "Files in current directory:"
        ls -la
        echo "Files in mermaid-scripts directory:"
        ls -la mermaid-scripts/ || true
        exit 1
      fi
      
      echo "? Found structure file: $STRUCTURE_FILE"
      
      # Clean the path and set variables
      CLEAN_FILE_PATH=${STRUCTURE_FILE#./}
      FILENAME=$(basename "$STRUCTURE_FILE")
      
      echo "##vso[task.setvariable variable=StructureFileName]$FILENAME"
      echo "##vso[task.setvariable variable=StructureFilePath]$CLEAN_FILE_PATH"
      
      # Show preview
      echo "--- Structure File Preview (30 lines) ---"
      head -30 "$STRUCTURE_FILE"
      echo "----------------------------------------"
    displayName: 'Generate Project Structure Documentation'
    workingDirectory: '$(System.DefaultWorkingDirectory)'
```

#### **4. Alternative: Remove Internal Dependencies Temporarily**

If the authentication still doesn't work, create a version of your CLI script without the internal dependencies:

```typescript
// mermaid-scripts/scripts/cli-standalone.ts
// Standalone version without internal dependencies for CI/CD

import { readdirSync, statSync, writeFileSync } from 'fs';
import { join, relative } from 'path';

// Simple interfaces instead of importing from shared packages
interface ProjectInfo {
  name: string;
  version?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

// Your existing CLI logic but without the shared package imports
// ... rest of your CLI implementation
```

Then modify your package.json script:

```json
{
  "scripts": {
    "map:sonnet:hide:standalone": "bun run mermaid-scripts/scripts/cli-standalone.ts map:sonnet . --show-all-with-hide-list"
  }
}
```

## ?? Option 2: Improvements & Recommendations

### **1. Use Azure DevOps Template Library**

Create pipeline templates in a separate repository or use Azure DevOps Library:

#### **A. Create a Templates Repository**

```yaml
# Create: pipeline-templates/.azuredevops/templates/utility-build.yml
parameters:
  - name: buildConfiguration
    type: string
    default: 'Release'
  - name: recipientEmails
    type: string
  - name: enableEmailNotification
    type: boolean
    default: true

stages:
  - stage: Build
    displayName: 'Build and Analyze'
    jobs:
      - job: AnalyzeAndBuild
        displayName: 'Analyze and Build'
        pool:
          name: 'Unraid'
        steps:
          - template: steps/setup-environment.yml
          - template: steps/install-dependencies.yml
          - template: steps/analyze-structure.yml
          - template: steps/generate-reports.yml
          
          - ${{ if eq(parameters.enableEmailNotification, true) }}:
            - template: steps/send-documentation-email.yml
              parameters:
                recipientEmails: ${{ parameters.recipientEmails }}
                markdownFilePath: $(StructureFilePath)
```

#### **B. Use the Template in Your Pipeline**

```yaml
# azure-pipelines.yml (simplified)
trigger:
  - master

pr:
  branches:
    include:
      - master

resources:
  repositories:
    - repository: templates
      type: git
      name: Introspection/pipeline-templates
      ref: refs/heads/main

variables:
  - group: SharedFeedCredentials
  - name: RecipientEmails
    value: '<EMAIL>; <EMAIL>'

extends:
  template: .azuredevops/templates/utility-build.yml@templates
  parameters:
    recipientEmails: $(RecipientEmails)
    enableEmailNotification: true
```

### **2. Enhanced Dependency Management**

#### **A. Create a Dependency Management Script**

```typescript
// scripts/manage-dependencies.ts
import { execSync } from 'child_process';

interface DependencyConfig {
  internal: string[];
  external: string[];
}

const config: DependencyConfig = {
  internal: [
    'excelytics.shared-internals',
    'excelytics.shared-models'
  ],
  external: [
    '@resvg/resvg-js',
    'mermaid',
    'pdf-lib'
  ]
};

async function installDependencies(environment: 'ci' | 'local') {
  if (environment === 'ci') {
    console.log('Installing for CI environment...');
    // Skip internal dependencies or use mocked versions
    await installExternalOnly();
  } else {
    console.log('Installing for local development...');
    await installAll();
  }
}

// Usage: bun run scripts/manage-dependencies.ts --env ci
```

#### **B. Environment-Specific Package.json Scripts**

```json
{
  "scripts": {
    "install:ci": "bun run scripts/manage-dependencies.ts --env ci",
    "install:local": "bun run scripts/manage-dependencies.ts --env local",
    "map:sonnet:hide": "bun run mermaid-scripts/scripts/cli.ts map:sonnet . --show-all-with-hide-list",
    "map:sonnet:hide:ci": "bun run mermaid-scripts/scripts/cli-standalone.ts map:sonnet . --show-all-with-hide-list"
  }
}
```

### **3. Azure DevOps Variable Groups & Library**

#### **A. Create Structured Variable Groups**

```yaml
# Variable Groups in Azure DevOps Library:

# Group: "Introspection-CI-Config"
Variables:
  - BuildConfiguration: 'Release'
  - NodeVersion: '22.11.0'
  - BunVersion: 'latest'

# Group: "Introspection-Notifications" 
Variables:
  - DefaultRecipients: '<EMAIL>; <EMAIL>'
  - SmtpServer: 'smtp.sendgrid.net'
  - SmtpPort: '587'

# Group: "Introspection-Artifacts-Auth" (Secure)
Variables:
  - FEED_PAT_READ: '$(secret-value)'
  - SmtpUser: '$(secret-value)'
  - SmtpPassword: '$(secret-value)'
```

#### **B. Use Variable Groups in Pipeline**

```yaml
variables:
  - group: Introspection-CI-Config
  - group: Introspection-Notifications  
  - group: Introspection-Artifacts-Auth
  - name: ProjectType
    value: 'utility'
```

### **4. Improved Pipeline Structure**

#### **A. Multi-Stage Pipeline with Environments**

```yaml
# azure-pipelines-full.yml
trigger:
  - master
  - develop

pr:
  branches:
    include:
      - master
      - develop

variables:
  - group: Introspection-CI-Config
  - group: Introspection-Notifications

stages:
  - stage: Validate
    displayName: 'Validation'
    jobs:
      - job: LintAndValidate
        steps:
          - template: templates/setup-environment.yml
          - template: templates/install-dependencies.yml
          - template: templates/lint-code.yml

  - stage: Build
    displayName: 'Build & Analyze'
    dependsOn: Validate
    jobs:
      - job: BuildAndAnalysis
        steps:
          - template: templates/setup-environment.yml
          - template: templates/install-dependencies.yml
          - template: templates/analyze-structure.yml
          - template: templates/generate-documentation.yml

  - stage: Notify
    displayName: 'Notifications'
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
    jobs:
      - deployment: SendNotifications
        environment: 'utility-notifications'
        strategy:
          runOnce:
            deploy:
              steps:
                - template: templates/send-documentation-email.yml
```

### **5. CI/CD Best Practices Implementation**

#### **A. Caching Strategy**

```yaml
# templates/setup-cache.yml
steps:
  - task: Cache@2
    displayName: 'Cache Bun Dependencies'
    inputs:
      key: 'bun | "$(Agent.OS)" | **/bun.lockb'
      restoreKeys: |
        bun | "$(Agent.OS)"
        bun
      path: '$(Pipeline.Workspace)/.bun-cache'
      
  - task: Cache@2  
    displayName: 'Cache Node Modules'
    inputs:
      key: 'node_modules | "$(Agent.OS)" | **/package.json'
      restoreKeys: |
        node_modules | "$(Agent.OS)"
      path: '**/node_modules'
```

#### **B. Conditional Execution**

```yaml
# templates/conditional-steps.yml
parameters:
  - name: runOnBranches
    type: object
    default: ['master', 'main']
  - name: skipOnPR
    type: boolean
    default: false

steps:
  - ${{ if and(in(variables['Build.SourceBranchName'], parameters.runOnBranches), not(and(parameters.skipOnPR, eq(variables['Build.Reason'], 'PullRequest')))) }}:
    - script: echo "Running conditional step"
      displayName: 'Conditional Execution'
```

### **6. Monitoring & Observability**

#### **A. Enhanced Logging Template**

```yaml
# templates/enhanced-logging.yml
steps:
  - script: |
      echo "=== BUILD CONTEXT ==="
      echo "Build ID: $(Build.BuildId)"
      echo "Build Number: $(Build.BuildNumber)"
      echo "Source Branch: $(Build.SourceBranchName)"
      echo "Reason: $(Build.Reason)"
      echo "Agent: $(Agent.Name)"
      echo "OS: $(Agent.OS)"
      echo "========================"
    displayName: 'Log Build Context'
    
  - script: |
      echo "=== ENVIRONMENT INFO ==="
      bun --version || echo "Bun not available"
      node --version || echo "Node not available"  
      npm --version || echo "NPM not available"
      echo "========================"
    displayName: 'Log Environment Versions'
```

## ?? **Implementation Priority**

1. **Immediate (Fix current issue):**
    - Implement the authentication fix
    - Create standalone CLI version
    - Update pipeline steps

2. **Short-term (1-2 weeks):**
    - Set up variable groups
    - Create pipeline templates repository
    - Implement caching

3. **Medium-term (1 month):**
    - Multi-stage pipelines
    - Environment-specific configurations
    - Enhanced monitoring

4. **Long-term (Ongoing):**
    - Advanced dependency management
    - Cross-repository template sharing
    - Automated pipeline optimization

This comprehensive solution should resolve your immediate issue while providing a robust foundation for scaling your CI/CD processes across all your Introspection microservices.