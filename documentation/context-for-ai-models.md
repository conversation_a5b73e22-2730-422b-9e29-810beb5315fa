> Added on 15.06.2025

/**
* This file augments the Express types to include our custom
* authentication and authorization logic.
* @module http.types
  */

// Import the payload type we want to attach to the request.
import type { AccessTokenPayload } from '../types';
// Import the original types from Express that we want to use or extend.
import type {
Response as ExpressResponse,
Request as ExpressRequest,
NextFunction
} from 'express';

/**
* An augmented version of the Express Request object that includes the authenticated user's payload.
* Specific to introspection api architecture
  */
  export interface AuthenticatedRequest extends ExpressRequest {
  /**
    * The decoded payload of the JWT access token.
    * This property is attached by the `authenticateRequest` middleware.
    * It will be undefined on unauthenticated or public routes.
      */
      user?: AccessTokenPayload;
      }

/**
* Re-export the types for convenience. We create a simple alias named "Request"
* 		so that consuming services can import it easily.
*/
export type Request = AuthenticatedRequest;
export type Response = ExpressResponse;
export { type NextFunction };

---

/**
* Auth-related types.
* Used for token payloads and related structures across microservices.
* @module token.types
  */
  import { Enum<PERSON>lientOrig<PERSON>, Enum<PERSON>lientPath, EnumTokenType } from '../../enums';

/**
* Base interface for token payloads, containing common fields.
  */
  export interface BaseTokenPayload {
  userId: string;
  email: string;
  clientId: string;
  clientOrigin: EnumClientOrigin;
  clientPath: EnumClientPath;
  isActive?: boolean;
  }

/**
* Interface for access token payload, used for API authentication and authorization.
* Typically, has a short lifespan (15-60 minutes).
* @permissions An array of strings representing the permissions granted to the token.
* examples: ['read:files', 'write:calculations', 'admin:users']
  */
  export interface AccessTokenPayload extends BaseTokenPayload {
  tokenType: EnumTokenType.ACCESS;
  permissions?: string[];
  issuedAt: Date;
  expiresAt?: Date;
  }

/**
* Interface for refresh token payload, used to obtain new access tokens.
* Typically, has a longer lifespan (7-30 days).
  */
  export interface RefreshTokenPayload extends BaseTokenPayload {
  tokenType: EnumTokenType.REFRESH;
  issuedAt: Date;
  expiresAt?: Date;
  }

/** Union type representing any valid token payload. */
export type TokenPayload = AccessTokenPayload | RefreshTokenPayload;

/** Input type for token generation, omitting 'isActive' from BaseTokenPayload. */
export type TokenGenerationInput = Omit<BaseTokenPayload, 'isActive'> & {
permissions?: string[];
isActive?: boolean;
};

---

/**
* Cache key definitions for Redis caching across Excelytics microservices.
* @module cache.constants
  */

/**
* Standardized cache key generators for various entity types.
* Ensures consistent cache key formats across all services.
  */
  export const CacheKeys = {
  /** Cache key for user profile data */
  USER_PROFILE: (userId: string) => `user:profile:${userId}`,

  /** Cache key for file processing status */
  FILE_PROCESSING: (fileId: string) => `file:processing:${fileId}`,

  /** Cache key for calculation results */
  CALCULATION_RESULT: (fileId: string) => `calc:result:${fileId}`,

  /** Cache key for model entities */
  MODEL_ENTITY: (modelName: string) => `models/${modelName}`
  } as const;

---

/**
* Standardized error codes used across all Excelytics microservices.
* Ensures consistent error handling and client responses.
* @module error.constants
  */

/**
* Comprehensive collection of error codes organized by category.
* Each code maps to a specific error condition and typically corresponds to an HTTP status code.
  */
  export const ErrorCodes = {
  // General Errors
  /** Unknown or unclassified error */
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  /** Service temporarily unavailable (503) */
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  /** Internal server error (500) */
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',

  // Input & Request Errors
  /** Resource not found (404) */
  NOT_FOUND: 'NOT_FOUND',
  /** Malformed request (400) */
  BAD_REQUEST: 'BAD_REQUEST',
  /** Unexpected error during processing */
  UNEXPECTED_ERROR: 'UNEXPECTED_ERROR',
  /** Request payload too large (413) */
  PAYLOAD_TOO_LARGE: 'PAYLOAD_TOO_LARGE',
  /** Required parameter missing */
  MISSING_PARAMETER: 'MISSING_PARAMETER',
  /** Parameter has invalid value or format */
  INVALID_PARAMETER: 'INVALID_PARAMETER',
  /** HTTP method not allowed for resource (405) */
  METHOD_NOT_ALLOWED: 'METHOD_NOT_ALLOWED',
  /** Request semantically incorrect (422) */
  UNPROCESSABLE_ENTITY: 'UNPROCESSABLE_ENTITY',
  /** Request body invalid or malformed */
  INVALID_REQUEST_BODY: 'INVALID_REQUEST_BODY',
  /** Media type not supported (415) */
  UNSUPPORTED_MEDIA_TYPE: 'UNSUPPORTED_MEDIA_TYPE',

  // CRUD Errors
  /** Data retrieval operation failed */
  FETCH_FAILED: 'FETCH_FAILED',
  /** Data update operation failed */
  UPDATE_FAILED: 'UPDATE_FAILED',
  /** Data deletion operation failed */
  DELETE_FAILED: 'DELETE_FAILED',

  // Authentication & Authorization Errors
  /** Authenticated but not authorized (403) */
  FORBIDDEN: 'FORBIDDEN',
  /** Authentication required (401) */
  UNAUTHORIZED: 'UNAUTHORIZED',
  /** Authentication token expired */
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  /** Authentication token invalid */
  INVALID_TOKEN: 'INVALID_TOKEN',
  /** User account locked due to security policy */
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  /** User not authenticated */
  UNAUTHENTICATED: 'UNAUTHENTICATED',
  /** User account disabled */
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  /** Cross-origin request not allowed */
  CORS_NOT_ALLOWED: 'CORS_NOT_ALLOWED',
  /** Permission denied for requested operation */
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  /** Required permission missing */
  MISSING_PERMISSION: 'MISSING_PERMISSION',
  /** Login credentials invalid */
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  /** User registration failed - user exists */
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  /** User registration failed */
  REGISTRATION_FAILED: 'REGISTRATION_FAILED',
  /** Authentication process failed */
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  /** Unknown error during registration */
  UNKNOWN_REGISTRATION_ERROR: 'UNKNOWN_REGISTRATION_ERROR',
  /** Session regeneration failed */
  SESSION_REGENERATION_FAILED: 'SESSION_REGENERATION_FAILED',

  // Validation Errors
  /** Input validation failed (400 or 422) */
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  /** Login form validation failed */
  LOGIN_VALIDATION_ERROR: 'LOGIN_VALIDATION_ERROR',
  /** Registration form validation failed */
  REGISTRATION_VALIDATION_ERROR: 'REGISTRATION_VALIDATION_ERROR',

  // Token Verification Errors
  /** Token introspection failed */
  INTROSPECTION_FAILED: 'INTROSPECTION_FAILED',
  /** Token refresh operation failed */
  TOKEN_REFRESH_FAILED: 'TOKEN_REFRESH_FAILED',
  /** Refresh token invalid */
  INVALID_REFRESH_TOKEN: 'INVALID_REFRESH_TOKEN',
  /** Error processing token */
  TOKEN_PROCESSING_ERROR: 'TOKEN_PROCESSING_ERROR',
  /** Error generating token */
  TOKEN_GENERATION_ERROR: 'TOKEN_GENERATION_ERROR',
  /** Token verification failed */
  TOKEN_VERIFICATION_FAILED: 'TOKEN_VERIFICATION_FAILED',

  // Resource & State Errors
  /** Resource state conflict (409) */
  CONFLICT: 'CONFLICT',
  /** Precondition for operation failed (412) */
  PRECONDITION_FAILED: 'PRECONDITION_FAILED',
  /** Operation not allowed by business rules */
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  /** Resource already exists */
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',

  // Rate Limiting
  /** Too many requests (429) */
  TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',
  /** Rate limit exceeded */
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  /** Specific action rate limited */
  ACTION_RATE_LIMITED: 'ACTION_RATE_LIMITED',
  /** Authentication rate limit exceeded */
  AUTH_RATE_LIMIT_EXCEEDED: 'AUTH_RATE_LIMIT_EXCEEDED',
  /** Calculation service rate limit exceeded */
  CALC_RATE_LIMIT_EXCEEDED: 'CALC_RATE_LIMIT_EXCEEDED',
  /** File upload rate limit exceeded */
  UPLOAD_RATE_LIMIT_EXCEEDED: 'UPLOAD_RATE_LIMIT_EXCEEDED',
  /** Calculation requests rate limit exceeded */
  CALCULATION_RATE_LIMIT_EXCEEDED: 'CALCULATION_RATE_LIMIT_EXCEEDED',
  /** Password reset rate limit exceeded */
  PASSWORD_RESET_RATE_LIMIT_EXCEEDED: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
  /** Heavy processing rate limit exceeded */
  HEAVY_PROCESSING_RATE_LIMIT_EXCEEDED: 'HEAVY_PROCESSING_RATE_LIMIT_EXCEEDED',

  // External Service Errors
  /** Bad gateway error (502) */
  BAD_GATEWAY: 'BAD_GATEWAY',
  /** External service error */
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  /** External service timeout */
  EXTERNAL_SERVICE_TIMEOUT: 'EXTERNAL_SERVICE_TIMEOUT',

  // Database Errors
  /** General database error */
  DATABASE_ERROR: 'DATABASE_ERROR',
  /** Duplicate key error (MongoDB 11000) */
  DUPLICATE_KEY_ERROR: 'DUPLICATE_KEY_ERROR',

  // Custom Business Logic Errors
  /** Health check failed */
  HEALTH_CHECK_FAILED: 'HEALTH_CHECK_FAILED',
  /** Insufficient funds for operation */
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  /** Item out of stock */
  ITEM_OUT_OF_STOCK: 'ITEM_OUT_OF_STOCK',
  /** User not verified */
  USER_NOT_VERIFIED: 'USER_NOT_VERIFIED'
  } as const;

---

/**
* Constants related to Node.js process handling, server operations, and database states.
* @module process.constants
  */

/**
* Common Node.js server error codes.
* Used for handling server startup and listening errors.
  */
  export const NodeServerErrors = {
  /**
    * Error code for "Permission Denied".
    * Occurs when a server attempts to listen on a port without sufficient permissions.
      */
      ACCESS_DENIED: 'EACCES',

  /**
    * Error code for "Address Already in Use".
    * Occurs when a server attempts to listen on a port that's already in use.
      */
      ADDRESS_IN_USE: 'EADDRINUSE',

  /**
    * The system call name associated with server listening errors.
    * Used for checking if an error originated from the `listen` operation.
      */
      LISTEN_SYSCALL: 'listen'
      } as const;

/**
* Process signal constants for handling graceful shutdown.
* Used in signal handlers for proper application termination.
  */
  export const ProcessSignals = {
  /**
    * Signal Interrupt (Ctrl+C in terminal).
    * Used to request process termination.
      */
      SIGNAL_INTERRUPT: 'SIGINT',

  /**
    * Signal Terminate.
    * Generic signal for process termination, often sent by process managers.
      */
      SIGNAL_TERMINATE: 'SIGTERM',

  /**
    * Custom identifier for unhandled promise rejections.
    * Used for consistent shutdown function calls.
      */
      UNHANDLED_REJECTION: 'UNHANDLED_REJECTION',

  /**
    * Custom identifier for uncaught exceptions.
    * Used for consistent shutdown function calls.
      */
      UNCAUGHT_EXCEPTION: 'UNCAUGHT_EXCEPTION'
      } as const;

/**
* Mongoose connection state codes.
* Used for database connection status monitoring.
  */
  export const MongooseStates = {
  /** Database disconnected */
  DISCONNECTED: 0,
  /** Database connected */
  CONNECTED: 1,
  /** Database connection in progress */
  CONNECTING: 2,
  /** Database disconnection in progress */
  DISCONNECTING: 3
  } as const;

/**
* Server-related constants for error handling and event management.
* Used in server lifecycle management.
  */
  export const ServerConstants = {
  /** Error code when attempting to interact with a server that's not running */
  SERVER_NOT_RUNNING: 'ERR_SERVER_NOT_RUNNING',
  /** Event name for unhandled promise rejections */
  UNHANDLED_REJECTION: 'unhandledRejection',
  /** Event name for uncaught exceptions */
  UNCAUGHT_EXCEPTION: 'uncaughtException',
  /** Event name for stream completion */
  FINISH: 'finish',
  /** Event name for errors */
  ERROR: 'error',
  /** Event name for server close */
  CLOSE: 'close',
  /** Event name for server connection */
  CONNECT: 'connect'
  } as const;

---

/**
* @module client.enum
  */

/**
* Enum representing different client origins.
* Used for tracking and managing client requests and responses.
  */
  export enum EnumClientOrigin {
  Excelytics = 1,
  API_Gateway = 2,
  Other = 3
  }

/**
* Enum representing different client paths.
* Used for routing and managing client requests and responses.
  */
  export enum EnumClientPath {
  Finance = 'excelytics.finance',   // Backend
  Identity = 'excelytics.identity', // IdP
  Client = 'excelytics.client',     // Frontend
  Calc = 'excelytics.calc',         // Calc
  Gateway = 'excelytics.gateway',   // API Gateway
  Other = 'excelytics.other'        // Other
  }

---

/**
* @module base.error
  */
  import { HttpStatus, ErrorCodes } from '../constants';

/** Interface for optional structured error details. */
export interface ErrorDetails {
[key: string]: any;
}

/** Type representing valid HTTP status codes. */
export type HttpStatusCode = (typeof HttpStatus)[keyof typeof HttpStatus];

/** Type representing valid error codes. */
export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes];

/**
* Custom base error class for standardized error handling across services.
* Includes HTTP Status Code, specific Error Code, and optional details.
  */
  export class BaseError extends Error {
  public readonly statusCode: HttpStatusCode;     // (derived type)
  public readonly errorCode: ErrorCode;           // (derived type)
  public readonly details?: ErrorDetails | any;   // Allow for flexible details
  public readonly originalError?: Error | any;    // To store the original caught error for logging

  /**
    * @param message Human-readable error message
    * @param statusCode HTTP status code associated with this error
    * @param errorCode Specific error code string from ErrorCodes
    * @param details Optional structured details about the error
    * @param originalError Optional original error that was caught
      */
      constructor(
      message: string,                    // The human-readable error message
      statusCode: HttpStatusCode,         // The HTTP status code associated with this error (derived type)
      errorCode: ErrorCode,               // A specific error code string (from ErrorCodes)  (derived type)
      details?: ErrorDetails | any,       // Optional structured details about the error
      originalError?: Error | any         // Optional original error that was caught
      ) {
      // Call the parent Error constructor
      super(message);

      this.name = this.constructor.name;  // Set the error name to the class name
      this.statusCode = statusCode;
      this.errorCode = errorCode;
      this.details = details;
      this.originalError = originalError;

      // This line is important for proper stack trace in V8 environments (Node.js, Chrome)
      if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
      }
      }

  /**
    * Converts this BaseError instance into the error structure expected by ApiResponse.
    * @returns An object containing the error code, message, and optional details.
      */
      public toApiResponseError(): { code: ErrorCode; message: string; details?: any } {
      return {
      code: this.errorCode,
      message: this.message,
      details: this.details
      };
      }
      }

---

/**
 * @module http.error
 */
import { BaseError, type ErrorCode, type ErrorDetails } from './base.error';
import { HttpStatus, ErrorCodes } from '../constants';

// --- 4xx Client Errors ---

/**
 * Use when the server cannot or will not process the request due to something that is perceived to be a client error
 * (e.g., malformed request syntax, invalid request message framing, or deceptive request routing).
 * Ideal for Zod validation failures.
 */
export class BadRequestError extends BaseError {
	constructor(
		message: string = 'Bad Request',
		errorCode: ErrorCode = ErrorCodes.BAD_REQUEST, // 404
		details?: ErrorDetails,
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.BAD_REQUEST,
			errorCode,
			details,
			originalError
		);

		this.name = 'BadRequestError';
	}
}

/**
 * Use when authentication is required and has failed or has not yet
 * been provided. The user is not authenticated.
 */
export class UnauthorizedError extends BaseError {
	constructor(
		message: string = 'Authentication required or failed',
		errorCode: ErrorCode = ErrorCodes.UNAUTHORIZED, // 401
		originalError?: unknown
	) {
		super(
			message,
			HttpStatus.UNAUTHORIZED,
			errorCode,
			undefined, // No specific details by default
			originalError
		);

		this.name = 'UnauthorizedError';
	}
}

/**
 * Use when the server understands the request but refuses to authorize it.
 * The user is authenticated, but they do not have permission for the action.
 */
export class ForbiddenError extends BaseError {
	constructor(
		message: string = 'Access to this resource is forbidden',
		errorCode: ErrorCode = ErrorCodes.FORBIDDEN, // 403
		originalError?: unknown
	) {
		super(
			message,
			HttpStatus.FORBIDDEN,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'ForbiddenError';
	}
}

/** Use when the server cannot find the requested resource. */
export class NotFoundError extends BaseError {
	constructor(
		message: string = 'Resource not found',
		resourceName?: string
	) {
		const details: ErrorDetails | undefined = resourceName
			? { resource: resourceName }
			: undefined;

		super(
			message,
			HttpStatus.NOT_FOUND, // 404
			ErrorCodes.NOT_FOUND,
			details
		);

		this.name = 'NotFoundError';
	}
}

/**
 * Use when a request conflicts with the current state of the server.
 * Common use case: Trying to create a resource that already exists (e.g., registering a user with an email that is already taken).
 */
export class ConflictError extends BaseError {
	constructor(
		message: string = 'Resource conflict',
		errorCode: ErrorCode = ErrorCodes.CONFLICT, // 409
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.CONFLICT,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'ConflictError';
	}
}

/**
 * Use when the server understands the content type of the request entity, and the syntax of the request entity is correct,
 * but it was unable to process the contained instructions.
 * Example: An Excel file is uploaded and parsed correctly, but it's missing a required "Revenue" column for a specific calculation.
 */
export class UnprocessableEntityError extends BaseError {
	constructor(
		message: string = 'Unprocessable Entity',
		errorCode: ErrorCode = ErrorCodes.UNPROCESSABLE_ENTITY, // 422
		details?: ErrorDetails,
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.UNPROCESSABLE_ENTITY,
			errorCode,
			details,
			originalError,
		);

		this.name = 'UnprocessableEntityError';
	}
}

// --- 5xx Server Errors ---

/**
 * Use when one server on the internet received an invalid response from another server.
 * Perfect for our microservices: `Introspection.Finance` would throw this if `Introspection.Calc` returns an unexpected error or fails to respond.
 */
export class BadGatewayError extends BaseError {
	constructor(
		message: string = 'Bad Gateway',
		errorCode: ErrorCode = ErrorCodes.BAD_GATEWAY, // 502
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.BAD_GATEWAY,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'BadGatewayError';
	}
}

/**
 * Use when the server is not ready to handle the request. Common causes are a server that is down for maintenance or that is overloaded.
 * Perfect for when a critical dependency like your MongoDB or Redis instance on Unraid is unreachable.
 */
export class ServiceUnavailableError extends BaseError {
	constructor(
		message: string = 'Service Unavailable',
		errorCode: ErrorCode = ErrorCodes.SERVICE_UNAVAILABLE, // 503
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.SERVICE_UNAVAILABLE,
			errorCode,
			undefined,
			originalError,
		);

		this.name = 'ServiceUnavailableError';
	}
}

---

/**
 * @module service.error
 */
import { BaseError, type ErrorCode, type HttpStatusCode } from './base.error';
import { ErrorCodes, HttpStatus } from '../constants';

/**
 * @class ServiceError
 * @extends BaseError
 * @description A custom error for failures in inter-service communication.
 * It extends BaseError to be handled consistently by the GlobalErrorHandler.
 */
export class ServiceError extends BaseError {
	constructor(
		message: string,
		statusCode: HttpStatusCode = HttpStatus.SERVICE_UNAVAILABLE, // 503 is a good default
		errorCode: ErrorCode = ErrorCodes.EXTERNAL_SERVICE_ERROR,
		details?: any,
		originalError?: Error | any
	) {
		super(message, statusCode, errorCode, details, originalError);
		this.name = 'ServiceError';
	}
}

---

/**
 * @module validation.error
 */
import { HttpStatus, ErrorCodes } from '../constants';
import { BaseError, type ErrorDetails } from './base.error';

/**
 * Custom error for validation failures.
 */
export class ValidationError extends BaseError {
	constructor(
		message: string = 'Validation failed',
		details?: ErrorDetails | any,
		originalZodError?: any
	) {
		super(
			message,
			HttpStatus.BAD_REQUEST,     // 400
			ErrorCodes.VALIDATION_ERROR,
			details,
			originalZodError
		);
		this.name = 'ValidationError';
	}
}

---


import type { DocumentType } from '@typegoose/typegoose';
import type { UpdateQuery, FilterQuery } from 'mongoose';

/**
 * Defines the shape for query options used in 'get' operations,
 * allowing for filtering, projection, sorting, and pagination.
 */
export interface GetQueryOptions<T> {
    query?: FilterQuery<T>;
    projection?: any;
    sort?: Record<string, 1 | -1>;
    limit?: number;
    skip?: number;
}

/**
 * Defines the contract for a Redis service.
 * This allows the RepositoryService to depend on an abstraction, not a
 * concrete implementation, making it more testable and flexible.
 */
export interface IRedisService {
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    exists(key: string): Promise<boolean>;
    setCache(key: string, value: any, ttl?: number): Promise<void>;
    getCache<T>(key: string): Promise<T | null>;
    deleteCache(key: string): Promise<void>;
}

/**
 * Defines the contract for a generic data repository.
 * This is the core interface for all direct database and cache interactions.
 * It consistently returns Mongoose DocumentTypes for single-entity operations
 * to allow for further processing (like auditing) by the calling service.
 */
export interface IRepositoryService<T> {
    /** Creates a new document in the database. */
    create(entity: Partial<T>): Promise<DocumentType<T>>;

    /** Retrieves a list of plain objects based on query options. */
    get(options: GetQueryOptions<T>): Promise<T[]>;

    /** Finds multiple documents matching a filter, returning full Mongoose documents. */
    find(filter: FilterQuery<T>): Promise<DocumentType<T>[]>;

    /** Retrieves a single full Mongoose document by its ID, or null if not found. */
    findById(id: string): Promise<DocumentType<T> | null>;

    /** Checks if a document with the given ID exists. */
    existsById(id: string): Promise<boolean>;

    /** Finds and updates a document by its ID, returning the updated Mongoose document. */
    update(id: string, entity: UpdateQuery<T>): Promise<DocumentType<T>>;

    /** Finds and deletes a document by its ID. */
    delete(id: string): Promise<void>;

    /** Counts the number of documents matching a filter. */
    count(filter?: FilterQuery<T>): Promise<number>;
}

/**
 * Defines the contract for the business logic layer (e.g., MutatorService).
 * This is the public-facing API for your controllers. It handles actors, orchestrates validation, and uses an IRepositoryService for data operations.
 * It returns plain JavaScript objects (`T`), not Mongoose documents, as the final response to the presentation layer should be clean data.
 * @entity: The entity to be actioned
 * @actorId: The Id of the user performing the action
 */
export interface IMutatorService<T> {
    /** Validates, creates, and audits a new entity. */
    create(entity: T, actorId: string): Promise<T>;

    /** Retrieves a list of entities. */
    get(options: GetQueryOptions<T>): Promise<T[]>;

    /** Validates, updates, and audits an existing entity. */
    update(id: string, entity: UpdateQuery<T>, actorId: string): Promise<T>;

    /** Validates, deletes, and audits an existing entity. */
    delete(id: string, actorId: string): Promise<void>;

    /** Counts entities matching a filter. */
    count(filter?: FilterQuery<T>): Promise<number>;
}

---

/**
 * Standardized API error response handling for Excelytics microservices.
 * Provides consistent error formatting across all services that consume this shared package.
 * @module api-error.notifications
 */
import { BaseError, type ErrorDetails } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';
import type { ErrorResponse } from '../types';
import { environments } from '../utils';
import type { Response } from '../config';
import { EnumEnv } from '../enums';
import { ZodError } from 'zod';

/**
 * Handles formatting and sending standardized error responses for REST APIs.
 * Supports various error types including BaseError, ZodError, and generic errors.
 */
export class CustomErrorResponse {
	/**
	 * Sends a generic error response based on error type
	 * @param response - Express response object
	 * @param error - Error to process (BaseError, Error, or any)
	 * @param defaultMessage - Fallback message if error doesn't provide one
	 * @param defaultStatusCode - HTTP status code to use if not a BaseError
	 * @param defaultErrorCode - Error code to use if not a BaseError
	 * @returns Express response with formatted error
	 */
	public SendError(
		response: Response,
		error?: BaseError | Error | any,
		defaultMessage: string = 'An unexpected error occurred',
		defaultStatusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
		defaultErrorCode: string = ErrorCodes.UNKNOWN_ERROR
	): Response {
		if (error instanceof BaseError) {
			const apiErrorResponse: ErrorResponse = {
				success: false,
				message: error.message,
				error: error.toApiResponseError()
			};

			return response.status(error.statusCode).json(apiErrorResponse);
		}

		// Log the unknown error for debugging if it's not a BaseError
		console.error('[CustomErrorResponse] Handling non-BaseError:', error);

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: error instanceof Error ? error.message : defaultMessage,
			error: {
				code: defaultErrorCode,
				message: error instanceof Error ? error.message : defaultMessage,
				details: error instanceof Error ? { name: error.name, stack: error.stack } : error
			}
		};

		return response.status(defaultStatusCode).json(apiErrorResponse);
	}

	/**
	 * Sends a 401 Unauthorized error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 401 status
	 */
	SendUnauthorizedError(
		response: Response,
		message: string = 'Authentication required or action not allowed'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.UNAUTHORIZED, message }
		};

		return response.status(HttpStatus.UNAUTHORIZED).json(apiErrorResponse);
	}

	/**
	 * Sends a 403 Forbidden error response
	 * @param res - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 403 status
	 */
	public SendForbiddenError(
		res: Response,
		message: string = 'Access to this resource is forbidden'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.FORBIDDEN, message: message }
		};

		return res.status(HttpStatus.FORBIDDEN).json(apiErrorResponse);
	}

	/**
	 * Sends a 500 Internal Server Error response
	 * @param response - Express response object
	 * @param originalError - Original error that caused the server error
	 * @param message - Custom error message
	 * @returns Express response with 500 status
	 */
	SendInternalServerError(
		response: Response,
		originalError?: any,
		message: string = 'An internal servererror occurred'
	): Response {
		if (originalError) {
			console.error('[ServerError]', message, originalError);
		} else {
			console.error('[ServerError]', message);
		}

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: {
				code: ErrorCodes.INTERNAL_SERVER_ERROR,
				message: message,
				// Avoid sending full error details to client in prod for server errors
				details: environments.ENV === EnumEnv.Development && originalError
					? { name: originalError.name, message: originalError.message } : undefined
			}
		};

		return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(apiErrorResponse);
	}

	/**
	 * Sends a 400 Bad Request error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @param details - Additional error details
	 * @returns Express response with 400 status
	 */
	SendBadRequestError(
		response: Response,
		message: string = 'The request could not be understood or was missing required parameters',
		details?: ErrorDetails | any,
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.BAD_REQUEST, message: message, details: details }
		};

		return response.status(HttpStatus.BAD_REQUEST).json(apiErrorResponse);
	}

	/**
	 * Sends a 404 Not Found error response
	 * @param response - Express response object
	 * @param message - Custom error message
	 * @returns Express response with 404 status
	 */
	public SendNotFoundError(
		response: Response,
		message: string = 'The requested resource was not found'
	): Response {
		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: { code: ErrorCodes.NOT_FOUND, message: message }
		};

		return response.status(HttpStatus.NOT_FOUND).json(apiErrorResponse);
	}

	/**
	 * Handles Zod validation errors with detailed formatting
	 * @param response - Express response object
	 * @param zodError - Zod validation error
	 * @param message - Custom error message
	 * @param errorCode - Specific error code for this validation error
	 * @returns Express response with 400 status and formatted validation errors
	 */
	public SendValidationError(
		response: Response,
		zodError: ZodError,
		message: string = 'Input validation failed',
		errorCode: string = ErrorCodes.VALIDATION_ERROR
	): Response {
		console.warn(`[ValidationError] ${message}`, zodError.format());

		const apiErrorResponse: ErrorResponse = {
			success: false,
			message: message,
			error: {
				code: errorCode,
				message: 'One or more fields failed validation',
				details: zodError.format() // Zod' structured errors
			}
		};

		// Or HttpStatus.UNPROCESSABLE_ENTITY (422)
		return response.status(HttpStatus.BAD_REQUEST).json(apiErrorResponse);
	}

	/**
	 * Handles registration-specific validation errors
	 * @param response - Express response object
	 * @param error - Zod validation error
	 * @returns Express response with formatted validation errors
	 */
	SendRegistrationValidationError(
		response: Response,
		error: ZodError
	): Response {
		return this.SendValidationError(
			response,
			error,
			'Registration failed due to validation errors',
			ErrorCodes.REGISTRATION_VALIDATION_ERROR
		);
	}

	/**
	 * Handles login-specific validation errors
	 * @param response - Express response object
	 * @param error - Zod validation error
	 * @returns Express response with formatted validation errors
	 */
	SendLoginValidationError(
		response: Response,
		error: ZodError
	): Response {
		return this.SendValidationError(
			response,
			error,
			'Login failed due to validation errors.',
			ErrorCodes.LOGIN_VALIDATION_ERROR
		);
	}

	/**
	 * Specialized handler for registration errors including duplicate key errors
	 * @param response - Express response object
	 * @param error - Any error that occurred during registration
	 * @returns Express response with appropriate status and error details
	 */
	HandleRegistrationError(response: Response, error: any): Response {
		console.error('[HandleRegistrationError]', error);

		// Handle potential duplicate key errors (e.g. email) - MongoDB duplicate key
		if (error.code === 11000 || (error.message && error.message.includes('duplicate key'))) {
			const field = error.keyPattern ? Object.keys(error.keyPattern)[0] : 'unique field';
			const errorMessage = `An account with this ${field} already exists.`;

			const apiErrorResponse: ErrorResponse = {
				success: false,
				message: errorMessage,
				error: {
					code: ErrorCodes.DUPLICATE_KEY_ERROR,
					message: errorMessage,
					details: { field }
				}
			};

			// 409 Conflict
			return response.status(HttpStatus.CONFLICT).json(apiErrorResponse);
		}

		// If it's already a BaseError from the service (e.g., a custom registration failure)
		if (error instanceof BaseError) {
			return this.SendError(response, error);
		}

		// Fallback for other errors during registration
		return this.SendInternalServerError(
			response,
			error,
			'An unexpected error occurred during registration');
	}
}

---


/**
 * Standardized API success response handling for Excelytics microservices.
 * Provides consistent success response formatting across all services that consume this shared package.
 * @module api-success.notifications
 */
import type { Response } from '../config';
import { HttpStatus } from '../constants';
import type { SuccessResponse, TokenResponse } from '../types';

/**
 * Handles formatting and sending standardized success responses for REST APIs.
 * Supports various HTTP status codes and response types including token responses.
 */
export class CustomSuccessResponse {
	/**
     * Sends a generic success response
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Data payload to include in response
     * @param statusCode - HTTP status code (defaults to 200 OK)
     * @returns Express response with formatted success data
     */
	SendSuccessResponse<TData = any>(
		response: Response,
		message: string = 'Operation successful',
		data: TData,
		statusCode: number = HttpStatus.OK
	): Response {
		const successResponse: SuccessResponse<TData> = {
			success: true,
			message,
			data
		};

		return response.status(statusCode).json(successResponse);
	}

	/**
     * Sends a success response specifically for token-related operations
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Token data including access and refresh tokens
     * @param statusCode - HTTP status code (defaults to 200 OK)
     * @returns Express response with formatted token response
     */
	SendTokenSuccessResponse(
		response: Response,
		message: string = 'Token operation successful',
		data: TokenResponse,
		statusCode: number = HttpStatus.OK
	): Response {
		const successResponse: SuccessResponse<TokenResponse> = {
			success: true,
			message,
			data
		};

		return response.status(statusCode).json(successResponse);
	}

	/**
     * Sends a 201 Created success response indicating a resource was created
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Data payload of the created resource
     * @returns Express response with 201 status and resource data
     */
	public SendResourceCreatedResponse<TData = any>(
		response: Response,
		message: string = 'Resource created successfully',
		data: TData
	): Response {
		return this.SendSuccessResponse(response, message, data, HttpStatus.CREATED);
	}

	/**
     * Sends a 202 Accepted response indicating an operation was accepted for processing
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Optional data payload related to the accepted request
     * @returns Express response with 202 status
     */
	public SendAcceptedResponse<TData = any>(
		response: Response,
		message: string = 'Request accepted for processing',
		data?: TData // Data is optional for 202
	): Response {
		const successResponse: SuccessResponse<TData | undefined> = {
			success: true,
			message,
			data
		};

		return response.status(HttpStatus.ACCEPTED).json(successResponse);
	}

	/**
     * Sends a 204 No Content response indicating successful operation with no response body
     * @param response - Express response object
     * @returns Express response with 204 status and no content
     */
	public SendNoContentResponse(response: Response): Response {
		return response.status(HttpStatus.NO_CONTENT).send();
	}
}

---


/**
 * Token verification and decoding service.
 * @module token.service
 */
import jwt, { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { BaseError, UnauthorizedError } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';
import type { AccessTokenPayload } from '../types';
import { EnumTokenType } from '../enums';

/**
 * A service for handling JWT verification and decoding.
 * This should be instantiated in each backend service that needs to validate tokens.
 */
export class TokenService {
	private readonly jwtSecret: string;

	/**
     * Creates an instance of TokenService.
     * @param jwtSecret The secret key used to verify the JWT signature.
     * This MUST be the same secret used by the IdP to sign the token.
     */
	constructor(jwtSecret: string) {
		if (!jwtSecret) {
			throw new Error('JWT secret is required for TokenService.');
		}

		this.jwtSecret = jwtSecret;
	}

	/**
     * Verifies an access token's signature and decodes its payload.
     * It also validates that the token is specifically an ACCESS token.
     * @param token The JWT string to verify.
     * @returns The decoded AccessTokenPayload if the token is valid.
     * @throws { UnauthorizedError } if the token is expired, invalid, or not an access token.
     * @throws { BaseError } for other unexpected errors during verification.
     */
	public verifyAndDecodeAccessToken(token: string): AccessTokenPayload {
		try {
			// Verify the token using the secret
			const decoded = jwt.verify(token, this.jwtSecret) as AccessTokenPayload;

			// Security Check: Ensure this is an access token and not a refresh token
			if (decoded.tokenType !== EnumTokenType.ACCESS) {
				throw new UnauthorizedError(
					'Invalid token type provided. Expected access token.',
					ErrorCodes.INVALID_TOKEN
				);
			}

			return decoded;
		} catch (error) {
			if (error instanceof TokenExpiredError) {
				throw new UnauthorizedError(
					'Access token has expired.',
					ErrorCodes.TOKEN_EXPIRED,
					error
				);
			}

			if (error instanceof JsonWebTokenError) {
				throw new UnauthorizedError(
					'Invalid access token provided.',
					ErrorCodes.INVALID_TOKEN,
					error
				);
			}

			if (error instanceof Error) {
				throw error;
			}

			throw new BaseError(
				'An unexpected error occurred during access token verification.',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.TOKEN_VERIFICATION_FAILED,
				error
			);
		}
	}
}

---

/**
 * Database-related types.
 * Used for MongoDB document structures across microservices.
 * @module database.types
 */
import { EnumFileProcessingStatus } from '../enums';
import type { FileMetadata } from './file.types';

/**
 * Base document interface for MongoDB documents.
 * Provides common fields for all database entities.
 */
export interface BaseDocument {
    _id?: string;
    createdAt: Date;
    updatedAt: Date;
    isDeleted?: boolean;
}

/**
 * User preferences for application settings.
 * Controls UI appearance and notification delivery.
 */
export interface UserPreferences {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: NotificationSettings;
}

/**
 * Settings for different notification channels.
 * Controls how users receive system notifications.
 */
export interface NotificationSettings {
    email: boolean;
    push: boolean;
    sms: boolean;
}

/**
 * Document representing a file stored in the database.
 * Contains metadata and processing status information.
 */
export interface FileDocument extends BaseDocument {
    userId: string;
    originalName: string;
    filename: string;
    mimetype: string;
    size: number;
    status: EnumFileProcessingStatus;
    processingStartedAt?: Date;
    processingCompletedAt?: Date;
    errorMessage?: string;
    metadata: FileMetadata;
}

