# Error Types & Responders Guide - Excelytics.SharedInternals

## 📋 Overview

This document provides comprehensive documentation for all error types, response patterns, and responder utilities available in the Excelytics.SharedInternals package. These components ensure consistent error handling across all microservices.

---

## 🏗️ BaseError Class Hierarchy

### **BaseError (Abstract Parent Class)**
```typescript
abstract class BaseError extends Error {
    public readonly statusCode: HttpStatusCode;
    public readonly errorCode: ErrorCode;
    public readonly details?: ErrorDetails;
    public readonly originalError?: Error;
    
    constructor(
        message: string,
        statusCode: HttpStatusCode,
        errorCode: ErrorCode,
        details?: ErrorDetails,
        originalError?: Error
    );
    
    // Converts error to API response format
    public toApiResponseError(): ApiErrorResponse;
}
```

### **Key Features:**
- **HTTP Status Code Integration** - Proper HTTP status codes for each error type
- **Machine-Readable Error Codes** - Consistent error codes for client handling
- **Optional Details** - Additional context for debugging and client information
- **Error Chaining** - Preserves original error for debugging while providing clean client messages
- **Serialization** - Clean JSON serialization for API responses

---

## 🎯 Specific Error Types

### **1. UnauthorizedError (401)**
```typescript
class UnauthorizedError extends BaseError {
    constructor(
        message: string = 'Unauthorized access',
        errorCode: ErrorCode = ErrorCodes.UNAUTHORIZED,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);
throw new UnauthorizedError('Token expired', ErrorCodes.TOKEN_EXPIRED);
throw new UnauthorizedError('Invalid JWT token', ErrorCodes.INVALID_TOKEN);

// Common Scenarios:
- Failed login attempts
- Expired or malformed JWT tokens
- Missing authentication headers
- Invalid API keys
```

### **2. ForbiddenError (403)**
```typescript
class ForbiddenError extends BaseError {
    constructor(
        message: string = 'Access forbidden',
        errorCode: ErrorCode = ErrorCodes.FORBIDDEN,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new ForbiddenError('Insufficient permissions', ErrorCodes.INSUFFICIENT_PERMISSIONS);
throw new ForbiddenError('Admin access required', ErrorCodes.ADMIN_REQUIRED);

// Common Scenarios:
- User lacks required permissions
- Resource access denied
- Admin-only operations
- Role-based access control violations
```

### **3. NotFoundError (404)**
```typescript
class NotFoundError extends BaseError {
    constructor(
        message: string = 'Resource not found',
        errorCode: ErrorCode = ErrorCodes.NOT_FOUND,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new NotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);
throw new NotFoundError('Resource not found', ErrorCodes.RESOURCE_NOT_FOUND);

// Common Scenarios:
- User lookup failures
- Invalid resource IDs
- Deleted or non-existent resources
- Invalid API endpoints
```

### **4. ConflictError (409)**
```typescript
class ConflictError extends BaseError {
    constructor(
        message: string = 'Resource conflict',
        errorCode: ErrorCode = ErrorCodes.CONFLICT,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new ConflictError('User already exists', ErrorCodes.USER_ALREADY_EXISTS);
throw new ConflictError('Email already registered', ErrorCodes.EMAIL_ALREADY_REGISTERED);

// Common Scenarios:
- Duplicate email registration
- Unique constraint violations
- Resource state conflicts
- Concurrent modification conflicts
```

### **5. ValidationError (400)**
```typescript
class ValidationError extends BaseError {
    constructor(
        message: string = 'Validation failed',
        errorCode: ErrorCode = ErrorCodes.VALIDATION_FAILED,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new ValidationError('Invalid email format', ErrorCodes.INVALID_EMAIL_FORMAT);
throw new ValidationError('Password too weak', ErrorCodes.WEAK_PASSWORD, {
    requirements: ['8+ characters', 'uppercase', 'lowercase', 'number']
});

// Common Scenarios:
- Input validation failures
- Schema validation errors
- Business rule violations
- Format validation errors
```

### **6. InternalServerError (500)**
```typescript
class InternalServerError extends BaseError {
    constructor(
        message: string = 'Internal server error',
        errorCode: ErrorCode = ErrorCodes.INTERNAL_SERVER_ERROR,
        details?: ErrorDetails,
        originalError?: Error
    );
}

// Usage Examples:
throw new InternalServerError('Database connection failed', ErrorCodes.DATABASE_ERROR);
throw new InternalServerError('External service unavailable', ErrorCodes.EXTERNAL_SERVICE_ERROR);

// Common Scenarios:
- Database connection failures
- External service timeouts
- Unexpected system errors
- Configuration errors
```

---

## 📝 Response Patterns & Interfaces

### **Error Response Interface:**
```typescript
interface ErrorResponse {
    success: false;
    message: string;           // Human-readable error message
    error: {
        code: string;          // Machine-readable error code
        message: string;       // Detailed error message
        details?: any;         // Additional error context (optional)
    };
}
```

### **Success Response Interface:**
```typescript
interface SuccessResponse<T = any> {
    success: true;
    message: string;           // Success message
    data: T;                   // Response payload
}
```

### **Service Result Pattern:**
```typescript
interface ServiceResult<T = any> {
    success: boolean;
    data?: T;                  // Present when success = true
    error?: BaseError;         // Present when success = false
}

// Usage in Services:
public async createUser(userData: CreateUserDTO): Promise<ServiceResult<User>> {
    try {
        const user = await this.userRepository.create(userData);
        return { success: true, data: user };
    } catch (error) {
        if (error.code === 'DUPLICATE_EMAIL') {
            const conflictError = new ConflictError(
                'User with this email already exists',
                ErrorCodes.USER_ALREADY_EXISTS
            );
            return { success: false, error: conflictError };
        }
        throw error; // Let GlobalErrorHandler handle unexpected errors
    }
}
```

---

## 🛠️ Response Helper Utilities

### **CustomSuccessResponse (Legacy - Being Phased Out)**
```typescript
class CustomSuccessResponse {
    public SendSuccessResponse<T>(
        response: Response,
        message: string,
        data: T,
        statusCode: number = 200
    ): void;
    
    public SendTokenSuccessResponse(
        response: Response,
        message: string,
        tokenData: TokenResponseData
    ): void;
}

// Usage:
this.successResponder.SendSuccessResponse(response, 'User created', user, 201);
this.successResponder.SendTokenSuccessResponse(response, 'Login successful', tokens);
```

### **CustomErrorResponse (Legacy - Being Phased Out)**
```typescript
class CustomErrorResponse {
    public SendError(
        response: Response,
        error: BaseError | Error,
        statusCode?: number
    ): void;
}

// Usage:
this.errorResponder.SendError(response, error);
```

### **Recommended Pattern (Using GlobalErrorHandler):**
```typescript
// In Controllers - NEW WAY
export async function createUser(req: Request, res: Response, next: NextFunction) {
    try {
        const result = await userService.createUser(req.body);
        
        if (!result.success) {
            next(result.error);  // Pass to GlobalErrorHandler
            return;
        }
        
        // Handle success response directly
        res.status(201).json({
            success: true,
            message: 'User created successfully',
            data: result.data
        });
    } catch (error) {
        next(error);  // Pass unexpected errors to GlobalErrorHandler
    }
}
```

---

## 🔍 Error Code Categories

### **Authentication & Authorization (4xx):**
```typescript
// 401 Unauthorized
INVALID_CREDENTIALS = 'INVALID_CREDENTIALS'
INVALID_TOKEN = 'INVALID_TOKEN'
TOKEN_EXPIRED = 'TOKEN_EXPIRED'
AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED'
TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED'

// 403 Forbidden
INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS'
ACCESS_DENIED = 'ACCESS_DENIED'
ADMIN_REQUIRED = 'ADMIN_REQUIRED'
ROLE_REQUIRED = 'ROLE_REQUIRED'
```

### **Resource Management (4xx):**
```typescript
// 404 Not Found
USER_NOT_FOUND = 'USER_NOT_FOUND'
RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND'
ENDPOINT_NOT_FOUND = 'ENDPOINT_NOT_FOUND'

// 409 Conflict
USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS'
EMAIL_ALREADY_REGISTERED = 'EMAIL_ALREADY_REGISTERED'
RESOURCE_CONFLICT = 'RESOURCE_CONFLICT'
CONCURRENT_MODIFICATION = 'CONCURRENT_MODIFICATION'
```

### **Validation & Input (4xx):**
```typescript
// 400 Bad Request
VALIDATION_FAILED = 'VALIDATION_FAILED'
INVALID_INPUT = 'INVALID_INPUT'
MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD'
INVALID_FORMAT = 'INVALID_FORMAT'
INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT'
WEAK_PASSWORD = 'WEAK_PASSWORD'
INVALID_ENUM_VALUE = 'INVALID_ENUM_VALUE'
```

### **System & Infrastructure (5xx):**
```typescript
// 500 Internal Server Error
INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
DATABASE_ERROR = 'DATABASE_ERROR'
UNKNOWN_ERROR = 'UNKNOWN_ERROR'
CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'

// 502 Bad Gateway
EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
THIRD_PARTY_API_ERROR = 'THIRD_PARTY_API_ERROR'

// 503 Service Unavailable
SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
DATABASE_UNAVAILABLE = 'DATABASE_UNAVAILABLE'
REDIS_UNAVAILABLE = 'REDIS_UNAVAILABLE'
```

---

## 🧪 Testing Error Types

### **Unit Testing BaseError:**
```typescript
describe('BaseError Types', () => {
    describe('UnauthorizedError', () => {
        it('should create error with correct properties', () => {
            const error = new UnauthorizedError('Invalid token', ErrorCodes.INVALID_TOKEN);
            
            expect(error.statusCode).toBe(401);
            expect(error.errorCode).toBe('INVALID_TOKEN');
            expect(error.message).toBe('Invalid token');
            expect(error.toApiResponseError()).toMatchObject({
                code: 'INVALID_TOKEN',
                message: 'Invalid token'
            });
        });
    });
    
    describe('ValidationError with details', () => {
        it('should include validation details', () => {
            const details = { field: 'email', reason: 'Invalid format' };
            const error = new ValidationError('Validation failed', ErrorCodes.VALIDATION_FAILED, details);
            
            expect(error.details).toEqual(details);
            expect(error.toApiResponseError().details).toEqual(details);
        });
    });
});
```

### **Integration Testing Error Responses:**
```typescript
describe('Error Response Integration', () => {
    it('should return standardized error format', async () => {
        const response = await request
            .post('/api/v1/auth/login')
            .send({ email: '<EMAIL>', password: 'wrong' });
        
        expect(response.status).toBe(401);
        expect(response.body).toMatchObject({
            success: false,
            message: expect.any(String),
            error: {
                code: 'INVALID_CREDENTIALS',
                message: expect.any(String)
            }
        });
    });
});
```

---

*This guide provides comprehensive coverage of all error types and response patterns in the Excelytics.SharedInternals package.*
