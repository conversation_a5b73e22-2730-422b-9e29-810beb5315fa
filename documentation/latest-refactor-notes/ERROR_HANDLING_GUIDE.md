# Error Handling Guide - Introspection.Identity

## 📋 Overview

This document provides a comprehensive guide to the error handling system used in the Introspection.Identity microservice. Our error handling follows a standardized approach using the SharedInternals GlobalErrorHandler with custom error types and response patterns.

---

## 🏗️ Architecture Overview

### **Error Handling Flow:**
```
Request → Controller → Service → Error Occurs
                ↓
        next(error) OR return { success: false, error }
                ↓
        GlobalErrorHandler (SharedInternals)
                ↓
        Standardized JSON Response
```

### **Key Components:**
1. **BaseError Classes** - Custom error types with HTTP status codes
2. **GlobalErrorHandler** - Centralized error processing and logging
3. **CustomErrorResponse** - Legacy response helper (being phased out)
4. **CustomSuccessResponse** - Success response helper
5. **Service Result Pattern** - Standardized service return format

---

## 🎯 Error Types & Hierarchy

### **BaseError (Parent Class)**
```typescript
class BaseError extends Error {
    public readonly statusCode: HttpStatusCode;
    public readonly errorCode: <PERSON>rrorCode;
    public readonly details?: ErrorDetails;
    public readonly originalError?: Error;
}
```

### **Specific Error Types:**

#### **1. UnauthorizedError (401)**
```typescript
// Usage in services
throw new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);

// Common scenarios:
- Invalid login credentials
- Expired or malformed JWT tokens
- Missing authentication headers
```

#### **2. ForbiddenError (403)**
```typescript
// Usage in services
throw new ForbiddenError('Insufficient permissions', ErrorCodes.INSUFFICIENT_PERMISSIONS);

// Common scenarios:
- User lacks required permissions
- Resource access denied
- Admin-only operations
```

#### **3. ConflictError (409)**
```typescript
// Usage in services
throw new ConflictError('User already exists', ErrorCodes.USER_ALREADY_EXISTS);

// Common scenarios:
- Duplicate email registration
- Resource conflicts
- Constraint violations
```

#### **4. NotFoundError (404)**
```typescript
// Usage in services
throw new NotFoundError('User not found', ErrorCodes.USER_NOT_FOUND);

// Common scenarios:
- User lookup failures
- Resource not found
- Invalid IDs
```

#### **5. ValidationError (400)**
```typescript
// Handled automatically by Zod middleware
// Custom usage:
throw new ValidationError('Invalid input data', ErrorCodes.VALIDATION_FAILED, zodError);
```

---

## 🔧 GlobalErrorHandler Features

### **Environment-Aware Logging:**
```typescript
interface ErrorLoggingConfig {
    isVerbose: boolean;           // true in development/test
    isDebugMode: boolean;         // true in development/test
    shouldLogSensitiveInfo: boolean; // true only with LOG_SENSITIVE=true
}
```

### **Debug Flags:**
```bash
# Environment Variables
DEBUG_ERRORS=true         # Enable verbose error logging
LOG_SENSITIVE=true        # Log sensitive information (development only)
ENV=development          # Controls overall logging behavior
```

### **Logging Behavior:**

#### **Expected Errors (4xx) in Test/Development:**
```
--- Global Error Handler Triggered ---
Request Path: POST /api/v1/auth/login
Timestamp: 2025-06-23T22:07:44.807Z
[Error Type]: UnauthorizedError
Error Code: INVALID_CREDENTIALS
Status Code: 401
Message: Invalid email or password
```

#### **Unexpected Errors (5xx):**
```
--- Global Error Handler Triggered ---
Request Path: POST /api/v1/auth/register
Timestamp: 2025-06-23T22:07:44.807Z
Error Type: DatabaseError
Message: Connection timeout
Stack: [REDACTED - Set LOG_SENSITIVE=true to view]
```

### **Sensitive Information Redaction:**
```typescript
// Automatically redacts fields containing:
const sensitivePatterns = [
    'password', 'token', 'secret', 'key', 'auth', 
    'credential', 'session', 'jwt', 'bearer'
];

// Example:
// BEFORE: { password: "mySecret123", email: "<EMAIL>" }
// AFTER:  { password: "[REDACTED]", email: "<EMAIL>" }
```

---

## 📝 Response Formats

### **Error Response Structure:**
```typescript
interface ErrorResponse {
    success: false;
    message: string;           // Human-readable error message
    error: {
        code: string;          // Machine-readable error code
        message: string;       // Detailed error message
        details?: any;         // Additional error context (optional)
    };
}
```

### **Success Response Structure:**
```typescript
interface SuccessResponse<T> {
    success: true;
    message: string;           // Success message
    data: T;                   // Response payload
}
```

### **Token Response Structure:**
```typescript
interface TokenResponse {
    success: true;
    message: string;
    data: {
        token: string;         // JWT access token
        refreshToken: string;  // JWT refresh token
        tokenPayload: {        // Decoded token information
            userId: string;
            email: string;
            clientId: string;
            // ... other claims
        };
    };
}
```

---

## 🛠️ Implementation Patterns

### **Current Pattern (Legacy - Being Phased Out):**
```typescript
// In Controllers - OLD WAY
export class AuthenticationController {
    public async login(request: Request, response: Response): Promise<void> {
        try {
            const result = await this.authenticationService.loginUser(data);
            if (!result.success) {
                this.errorResponder.SendError(response, result.error);
                return;
            }
            this.successResponder.SendTokenSuccessResponse(response, 'Login successful', result.data);
        } catch (error) {
            this.errorResponder.SendError(response, error);
        }
    }
}
```

### **Recommended Pattern (Future Implementation):**
```typescript
// In Controllers - NEW WAY
export class AuthenticationController {
    public async login(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const result = await this.authenticationService.loginUser(data);
            if (!result.success) {
                next(result.error);  // Pass to GlobalErrorHandler
                return;
            }
            // Only handle success responses in controllers
            response.status(200).json({
                success: true,
                message: 'Login successful',
                data: result.data
            });
        } catch (error) {
            next(error);  // Pass to GlobalErrorHandler
        }
    }
}
```

### **Service Pattern (Recommended):**
```typescript
// In Services
export class AuthenticationService {
    public async loginUser(credentials: LoginDTO): Promise<LoginServiceResult> {
        try {
            // Business logic here
            const user = await this.findUserByEmail(credentials.email);
            if (!user) {
                throw new UnauthorizedError('Invalid email or password', ErrorCodes.INVALID_CREDENTIALS);
            }
            
            return { success: true, data: tokenData };
        } catch (error) {
            // Let the error bubble up to controller/GlobalErrorHandler
            if (error instanceof BaseError) {
                return { success: false, error };
            }
            // Wrap unexpected errors
            const wrappedError = new BaseError(
                'Login failed',
                HttpStatus.INTERNAL_SERVER_ERROR,
                ErrorCodes.AUTHENTICATION_FAILED,
                undefined,
                error
            );
            return { success: false, error: wrappedError };
        }
    }
}
```

---

## 🔍 Error Codes Reference

### **Authentication & Authorization:**
```typescript
INVALID_CREDENTIALS = 'INVALID_CREDENTIALS'           // 401
INVALID_TOKEN = 'INVALID_TOKEN'                       // 401
TOKEN_EXPIRED = 'TOKEN_EXPIRED'                       // 401
INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS' // 403
AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED'      // 401
TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED'        // 401
```

### **User Management:**
```typescript
USER_NOT_FOUND = 'USER_NOT_FOUND'                    // 404
USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS'          // 409
USER_CREATION_FAILED = 'USER_CREATION_FAILED'        // 500
USER_UPDATE_FAILED = 'USER_UPDATE_FAILED'            // 500
USER_DELETION_FAILED = 'USER_DELETION_FAILED'        // 500
```

### **Validation & Input:**
```typescript
VALIDATION_FAILED = 'VALIDATION_FAILED'              // 400
INVALID_INPUT = 'INVALID_INPUT'                       // 400
MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD'    // 400
```

### **System & Database:**
```typescript
INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'      // 500
DATABASE_ERROR = 'DATABASE_ERROR'                    // 500
EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'    // 502
SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'          // 503
```

---

## 🧪 Testing Error Handling

### **Testing Expected Errors:**
```typescript
describe('Authentication Errors', () => {
    it('Should return 401 for invalid credentials', async () => {
        const response = await request
            .post('/api/v1/auth/login')
            .send({ email: '<EMAIL>', password: 'wrong' });
        
        expect(response.status).toBe(401);
        expect(response.body.success).toBe(false);
        expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });
});
```

### **Testing Validation Errors:**
```typescript
it('Should return 400 for missing email', async () => {
    const response = await request
        .post('/api/v1/auth/login')
        .send({ password: 'password123' });
    
    expect(response.status).toBe(400);
    expect(response.body.error.code).toBe('VALIDATION_FAILED');
});
```

---

*This guide will be updated as we transition from CustomErrorResponse to the standardized next(error) pattern.*
