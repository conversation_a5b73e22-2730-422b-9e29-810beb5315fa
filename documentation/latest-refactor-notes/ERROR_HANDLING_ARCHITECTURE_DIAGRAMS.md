# Error Handling Architecture Diagrams

This document contains comprehensive Mermaid diagrams visualizing the error handling system architecture, types hierarchy, and data flow for the Excelytics microservices.

## Table of Contents

1. [Simple Overview - Error Handling Flow](#1-simple-overview---error-handling-flow)
2. [Error Types Hierarchy](#2-error-types-hierarchy)
3. [Response Types Structure](#3-response-types-structure)
4. [Comprehensive Error Processing Flow](#4-comprehensive-error-processing-flow)
5. [Helper Functions Architecture](#5-helper-functions-architecture)
6. [Environment Configuration Hierarchy](#6-environment-configuration-hierarchy)
7. [Microservices Integration Architecture](#7-microservices-integration-architecture)
8. [Error Code Categories and HTTP Status Mapping](#8-error-code-categories-and-http-status-mapping)

---

## 1. Simple Overview - Error Handling Flow

```mermaid
flowchart TD
    A[Request] --> B[Express Route Handler]
    B --> C{Error Occurs?}
    C -->|Yes| D[GlobalErrorHandler]
    C -->|No| E[Success Response]
    
    D --> F[Classify Error]
    F --> G[Log Error]
    G --> H[Process Response]
    H --> I[Send JSON Response]
    
    style D fill:#ff6b6b
    style E fill:#51cf66
    style I fill:#339af0
```

**Purpose**: Shows the basic flow from request to response, highlighting where errors are caught and processed.

---

## 2. Error Types Hierarchy

```mermaid
classDiagram
    class Error {
        +message string
        +stack string
    }

    class BaseError {
        +message string
        +statusCode number
        +errorCode string
        +details any
        +originalError Error
    }

    class ValidationError {
        +details ValidationDetails
        +errorCode string
        +statusCode number
    }

    class UnauthorizedError {
        +errorCode string
        +statusCode number
    }

    class NotFoundError {
        +errorCode string
        +statusCode number
        +details ResourceDetails
    }

    class ConflictError {
        +errorCode string
        +statusCode number
    }

    class InternalServerError {
        +errorCode string
        +statusCode number
    }

    class ZodError {
        +issues ZodIssue[]
        +format() FormattedError
    }

    Error <|-- BaseError
    BaseError <|-- ValidationError
    BaseError <|-- UnauthorizedError
    BaseError <|-- NotFoundError
    BaseError <|-- ConflictError
    BaseError <|-- InternalServerError

    Error <|-- ZodError

    style BaseError fill:#e3f2fd
    style ValidationError fill:#fff3e0
    style UnauthorizedError fill:#ffebee
    style ZodError fill:#f3e5f5
```

**Purpose**: Illustrates the inheritance hierarchy of error classes and their specific properties.

---

## 3. Response Types Structure

```mermaid
classDiagram
    class ApiErrorObject {
        +code string
        +message string
        +details any
    }

    class ApiResponse {
        +success boolean
        +message string
        +data any
        +error ApiErrorObject
        +pagination PaginationMetadata
    }

    class SuccessResponse {
        +success boolean
        +data any
        +message string
    }

    class ErrorResponse {
        +success boolean
        +error ApiErrorObject
        +message string
    }

    class TokenResponse {
        +token string
        +refreshToken string
        +tokenPayload any
    }

    ApiResponse <|-- SuccessResponse
    ApiResponse <|-- ErrorResponse
    ApiResponse *-- ApiErrorObject

    class ApiErrorObjectSchema {
        <<ZodSchema>>
        +parse(data) ApiErrorObject
        +safeParse(data) SafeParseResult
    }

    class ErrorResponseSchema {
        <<ZodSchema>>
        +parse(data) ErrorResponse
        +safeParse(data) SafeParseResult
    }

    class SuccessResponseSchema {
        <<ZodSchema>>
        +parse(data) SuccessResponse
        +safeParse(data) SafeParseResult
    }

    ApiErrorObjectSchema ..> ApiErrorObject
    ErrorResponseSchema ..> ErrorResponse
    SuccessResponseSchema ..> SuccessResponse

    style ApiResponse fill:#e8f5e8
    style ErrorResponse fill:#ffebee
    style SuccessResponse fill:#e8f5e8
    style ApiErrorObjectSchema fill:#f3e5f5
    style ErrorResponseSchema fill:#f3e5f5
    style SuccessResponseSchema fill:#f3e5f5
```

**Purpose**: Shows the structure of API response types and their relationship with Zod validation schemas.

---

## 4. Comprehensive Error Processing Flow

```mermaid
flowchart TD
    A[Error Thrown] --> B[GlobalErrorHandler]
    B --> C[getErrorLoggingConfig]
    C --> D[classifyError]
    
    D --> E{Error Type?}
    E -->|BaseError| F[processBaseErrorResponse]
    E -->|ZodError| G[processZodErrorResponse]
    E -->|CORS Error| H[processCorsErrorResponse]
    E -->|Generic Error| I[processGenericErrorResponse]
    
    F --> J[Check Environment]
    G --> K[Format Zod Issues]
    H --> L[Create CORS Response]
    I --> M[Determine Status Code]
    
    J --> N{Production & 500?}
    N -->|Yes| O[Sanitize Message]
    N -->|No| P[Keep Original]
    
    K --> Q[Create Validation Response]
    L --> R[Create CORS Response]
    M --> S[Create Generic Response]
    
    O --> T[Validate with Zod]
    P --> T
    Q --> T
    R --> T
    S --> T
    
    T --> U[addStackTraceToResponse]
    U --> V{Debug Mode?}
    V -->|Yes| W[Add Stack Trace]
    V -->|No| X[Skip Stack Trace]
    
    W --> Y[logError]
    X --> Y
    Y --> Z[Send Response]
    
    style B fill:#ff6b6b
    style T fill:#f3e5f5
    style Z fill:#339af0
```

**Purpose**: Detailed flow showing how different error types are processed through the system.

---

## 5. Helper Functions Architecture

```mermaid
graph TB
    subgraph "Error Logging Helpers"
        A[getErrorLoggingConfig]
        B[classifyError]
        C[logError]
        D[logBasicErrorInfo]
        E[logBaseErrorDetails]
        F[logGenericErrorDetails]
        G[redactSensitiveInformation]
    end
    
    subgraph "Error Response Helpers"
        H[processErrorResponse]
        I[processBaseErrorResponse]
        J[processZodErrorResponse]
        K[processCorsErrorResponse]
        L[processGenericErrorResponse]
        M[addStackTraceToResponse]
    end
    
    subgraph "Core Error Helper"
        N[ErrorHelper.handleError]
        O[handleZodErrors]
        P[handleCorsErrors]
        Q[handleUnexpectedErrors]
    end
    
    subgraph "Environment & Config"
        R[Bun.env]
        S[BaseFieldsSchema]
        T[ErrorLoggingConfig]
        U[ErrorClassification]
    end
    
    A --> R
    A --> T
    B --> U
    C --> D
    C --> E
    C --> F
    E --> G
    F --> G
    
    H --> I
    H --> J
    H --> K
    H --> L
    H --> M
    
    N --> O
    N --> P
    N --> Q
    
    I --> N
    J --> O
    K --> P
    L --> Q
    
    style A fill:#e3f2fd
    style H fill:#fff3e0
    style N fill:#f3e5f5
    style R fill:#e8f5e8
```

**Purpose**: Shows the organization and dependencies between helper functions across different modules.

---

## 6. Environment Configuration Hierarchy

```mermaid
graph TD
    subgraph "Environment Sources"
        A[Bun.env.ENV]
        B[Bun.env.NODE_ENV]
        C[Bun.env.DEBUG_ERRORS]
        D[Bun.env.LOG_SENSITIVE]
        E[Bun.env.LOG_LEVEL]
    end
    
    subgraph "Configuration Logic"
        F[getErrorLoggingConfig]
        G{Environment Check}
        H{Debug Override}
        I{Sensitive Check}
    end
    
    subgraph "Configuration Output"
        J[ErrorLoggingConfig]
        K[isVerbose: boolean]
        L[isDebugMode: boolean]
        M[shouldLogSensitiveInfo: boolean]
    end
    
    subgraph "Environment Values"
        N[development]
        O[test]
        P[production]
        Q[staging]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    
    F --> G
    F --> H
    F --> I
    
    G --> N
    G --> O
    G --> P
    G --> Q
    
    G --> L
    H --> K
    I --> M
    
    K --> J
    L --> J
    M --> J
    
    style F fill:#e3f2fd
    style J fill:#fff3e0
    style N fill:#e8f5e8
    style O fill:#e8f5e8
    style P fill:#ffebee
    style Q fill:#fff3e0
```

**Purpose**: Illustrates how environment variables are processed into configuration objects.

---

## 7. Microservices Integration Architecture

```mermaid
graph TB
    subgraph "SharedInternals Package"
        A[GlobalErrorHandler]
        B[Error Types]
        C[Response Schemas]
        D[Helper Functions]
        E[Environment Config]
    end

    subgraph "Identity Service (Port 6002)"
        F[Express App]
        F1[Auth Routes]
        F2[User Routes]
        F3[Token Routes]
    end

    subgraph "Finance Service (Port 6001)"
        G[Express App]
        G1[Upload Routes]
        G2[Analysis Routes]
        G3[Report Routes]
    end

    subgraph "Calc Service (Port 6003)"
        H[Express App]
        H1[Calculation Routes]
        H2[Formula Routes]
        H3[Processing Routes]
    end

    subgraph "Client Service (Port 4200)"
        I[Frontend App]
        I1[Dashboard]
        I2[Upload UI]
        I3[Reports UI]
    end

    A --> F
    A --> G
    A --> H
    B --> F
    B --> G
    B --> H
    C --> F
    C --> G
    C --> H
    D --> F
    D --> G
    D --> H
    E --> F
    E --> G
    E --> H

    F --> I
    G --> I
    H --> I

    F1 --> F2
    F2 --> F3
    G1 --> G2
    G2 --> G3
    H1 --> H2
    H2 --> H3
    I1 --> I2
    I2 --> I3

    style A fill:#ff6b6b
    style F fill:#e3f2fd
    style G fill:#fff3e0
    style H fill:#f3e5f5
    style I fill:#e8f5e8
```

**Purpose**: Shows how the SharedInternals package integrates across all microservices in the Excelytics ecosystem.

---

## 8. Error Code Categories and HTTP Status Mapping

```mermaid
graph LR
    subgraph "4xx Client Errors"
        A[400 - BAD_REQUEST]
        B[401 - UNAUTHORIZED]
        C[403 - FORBIDDEN]
        D[404 - NOT_FOUND]
        E[409 - CONFLICT]
        F[422 - VALIDATION_ERROR]
        G[429 - RATE_LIMITED]
    end

    subgraph "5xx Server Errors"
        H[500 - INTERNAL_SERVER_ERROR]
        I[502 - BAD_GATEWAY]
        J[503 - SERVICE_UNAVAILABLE]
        K[504 - GATEWAY_TIMEOUT]
    end

    subgraph "Authentication Errors"
        L[INVALID_TOKEN]
        M[TOKEN_EXPIRED]
        N[INVALID_CREDENTIALS]
        O[INSUFFICIENT_PERMISSIONS]
    end

    subgraph "Validation Errors"
        P[VALIDATION_ERROR]
        Q[INVALID_FORMAT]
        R[MISSING_PARAMETER]
        S[INVALID_EMAIL_FORMAT]
    end

    subgraph "Business Logic Errors"
        T[USER_NOT_FOUND]
        U[USER_ALREADY_EXISTS]
        V[INSUFFICIENT_FUNDS]
        W[FILE_TOO_LARGE]
    end

    subgraph "System Errors"
        X[DATABASE_ERROR]
        Y[NETWORK_ERROR]
        Z[CORS_NOT_ALLOWED]
        AA[UNKNOWN_ERROR]
    end

    L --> B
    M --> B
    N --> B
    O --> C

    P --> F
    Q --> A
    R --> A
    S --> F

    T --> D
    U --> E
    V --> A
    W --> A

    X --> H
    Y --> H
    Z --> C
    AA --> H

    style A fill:#fff3e0
    style B fill:#ffebee
    style C fill:#ffebee
    style D fill:#fff3e0
    style E fill:#fff3e0
    style F fill:#fff3e0
    style G fill:#ffebee
    style H fill:#ffcdd2
    style I fill:#ffcdd2
    style J fill:#ffcdd2
    style K fill:#ffcdd2
```

**Purpose**: Maps error codes to their corresponding HTTP status codes, organized by category.

---

## Summary

These Mermaid diagrams provide a comprehensive visualization of the error handling system:

### **Simple Diagrams:**
1. **Error Handling Flow** - Basic request-to-response flow
2. **Error Types Hierarchy** - Class inheritance structure

### **Comprehensive Diagrams:**
3. **Response Types Structure** - API response types and Zod schemas
4. **Comprehensive Error Processing Flow** - Detailed error processing pipeline
5. **Helper Functions Architecture** - Function organization and dependencies
6. **Environment Configuration Hierarchy** - Environment variable processing
7. **Microservices Integration** - How SharedInternals integrates across services
8. **Error Code Categories** - Error codes mapped to HTTP status codes

### **Key Insights from the Diagrams:**

1. **Hierarchical Structure**: Clear inheritance from `Error` → `BaseError` → specific error types
2. **Type Safety**: Zod schemas validate all response structures at runtime
3. **Environment-Driven**: Configuration adapts based on `Bun.env` variables
4. **Modular Design**: Helper functions are organized by responsibility
5. **Consistent Integration**: All microservices use the same error handling patterns
6. **Comprehensive Coverage**: Error codes cover authentication, validation, business logic, and system errors

### **Usage Guidelines:**

These diagrams can be used for:
- **Documentation** - Understanding the system architecture
- **Onboarding** - Teaching new developers the error handling patterns
- **Debugging** - Tracing error flow through the system
- **Planning** - Extending the error handling system
- **Code Reviews** - Ensuring consistency across microservices

### **Environment Configuration:**

```bash
# Core Environment
ENV=development|test|production|staging
NODE_ENV=development|test|production|staging  # Fallback for ENV

# Error Handling & Logging Configuration
DEBUG_ERRORS=true              # Enable verbose error logging (overrides environment defaults)
LOG_SENSITIVE=true             # Log sensitive information (development only - NEVER in production)

# Logging
LOG_LEVEL=DEBUG|INFO|WARN|ERROR|TRACE|SILENT
```

### **Integration Notes:**

- All microservices import the `GlobalErrorHandler` from SharedInternals
- Error types and response schemas are shared across services
- Environment configuration is consistent but can be overridden per service
- Zod validation ensures type safety at runtime
- Helper functions provide consistent error processing logic

---

*Generated for Excelytics Microservices Architecture - Introspection Consulting*
