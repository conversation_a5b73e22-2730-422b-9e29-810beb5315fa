# Global Error Handler Documentation - Excelytics.SharedInternals

## 📋 Overview

The GlobalErrorHandler is a centralized error handling middleware for all Excelytics microservices. It provides standardized error responses, environment-aware logging, and sensitive information protection across the entire platform.

---

## 🏗️ Architecture & Design

### **Core Principles:**
1. **Centralized Error Handling** - Single point of error processing for all microservices
2. **Environment Awareness** - Different logging behavior for development, test, and production
3. **Security First** - Automatic redaction of sensitive information
4. **Type Safety** - Full TypeScript support with proper interfaces
5. **Low Complexity** - Each function has a single responsibility and low complexity

### **Component Structure:**
```
GlobalErrorHandler (Main Middleware)
├── ErrorLoggingHelper (Logging Logic)
│   ├── getErrorLoggingConfig()
│   ├── classifyError()
│   ├── logError()
│   └── redactSensitiveInformation()
├── ErrorResponseHelper (Response Processing)
│   ├── processErrorResponse()
│   ├── processBaseErrorResponse()
│   ├── processZodErrorResponse()
│   └── addStackTraceToResponse()
└── Types & Interfaces
    ├── ErrorLoggingConfig
    ├── ErrorClassification
    └── SensitiveFieldPattern
```

---

## 🔧 Configuration & Environment Variables

### **Environment Variables:**
```bash
# Core Environment
ENV=development|test|production|staging
NODE_ENV=development|test|production|staging  # Fallback for ENV

# Error Handling & Logging Configuration
DEBUG_ERRORS=true              # Enable verbose error logging (overrides environment defaults)
LOG_SENSITIVE=true             # Log sensitive information (development only - NEVER in production)

# Logging
LOG_LEVEL=DEBUG|INFO|WARN|ERROR|TRACE|SILENT
```

### **Logging Configuration Logic:**
```typescript
interface ErrorLoggingConfig {
    isVerbose: boolean;           // ENV=development|test OR DEBUG_ERRORS=true
    isDebugMode: boolean;         // ENV=development|test
    shouldLogSensitiveInfo: boolean; // ENV=development AND LOG_SENSITIVE=true
}

// Configuration is determined using Bun.env:
function getErrorLoggingConfig(): ErrorLoggingConfig {
    const currentEnv = Bun.env.ENV || Bun.env.NODE_ENV || 'development';
    const isDebugMode = currentEnv === 'development' || currentEnv === 'test';
    const isVerbose = isDebugMode || Bun.env.DEBUG_ERRORS === 'true';
    const shouldLogSensitiveInfo = currentEnv === 'development' && Bun.env.LOG_SENSITIVE === 'true';

    return { isVerbose, isDebugMode, shouldLogSensitiveInfo };
}
```

---

## 🎯 Error Classification System

### **Error Types Supported:**

#### **1. BaseError (Custom Errors)**
```typescript
class BaseError extends Error {
    statusCode: HttpStatusCode;    // 400, 401, 403, 404, 409, 500, etc.
    errorCode: ErrorCode;          // INVALID_CREDENTIALS, USER_NOT_FOUND, etc.
    details?: ErrorDetails;        // Additional context
    originalError?: Error;         // Wrapped original error
}

// Subclasses:
- UnauthorizedError (401)
- ForbiddenError (403)
- NotFoundError (404)
- ConflictError (409)
- ValidationError (400)
```

#### **2. Zod Validation Errors**
```typescript
// Automatically handled by ErrorHelper.handleZodErrors()
// Converts Zod validation failures to standardized format
```

#### **3. CORS Errors**
```typescript
// Handles "Not allowed by CORS" errors
// Provides user-friendly CORS error messages
```

#### **4. Generic/Unexpected Errors**
```typescript
// Fallback for standard JavaScript Error objects
// Wraps in standardized format with appropriate status codes
```

---

## 📊 Logging Behavior

### **Development/Test Environment:**
```typescript
// Expected Errors (4xx) - Minimal Logging
--- Global Error Handler Triggered ---
Request Path: POST /api/v1/auth/login
Timestamp: 2025-06-23T22:07:44.807Z
[Error Type]: UnauthorizedError
Error Code: INVALID_CREDENTIALS
Status Code: 401
Message: Invalid email or password

// Unexpected Errors (5xx) - Detailed Logging
--- Global Error Handler Triggered ---
Request Path: POST /api/v1/users
Timestamp: 2025-06-23T22:07:44.807Z
Error Type: DatabaseError
Message: Connection timeout
Stack: [REDACTED - Set LOG_SENSITIVE=true to view]
```

### **Production Environment:**
```typescript
// Expected Errors (4xx) - No Logging (Silent)
// Unexpected Errors (5xx) - Essential Info Only
--- Global Error Handler Triggered ---
Request Path: POST /api/v1/users
Timestamp: 2025-06-23T22:07:44.807Z
Error Type: DatabaseError
Message: Internal server error occurred
```

### **Sensitive Information Redaction:**
```typescript
// Automatically redacts fields containing these patterns:
const SENSITIVE_PATTERNS = [
    'password', 'token', 'secret', 'key', 'auth',
    'credential', 'session', 'jwt', 'bearer'
];

// Example:
// BEFORE: { userPassword: "secret123", email: "<EMAIL>" }
// AFTER:  { userPassword: "[REDACTED]", email: "<EMAIL>" }
```

---

## 🔄 Response Processing Flow

### **1. Error Classification:**
```typescript
function classifyError(error: any): ErrorClassification {
    return {
        isExpectedError: error.statusCode && error.statusCode < 500,
        isBaseError: error instanceof BaseError,
        isZodError: error.name === 'ZodError',
        isCorsError: error.message?.includes('Not allowed by CORS')
    };
}
```

### **2. Logging Decision Tree:**
```
Error Occurs
    ↓
Is Verbose Mode? (Dev/Test/DEBUG_ERRORS=true)
    ↓ YES                           ↓ NO
Log All Details              Is Expected Error (4xx)?
    ↓                               ↓ YES        ↓ NO
Is Sensitive Logging On?        Silent      Log Essential
    ↓ YES        ↓ NO                           Info Only
Show Details  Redact Details
```

### **3. Response Generation:**
```typescript
// BaseError Response
{
    success: false,
    message: "User-friendly error message",
    error: {
        code: "MACHINE_READABLE_CODE",
        message: "Detailed error message",
        details?: { /* Additional context */ }
    }
}

// Production 500 Error Response (Sanitized)
{
    success: false,
    message: "An internal server error occurred. Please try again later.",
    error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "An internal server error occurred."
        // details removed for security
    }
}
```

---

## 🛠️ Helper Functions Reference

### **Error Logging Helpers:**

#### **getErrorLoggingConfig(): ErrorLoggingConfig**
- **Purpose**: Determines logging behavior based on environment
- **Complexity**: Low (8 lines)
- **Returns**: Configuration object with logging flags

#### **classifyError(error: any): ErrorClassification**
- **Purpose**: Categorizes error type for appropriate handling
- **Complexity**: Low (7 lines)
- **Returns**: Classification object with boolean flags

#### **logError(error: any, request: Request, config: ErrorLoggingConfig): void**
- **Purpose**: Main logging orchestrator
- **Complexity**: Low (12 lines)
- **Behavior**: Delegates to specific logging functions based on error type

#### **logBaseErrorDetails(error: BaseError, config: ErrorLoggingConfig, classification: ErrorClassification): void**
- **Purpose**: Logs BaseError specific information
- **Complexity**: Medium (18 lines)
- **Features**: Conditional logging based on verbosity and sensitivity settings

#### **redactSensitiveInformation(details: any): any**
- **Purpose**: Removes sensitive data from error details
- **Complexity**: Medium (15 lines)
- **Security**: Protects passwords, tokens, secrets, etc.

### **Error Response Helpers:**

#### **processErrorResponse(error: any, config: ErrorLoggingConfig, classification: ErrorClassification): ResponseResult**
- **Purpose**: Main response processing orchestrator
- **Complexity**: Medium (18 lines)
- **Returns**: Status code and response body

#### **processBaseErrorResponse(error: BaseError, config: ErrorLoggingConfig): ResponseResult**
- **Purpose**: Handles BaseError instances
- **Complexity**: Medium (20 lines)
- **Features**: Production sanitization, sensitive data redaction

#### **addStackTraceToResponse(error: any, responseBody: ErrorResponse, config: ErrorLoggingConfig, classification: ErrorClassification): void**
- **Purpose**: Conditionally adds stack traces to responses
- **Complexity**: Medium (15 lines)
- **Security**: Only in debug mode with proper redaction

---

## 🧪 Testing & Validation

### **Unit Testing Approach:**
```typescript
describe('GlobalErrorHandler', () => {
    describe('Error Classification', () => {
        it('should classify BaseError correctly', () => {
            const error = new UnauthorizedError('Test', ErrorCodes.INVALID_TOKEN);
            const classification = classifyError(error);
            expect(classification.isBaseError).toBe(true);
            expect(classification.isExpectedError).toBe(true);
        });
    });

    describe('Logging Configuration', () => {
        it('should enable verbose logging in development', () => {
            process.env.ENV = 'development';
            const config = getErrorLoggingConfig();
            expect(config.isVerbose).toBe(true);
        });
    });
});
```

### **Integration Testing:**
```typescript
describe('Error Response Integration', () => {
    it('should return standardized error response', async () => {
        const response = await request
            .post('/api/v1/test-endpoint')
            .send({ invalid: 'data' });
        
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
            success: false,
            message: expect.any(String),
            error: {
                code: expect.any(String),
                message: expect.any(String)
            }
        });
    });
});
```

---

## 🚀 Usage in Microservices

### **Setup in Express Application:**
```typescript
import { GlobalErrorHandler } from 'excelytics.shared-internals';

// Apply as last middleware
app.use(GlobalErrorHandler);
```

### **Throwing Errors in Services:**
```typescript
// Recommended pattern
throw new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);

// With additional details
throw new ValidationError(
    'Invalid input data',
    ErrorCodes.VALIDATION_FAILED,
    { field: 'email', reason: 'Invalid format' }
);
```

### **Using next() in Controllers:**
```typescript
// Recommended pattern
export async function loginController(req: Request, res: Response, next: NextFunction) {
    try {
        const result = await authService.login(req.body);
        res.json({ success: true, data: result });
    } catch (error) {
        next(error); // Pass to GlobalErrorHandler
    }
}
```

---

## 📚 Error Codes Reference

### **Authentication & Authorization Errors:**
```typescript
// 401 Unauthorized
INVALID_CREDENTIALS = 'INVALID_CREDENTIALS'
INVALID_TOKEN = 'INVALID_TOKEN'
TOKEN_EXPIRED = 'TOKEN_EXPIRED'
AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED'
TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED'

// 403 Forbidden
INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS'
ACCESS_DENIED = 'ACCESS_DENIED'
ADMIN_REQUIRED = 'ADMIN_REQUIRED'
```

### **User Management Errors:**
```typescript
// 404 Not Found
USER_NOT_FOUND = 'USER_NOT_FOUND'
RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND'

// 409 Conflict
USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS'
EMAIL_ALREADY_REGISTERED = 'EMAIL_ALREADY_REGISTERED'
RESOURCE_CONFLICT = 'RESOURCE_CONFLICT'

// 400 Bad Request
INVALID_USER_DATA = 'INVALID_USER_DATA'
INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT'
WEAK_PASSWORD = 'WEAK_PASSWORD'
```

### **Validation & Input Errors:**
```typescript
// 400 Bad Request
VALIDATION_FAILED = 'VALIDATION_FAILED'
INVALID_INPUT = 'INVALID_INPUT'
MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD'
INVALID_FORMAT = 'INVALID_FORMAT'
INVALID_ENUM_VALUE = 'INVALID_ENUM_VALUE'
```

### **System & Infrastructure Errors:**
```typescript
// 500 Internal Server Error
INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
DATABASE_ERROR = 'DATABASE_ERROR'
UNKNOWN_ERROR = 'UNKNOWN_ERROR'

// 502 Bad Gateway
EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
THIRD_PARTY_API_ERROR = 'THIRD_PARTY_API_ERROR'

// 503 Service Unavailable
SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
DATABASE_UNAVAILABLE = 'DATABASE_UNAVAILABLE'
REDIS_UNAVAILABLE = 'REDIS_UNAVAILABLE'
```

---

## 🔄 Migration Guide

### **From CustomErrorResponse to GlobalErrorHandler:**

#### **Before (Legacy Pattern):**
```typescript
// In Controller
if (!result.success) {
    this.errorResponder.SendError(response, result.error);
    return;
}
```

#### **After (Recommended Pattern):**
```typescript
// In Controller
if (!result.success) {
    next(result.error);
    return;
}
```

### **Service Layer Changes:**
```typescript
// Before - Mixed responsibilities
catch (error) {
    console.error('Service error:', error); // Service logging
    return { success: false, error: new BaseError(...) }; // Error creation
}

// After - Single responsibility
catch (error) {
    // Let error bubble up, GlobalErrorHandler will log
    throw new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);
}
```

---

*This documentation reflects the enhanced GlobalErrorHandler with extracted helper functions and improved error handling capabilities.*
