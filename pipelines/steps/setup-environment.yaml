parameters:
  # The URL of your private registry
  - name: feedRegistryUrl
    type: string
    # Using the FinanceFeed numeric ID to prevent issues with packages that cannot encode the name correctly
    default: 'https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/'

steps:
  - checkout: self
    displayName: 'Checkout complete source'
    fetchDepth: 0

  - task: NodeTool@0
    displayName: 'Setup Node.js 22.11.0'
    inputs:
      versionSpec: '22.11.0'

  - task: Npm@1
    displayName: 'Setup Bun Globally'
    inputs:
      command: 'custom'
      customCommand: 'install -g bun'
    # verbose: true

  # 1. Authenticate introspectiondev Azure Artifacts using npm
  #    For Azure Artifacts in the same organization, no customEndpoint needed
  - task: npmAuthenticate@0
    displayName: 'Authenticate with Azure Artifacts'
    inputs:
      workingFile: '$(System.DefaultWorkingDirectory)/.npmrc'

  # 2. Copy the authenticated .npmrc to HOME for Bun to use
  - script: |
      echo "Preparing .npmrc for Bun authentication..."

      REPO_NPMRC_PATH="$(pwd)/.npmrc"
      FINAL_NPMRC_PATH="$HOME/.npmrc"

      if [ ! -f "$REPO_NPMRC_PATH" ]; then
        echo "❌ .npmrc not found!"
        exit 1
      fi

      # Copy the authenticated .npmrc to HOME where Bun will find it
      cp "$REPO_NPMRC_PATH" "$FINAL_NPMRC_PATH" || { echo "❌ Failed to copy .npmrc!"; exit 1; }
      echo "✅ Authentication configured."
    displayName: 'Prepare Global .npmrc for Bun'

  # 3. Verify Bun Authentication
  - script: |
      echo "Verifying Bun authentication..."

      # Test authentication with dry run
      bun install --dry-run || {
        echo "❌ Authentication failed. Check feed permissions and PAT validity."
        exit 1
      }

      echo "✅ Authentication successful."
    displayName: 'Verify Bun Authentication'