steps:
  - script: |
      echo "=== Publishing Changed Packages ==="
      
      # Ensure we are logged into Azure DevOps CLI using the pipeline's token
      # This gives us permission to query the artifact feed.
      echo $(System.AccessToken) | az devops login
      az devops configure --defaults organization=$(System.TeamFoundationCollectionUri) project=$(System.TeamProject)
      
      if [ "$(ShouldPublish)" = "true" ]; then
        echo "📦 Packages to publish based on version check: $(PackagesToPublish)"
      
        PACKAGES_WERE_PUBLISHED=false
      
        for package_info in $(PackagesToPublish); do
          PACKAGE_NAME=$(echo "$package_info" | cut -d':' -f1)
          PACKAGE_VERSION=$(echo "$package_info" | cut -d':' -f2)
      
          echo "----------------------------------------------------"
          echo "Processing $PACKAGE_NAME@$PACKAGE_VERSION..."
      
          # Check if the package version already exists in the feed
          if az artifacts package version show --package-name "$PACKAGE_NAME" --package-version "$PACKAGE_VERSION" --feed "FinanceFeed" --scope project >/dev/null 2>&1; then
            # If the command succeeds, the package exists.
            echo "✅ SKIPPING: Version $PACKAGE_VERSION already exists in FinanceFeed."
          else
            # If the command fails, the package does not exist. Publish it.
            echo "🚀 PUBLISHING: Version $PACKAGE_VERSION not found in feed."
      
            cd "packages/$PACKAGE_NAME"
      
            # Run the package's publish script (e.g., "bun install && bun run build && bun publish")
            if bun run publish:package; then
              echo "✅ Successfully published $PACKAGE_NAME@$PACKAGE_VERSION"
      
              # Create git tag for this successfully published package
              git tag "${PACKAGE_NAME}-v${PACKAGE_VERSION}"
              PACKAGES_WERE_PUBLISHED=true
            else
              echo "❌ Failed to publish $PACKAGE_NAME. Check its build script and logs."
              # Optionally, exit the script if one package fails to prevent tagging
              # exit 1 
            fi
      
            cd - > /dev/null
          fi
        done
      
        # Push all new tags at the end if any packages were successfully published
        if [ "$PACKAGES_WERE_PUBLISHED" = true ]; then
          echo "Pushing new version tags to remote..."
          git push origin --tags
        else
          echo "No new packages were published."
        fi
      
      else
        echo "ℹ️ No packages to publish based on initial version check."
      fi
    displayName: 'Publish Packages'
    condition: and(succeeded(), eq(variables['ShouldPublish'], 'true'))
    env:
      # The System.AccessToken is a special variable containing the pipeline's auth token
      AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)