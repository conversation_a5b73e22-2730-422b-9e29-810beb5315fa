steps:
  - script: |
      echo "Running ESLint analysis..."
      bunx eslint . --format json --output-file eslint-results.json || true
      bunx eslint . || echo "ESLint found issues (continuing...)"
    displayName: 'Run ESLint Analysis'

  - script: |
      echo "Building packages individually..."

      # Build each package using their own build script
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          echo "Building ${package_dir}..."
          cd "${package_dir}"

          # Use the package's own build script
          bun run build || echo "Build failed for ${package_dir}"

          # Verify build output
          if [ -d "dist" ]; then
            echo "✅ Build successful for ${package_dir}"
            ls -la dist/
          else
            echo "⚠️ No dist folder found for ${package_dir}"
          fi

          cd - > /dev/null
        fi
      done
    displayName: 'Build All Packages'

  - script: |
      echo "Running tests for packages with test suites..."

      # Set up test environment variables
      export ENV=test
      export NODE_ENV=test
      export SERVICE_NAME=ci-test-service
      export PORT=3000
      export MONGO_USER=test-user
      export MONGO_PASS=test-pass
      export MONGO_HOST=localhost
      export MONGO_DB_NAME=test-db
      export REDIS_HOST=localhost
      export DEBUG_ERRORS=false
      export LOG_SENSITIVE=false

      # Initialize test results tracking
      TOTAL_TESTS=0
      PASSED_TESTS=0
      FAILED_TESTS=0
      TEST_RESULTS_DIR="$(Build.ArtifactStagingDirectory)/test-results"
      mkdir -p "$TEST_RESULTS_DIR"

      # Run tests for each package that has them
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          package_name=$(basename "$package_dir")
          echo "Checking for tests in ${package_name}..."

          cd "${package_dir}"

          # Check if package has test script and test files
          if grep -q '"test"' package.json && [ -d "src/__tests__" ]; then
            echo "🧪 Running tests for ${package_name}..."

            # Run tests and capture results
            if bun test --reporter=verbose 2>&1 | tee "${TEST_RESULTS_DIR}/${package_name}-test-output.log"; then
              echo "✅ Tests passed for ${package_name}"
              PASSED_TESTS=$((PASSED_TESTS + 1))
            else
              echo "❌ Tests failed for ${package_name}"
              FAILED_TESTS=$((FAILED_TESTS + 1))
            fi

            TOTAL_TESTS=$((TOTAL_TESTS + 1))
          else
            echo "⏭️ No tests found for ${package_name}"
          fi

          cd - > /dev/null
        fi
      done

      # Generate test summary
      cat > "${TEST_RESULTS_DIR}/test-summary.md" << EOF
      # 🧪 Test Results Summary

      ## 📊 Test Statistics
      - **Total Packages Tested:** $TOTAL_TESTS
      - **Passed:** $PASSED_TESTS
      - **Failed:** $FAILED_TESTS
      - **Success Rate:** $(( TOTAL_TESTS > 0 ? (PASSED_TESTS * 100) / TOTAL_TESTS : 0 ))%

      ## 📋 Test Environment
      - **Environment:** test
      - **Node Environment:** test
      - **Debug Errors:** false
      - **Log Sensitive:** false

      ## 📦 Tested Packages
      $(find packages/ -name "__tests__" -type d | sed 's|packages/\([^/]*\)/.*|- \1|' | sort | uniq)
      EOF

      echo "Test Summary:"
      cat "${TEST_RESULTS_DIR}/test-summary.md"

      # Fail the build if any tests failed
      if [ $FAILED_TESTS -gt 0 ]; then
        echo "❌ $FAILED_TESTS package(s) had test failures"
        exit 1
      else
        echo "✅ All tests passed successfully"
      fi
    displayName: 'Run Package Tests'
    env:
      ENV: test
      NODE_ENV: test