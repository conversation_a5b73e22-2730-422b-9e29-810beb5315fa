# This template installs all workspace dependencies using Bun.
# Prerequisites: Node.js, Bun, and Azure Artifacts authentication should be handled in setup-environment.yaml
steps:
  - script: |
      echo "Installing project dependencies using Bun..."
      echo "Current directory: $(pwd)"
      echo "Workspace contents:"
      ls -la
      echo "Packages folder contents:"
      ls -la packages/ || echo "Packages folder not found"
      
      # Show authentication status
      echo "Checking authentication setup..."
      if [ -f ".npmrc" ]; then
        echo "✅ Root .npmrc exists"
        grep "registry=" .npmrc || echo "No registry found"
        grep "always-auth" .npmrc || echo "No always-auth found"
      else
        echo "❌ Root .npmrc not found"
        exit 1
      fi

      if [ -f "$HOME/.npmrc" ]; then
        echo "✅ Home .npmrc exists"
      else
        echo "❌ Home .npmrc not found"
        exit 1
      fi

      rm -f bun.lock
      echo "Installing root dependencies..."
      bun install
      echo "✅ Root dependencies installed successfully"
    displayName: 'Install Project Dependencies (Bun)'

  - script: |
      echo "Installing dependencies in packages..."

      # Ensure we have the authenticated .npmrc available
      ROOT_NPMRC="$(pwd)/.npmrc"
      HOME_NPMRC="$HOME/.npmrc"

      if [ ! -f "$ROOT_NPMRC" ]; then
        echo "❌ Root .npmrc not found at $ROOT_NPMRC"
        exit 1
      fi

      if [ ! -f "$HOME_NPMRC" ]; then
        echo "❌ Home .npmrc not found at $HOME_NPMRC"
        exit 1
      fi

      echo "✅ Authentication files verified"
      echo "Root .npmrc exists: $(ls -la "$ROOT_NPMRC")"
      echo "Home .npmrc exists: $(ls -la "$HOME_NPMRC")"

      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          echo "Installing dependencies in ${package_dir}"

          # Copy authenticated .npmrc to package directory
          cp "$ROOT_NPMRC" "${package_dir}.npmrc"
          echo "✅ Copied .npmrc to ${package_dir}"

          cd "${package_dir}"

          # Verify .npmrc exists in package directory
          if [ -f ".npmrc" ]; then
            echo "✅ .npmrc found in package directory"
            # Show registry configuration (without showing token)
            grep "registry=" .npmrc || echo "No registry found in .npmrc"
          else
            echo "❌ .npmrc not found in package directory"
            exit 1
          fi

          # Install with verbose output for debugging
          echo "Running: bun install"
          bun install || {
            echo "❌ Failed to install in ${package_dir}"
            echo "Package directory contents:"
            ls -la
            echo "Package .npmrc contents:"
            cat .npmrc | head -5
            exit 1
          }

          echo "✅ Successfully installed dependencies in ${package_dir}"
          cd - > /dev/null
        fi
      done
    displayName: 'Install Package Dependencies'

  - script: |
      echo "Cleaning up .npmrc files from package directories..."
      for package_dir in packages/*/; do
        if [ -f "${package_dir}.npmrc" ]; then
          rm -f "${package_dir}.npmrc"
          echo "✅ Removed .npmrc from ${package_dir}"
        fi
      done
      echo "✅ Cleanup completed"
    displayName: 'Cleanup Package .npmrc Files'