# Generate project structure documentation and set pipeline variables
steps:
    # Generate the file structure map
    - script: |
          echo "Generating enhanced project structure analysis..."
          set -e
          bun run analyze:structure:sonnet
          echo "✅ Structure analysis completed."
      displayName: 'Generate Enhanced File Structure Map'
      workingDirectory: '$(System.DefaultWorkingDirectory)'

    - script: |
        echo "Looking for generated structure files..."
        EXPECTED_FILENAME_PATTERN="*enhanced_structure.md"
          
        STRUCTURE_FILE=$(find "$(Build.SourcesDirectory)" -maxdepth 1 -name "$EXPECTED_FILENAME_PATTERN" -type f | head -1)
          
        if [ -z "$STRUCTURE_FILE" ]; then
          echo "❌ No structure file found matching '$EXPECTED_FILENAME_PATTERN'!"
          ls -la "$(Build.SourcesDirectory)"/*.md 2>/dev/null || echo "No .md files found."
          exit 1
        fi

        echo "✅ Structure file found: $STRUCTURE_FILE"
        FILENAME=$(basename "$STRUCTURE_FILE")
        
        # Set pipeline variables for subsequent tasks
        echo "##vso[task.setvariable variable=StructureFileName]$FILENAME"
        echo "##vso[task.setvariable variable=StructureFilePath]$STRUCTURE_FILE"
        
        echo "--- File Preview (first 20 lines) ---"
        head -20 "$STRUCTURE_FILE"
        echo "--- End Preview ---"
      displayName: 'Verify Structure File and Set Variables'