parameters:
  - name: shouldPublish
    type: boolean
    default: false

steps:
  - script: |
      echo "=== Checking for Version Changes ==="
      
      # Function to get version from package.json
      get_version() {
        if [ -f "$1/package.json" ]; then
          node -p "require('$1/package.json').version"
        else
          echo "0.0.0"
        fi
      }
      
      # Check each package for version changes
      PACKAGES_TO_PUBLISH=""
      
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          PACKAGE_NAME=$(basename "$package_dir")
          CURRENT_VERSION=$(get_version "$package_dir")
          
          echo "Checking $PACKAGE_NAME (v$CURRENT_VERSION)..."
          
          # Check if this version exists in git history
          LAST_TAG=$(git tag -l "${PACKAGE_NAME}-v*" | sort -V | tail -1)
          
          if [ -z "$LAST_TAG" ]; then
            echo "🆕 No previous version found for $PACKAGE_NAME - will publish"
            PACKAGES_TO_PUBLISH="$PACKAGES_TO_PUBLISH $PACKAGE_NAME:$CURRENT_VERSION"
          else
            LAST_VERSION=$(echo "$LAST_TAG" | sed "s/${PACKAGE_NAME}-v//")
            if [ "$CURRENT_VERSION" != "$LAST_VERSION" ]; then
              echo "📦 Version changed for $PACKAGE_NAME: $LAST_VERSION → $CURRENT_VERSION"
              PACKAGES_TO_PUBLISH="$PACKAGES_TO_PUBLISH $PACKAGE_NAME:$CURRENT_VERSION"
            else
              echo "✅ No version change for $PACKAGE_NAME"
            fi
          fi
        fi
      done
      
      if [ -n "$PACKAGES_TO_PUBLISH" ]; then
        echo "##vso[task.setvariable variable=PackagesToPublish]$PACKAGES_TO_PUBLISH"
        echo "##vso[task.setvariable variable=ShouldPublish]true"
        echo "📦 Packages to publish: $PACKAGES_TO_PUBLISH"
      else
        echo "##vso[task.setvariable variable=ShouldPublish]false"
        echo "ℹ️ No packages need publishing"
      fi
    displayName: 'Check Version Changes'
    condition: eq('${{ parameters.shouldPublish }}', true)