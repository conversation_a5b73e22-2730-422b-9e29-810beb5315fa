# Pipeline step for running tests across all packages
# Handles environment setup, test execution, and result reporting

parameters:
  - name: testEnvironment
    type: string
    default: 'test'
  - name: enableVerboseLogging
    type: boolean
    default: false
  - name: enableSensitiveLogging
    type: boolean
    default: false

steps:
  - script: |
      echo "🧪 Setting up test environment..."
      
      # Set up comprehensive test environment variables
      export ENV=${{ parameters.testEnvironment }}
      export NODE_ENV=${{ parameters.testEnvironment }}
      export SERVICE_NAME=ci-test-service
      export PORT=3000
      export MONGO_USER=test-user
      export MONGO_PASS=test-pass
      export MONGO_HOST=localhost
      export MONGO_DB_NAME=test-db
      export REDIS_HOST=localhost
      export DEBUG_ERRORS=${{ parameters.enableVerboseLogging }}
      export LOG_SENSITIVE=${{ parameters.enableSensitiveLogging }}
      
      echo "✅ Test environment configured:"
      echo "  - ENV: $ENV"
      echo "  - NODE_ENV: $NODE_ENV"
      echo "  - DEBUG_ERRORS: $DEBUG_ERRORS"
      echo "  - LOG_SENSITIVE: $LOG_SENSITIVE"
    displayName: 'Setup Test Environment'
    env:
      ENV: ${{ parameters.testEnvironment }}
      NODE_ENV: ${{ parameters.testEnvironment }}

  - script: |
      echo "🔍 Discovering test suites..."
      
      # Initialize test tracking
      TOTAL_PACKAGES=0
      PACKAGES_WITH_TESTS=0
      TEST_RESULTS_DIR="$(Build.ArtifactStagingDirectory)/test-results"
      mkdir -p "$TEST_RESULTS_DIR"
      
      # Discover packages with tests
      echo "Packages with test suites:" > "${TEST_RESULTS_DIR}/test-discovery.log"
      
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          package_name=$(basename "$package_dir")
          TOTAL_PACKAGES=$((TOTAL_PACKAGES + 1))
          
          # Check for test configuration
          cd "${package_dir}"
          HAS_TEST_SCRIPT=$(grep -q '"test"' package.json && echo "true" || echo "false")
          HAS_TEST_DIR=$([ -d "src/__tests__" ] && echo "true" || echo "false")
          TEST_FILE_COUNT=$(find src/__tests__ -name "*.test.ts" 2>/dev/null | wc -l || echo "0")
          
          if [ "$HAS_TEST_SCRIPT" = "true" ] && [ "$HAS_TEST_DIR" = "true" ] && [ "$TEST_FILE_COUNT" -gt 0 ]; then
            echo "✅ ${package_name}: ${TEST_FILE_COUNT} test files" | tee -a "${TEST_RESULTS_DIR}/test-discovery.log"
            PACKAGES_WITH_TESTS=$((PACKAGES_WITH_TESTS + 1))
          else
            echo "⏭️ ${package_name}: No tests" | tee -a "${TEST_RESULTS_DIR}/test-discovery.log"
          fi
          
          cd - > /dev/null
        fi
      done
      
      echo ""
      echo "📊 Test Discovery Summary:"
      echo "  - Total Packages: $TOTAL_PACKAGES"
      echo "  - Packages with Tests: $PACKAGES_WITH_TESTS"
      echo "  - Packages without Tests: $((TOTAL_PACKAGES - PACKAGES_WITH_TESTS))"
      
      # Set variables for next steps
      echo "##vso[task.setvariable variable=PackagesWithTests]$PACKAGES_WITH_TESTS"
      echo "##vso[task.setvariable variable=TotalPackages]$TOTAL_PACKAGES"
    displayName: 'Discover Test Suites'

  - script: |
      echo "🧪 Running test suites..."
      
      # Test execution tracking
      PASSED_PACKAGES=0
      FAILED_PACKAGES=0
      SKIPPED_PACKAGES=0
      TEST_RESULTS_DIR="$(Build.ArtifactStagingDirectory)/test-results"
      
      # Set test environment variables
      export ENV=${{ parameters.testEnvironment }}
      export NODE_ENV=${{ parameters.testEnvironment }}
      export SERVICE_NAME=ci-test-service
      export PORT=3000
      export MONGO_USER=test-user
      export MONGO_PASS=test-pass
      export MONGO_HOST=localhost
      export MONGO_DB_NAME=test-db
      export REDIS_HOST=localhost
      export DEBUG_ERRORS=${{ parameters.enableVerboseLogging }}
      export LOG_SENSITIVE=${{ parameters.enableSensitiveLogging }}
      
      # Run tests for each package
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          package_name=$(basename "$package_dir")
          echo ""
          echo "🔍 Processing ${package_name}..."
          
          cd "${package_dir}"
          
          # Check if package has tests
          if grep -q '"test"' package.json && [ -d "src/__tests__" ]; then
            echo "🧪 Running tests for ${package_name}..."
            
            # Create package-specific test output file
            TEST_OUTPUT_FILE="${TEST_RESULTS_DIR}/${package_name}-test-output.log"
            
            # Run tests with timeout and capture all output
            if timeout 300 bun test > "$TEST_OUTPUT_FILE" 2>&1; then
              echo "✅ Tests PASSED for ${package_name}"
              PASSED_PACKAGES=$((PASSED_PACKAGES + 1))
              
              # Extract test statistics if available
              if grep -q "pass\|fail" "$TEST_OUTPUT_FILE"; then
                echo "📊 Test Results:" | tee -a "$TEST_OUTPUT_FILE"
                grep -E "(pass|fail|✓|✗)" "$TEST_OUTPUT_FILE" | tail -5 | tee -a "$TEST_OUTPUT_FILE"
              fi
            else
              echo "❌ Tests FAILED for ${package_name}"
              FAILED_PACKAGES=$((FAILED_PACKAGES + 1))
              
              # Show last few lines of output for debugging
              echo "🔍 Last 10 lines of test output:"
              tail -10 "$TEST_OUTPUT_FILE"
            fi
          else
            echo "⏭️ No tests found for ${package_name}"
            SKIPPED_PACKAGES=$((SKIPPED_PACKAGES + 1))
          fi
          
          cd - > /dev/null
        fi
      done
      
      # Calculate success rate
      TOTAL_TESTED=$((PASSED_PACKAGES + FAILED_PACKAGES))
      SUCCESS_RATE=0
      if [ $TOTAL_TESTED -gt 0 ]; then
        SUCCESS_RATE=$(( (PASSED_PACKAGES * 100) / TOTAL_TESTED ))
      fi
      
      # Generate comprehensive test summary
      cat > "${TEST_RESULTS_DIR}/test-summary.md" << EOF
      # 🧪 Test Execution Summary
      
      ## 📊 Test Results
      - **Total Packages:** $(TotalPackages)
      - **Packages Tested:** $TOTAL_TESTED
      - **Passed:** $PASSED_PACKAGES ✅
      - **Failed:** $FAILED_PACKAGES ❌
      - **Skipped:** $SKIPPED_PACKAGES ⏭️
      - **Success Rate:** $SUCCESS_RATE%
      
      ## 🔧 Test Environment
      - **Environment:** ${{ parameters.testEnvironment }}
      - **Node Environment:** ${{ parameters.testEnvironment }}
      - **Debug Errors:** ${{ parameters.enableVerboseLogging }}
      - **Log Sensitive:** ${{ parameters.enableSensitiveLogging }}
      - **Build Number:** $(Build.BuildNumber)
      - **Source Branch:** $(Build.SourceBranchName)
      
      ## 📦 Package Results
      EOF
      
      # Add individual package results
      for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
          package_name=$(basename "$package_dir")
          cd "${package_dir}"
          
          if grep -q '"test"' package.json && [ -d "src/__tests__" ]; then
            if [ -f "${TEST_RESULTS_DIR}/${package_name}-test-output.log" ]; then
              if grep -q "✅" "${TEST_RESULTS_DIR}/${package_name}-test-output.log" 2>/dev/null; then
                echo "- **${package_name}:** ✅ PASSED" >> "${TEST_RESULTS_DIR}/test-summary.md"
              else
                echo "- **${package_name}:** ❌ FAILED" >> "${TEST_RESULTS_DIR}/test-summary.md"
              fi
            fi
          else
            echo "- **${package_name}:** ⏭️ NO TESTS" >> "${TEST_RESULTS_DIR}/test-summary.md"
          fi
          
          cd - > /dev/null
        fi
      done
      
      echo ""
      echo "📋 Final Test Summary:"
      cat "${TEST_RESULTS_DIR}/test-summary.md"
      
      # Set pipeline variables for reporting
      echo "##vso[task.setvariable variable=TestsPassed]$PASSED_PACKAGES"
      echo "##vso[task.setvariable variable=TestsFailed]$FAILED_PACKAGES"
      echo "##vso[task.setvariable variable=TestsSkipped]$SKIPPED_PACKAGES"
      echo "##vso[task.setvariable variable=TestSuccessRate]$SUCCESS_RATE"
      
      # Fail the pipeline if any tests failed
      if [ $FAILED_PACKAGES -gt 0 ]; then
        echo ""
        echo "❌ Pipeline failed: $FAILED_PACKAGES package(s) had test failures"
        echo "##vso[task.logissue type=error]$FAILED_PACKAGES package(s) failed tests"
        exit 1
      else
        echo ""
        echo "✅ All tests passed successfully!"
      fi
    displayName: 'Execute Tests'
    env:
      ENV: ${{ parameters.testEnvironment }}
      NODE_ENV: ${{ parameters.testEnvironment }}
      DEBUG_ERRORS: ${{ parameters.enableVerboseLogging }}
      LOG_SENSITIVE: ${{ parameters.enableSensitiveLogging }}

  - task: PublishTestResults@2
    displayName: 'Publish Test Results'
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '$(Build.ArtifactStagingDirectory)/test-results/**/*.xml'
      failTaskOnFailedTests: false
      testRunTitle: 'Introspection Shared Packages Tests'
    condition: always()
    continueOnError: true
