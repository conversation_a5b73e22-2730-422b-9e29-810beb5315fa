-----------------------
Zod parse vs safeParse:
-----------------------
parse():
- Is used when you expect the data to be Valid and want the code to throw an error if it's not
- e.g. In internal functions or when you've already validated the data elsewhere.

safeParse():
- Is used when you need to handle potential validation errors gracefully,
- and provide meaningful error messages to the client (e.g. in API controllers).

-----------------
Key Differences:
----------------
parse():
- Success:              Returns the validated data if successful.
- Failure:              Throws a 'ZodError' if validation fails.
- Error Handling:       Requires a 'try...catch' block,
- Use Cases:            When you're certain the data is valid.
- Error Information:    'ZodError' is thrown.

safeParse():
- Success:              Returns an object: { success: true,  data: validatedData }
- Failure:              Returns an object: { success: false, error: ZodError     }
- Error Handling:       Requires checking the success property of the returned object
- Use Cases:            When you want to handle potential validation errors gracefully.
- Error Information:    'ZodError' is available in the 'validationResult.error' property.