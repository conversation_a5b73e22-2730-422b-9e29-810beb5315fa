What is CORS & How is it used?
------------------------------
- CORS (Cross-Origin Resource Sharing) is a security feature implemented by browsers that restrict web pages from making
    requests to a different domain than the one that is served to the original page.
- Without proper CORS configuration, browsers will block requests from your FE applications to your IdP APO, if hosted on different domains.

What is Helmet & How is it used?
--------------------------------
- Helmet is a collection of middleware functions that help secure Express applications by setting various HTTP headers.
- It helps protect your application from well-known web vulnerabilities by setting appropriate HTTP headers.


MISCELLANEOUS TERMS:
--------------------
frame:
- Refers to a way to embed one HTML document within another, created with the <iframe> tag (inline-frame).
- <iframe> is used to embed a separate webpage within a current page, by creating a rectangular region within your page
    where another HTML document can be displayed.
- e.g. <iframe src="https://www.example.com" width="500" height="300" />

why can frames be a security risk?
- <iframe>'s can be exploited through clickjacking:
-- Malicious website: Attacker can create a website that visually overlays a transparent <iframe> on top of a legitimate website.
-- Hidden Target:     The <iframe> loads a page from a legitimate website that performs sensitive actions.
--Malicious Actions:  The attacker can thus make the user perform actions on the legitimate website without their knowledge or consent.
-- User Deception:    The attacker tricks the user into clicking buttons or links on their website, but because of the overlay,
                        the user is actually interacting with the hidden legitimate website inside the <iframe>.

referrer header:
- This header indicates the URL of the page that was linked to the currently requested resource.
- In other words, it tells the server where the user WAS coming from when they clicked a link/submitted a form to reach the current page.
-- Use Cases: Analytics, Security, Logging, Content Personalization, CSRF Protection (Cross-Site Request Forgery)

X-Requested-With header:
- A non-standard header that is often added by JS libraries (JQuery, Axios etc.) when making async HTTP requests (AJAX requests).
- It is used to indicate that the request was initiated via JavaScript.

XHR (XMLHttpRequest):
- A browser API that allows JS to make HTTP requests to a server asynchronously.
- Was initially designed to retrieve XML data, however it can now be used to transfer any type of data (SON, text, binary, etc.)
