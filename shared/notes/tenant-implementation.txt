Implementing a Tenant Provider:
-------------------------------
- A database collection is used to store tenant information (e.g. Tenants), essential to:
-- This is used to store tenant names, unique codes (if used), Id's, status (active/inactive), and any tenant-specific settings.
-- Link users (IdentityModel) to their respective tenants via the tenantId field.
-- Look up tenant details during login or tenant resolution.

What is a Tenant? (in our context):
------------------------------------
- Think of a "tenant" as a distinct entity using your application
- Hence, we will have the following tenants:
-- 1. A Company
-- 2. An Organization (Companies & Individuals will belong to this)
-- 3. An Admin
-- 4. A User (Individual user)

1. Company User:
----------------
- The company itself will be the Tenant.
- All data entered by users belonging to that company stays within that company's tenant boundary.

2. Organization User:
---------------------
- This is a special case, they do not represent a single data boundary themselves, but rather have the ability to manage multiple Company Tenants.

3. Admin User:
--------------
- This user operates above the tenant level, and has access to all tenants.

4. Individual User:
-------------------
- The individual user effectively is their OWN tenant.
- Their data is isolated to just them.