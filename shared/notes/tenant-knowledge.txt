What is a Tenant?
-----------------
- A tenant is a separate entity that shares the same application/infrastructure but operates in isolation from other tenants.
--Examples of tenants include: A company, an organization, a department, or a specific user.
- As such, each tenant has its own set of users, permissions and data, ensuring privacy and security.

IdP's role as a Tenant:
- The IdP manages authentication and authorization for users across tenants.
- It ensures that users are authenticated and assigned the correct roles/permissions based on their tenant.

Finance instance's role as a Tenant:
- The main finance instance is the shared application/service that serves all tenants.
- It uses the IdP to verify user identities and enforce tenant-specific access controls.
- As such, this instance must be designed to handle tenant isolation, ensuring that data & operations for one tenant do not affect others.


Understanding Tenant Providers (in C#):
--------------------------------------
- In multi-tenant applications, a "tenant provider" manages tenant context throughout the application.
- C# often uses claims-based authentication, where tenant information is stored as claims within the identity.
-- These claims are globally accessible through the current user's context.
-- ASP.NET Core offers middleware that automatically resolves tenant information from claims.


Understanding Tenant Providers (in JavaScript):
-----------------------------------------------
Core Concept:
- A tenant provider's job is to figure out which tenant a specific incoming request belongs to.
- In a multi-tenant application, different users belong to different organizations (tenants) and their data needs to be isolated.

Resolution Strategies:
- The provider uses a specific strategy to identify the tenant.

-- JWT Payload (current approach):
--- After a user logs in, the tenantId is embedded within the JWT.
--- Subsequent requests include this JWT, and the provider extracts the tenantId from the verified token.
--- This is very common for API-based systems.

-- Subdomain:
--- Each tenant gets a unique subdomain (e.g. tenantA.yourapp.com, tenantB.yourapp.com)
--- The provider extracts the tenant identifier (tenantA, tenantB) from the request's host.

-- URL Path:
--- The tenant identifier is part of the URL path (e.g. yourapp.com/tenantA/data, yourapp.com/tenantB/data)

-- HTTP Header:
--- A custom header (e.g. x-tenant-id) is sent with each request containing the tenant identifier.

-- Query Parameter:
--- The tenant Id could be in a query parameter (e.g. yourapp.com/data?tenantId=tenantA)
--- Less common for security/consistency reasons.