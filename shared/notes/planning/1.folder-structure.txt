identity-provider/
├── src/
│   ├── models/
│   │   └── user.model.ts       // Your user model (Typegoose)
│   ├── services/
│   │   ├── password.util.service.ts // Password hashing/validation
│   │   ├── auth.service.ts      // Authentication logic (login, register)
│   │   └── token.service.ts     // JWT token generation/verification
│   ├── controllers/
│   │   └── auth.controller.ts   // Route handlers for authentication
│   ├── routes/
│   │   └── auth.routes.ts       // Defines authentication routes
│   ├── middleware/
│   │   └── auth.middleware.ts   // Middleware for protected routes
│   ├── utils/
│   │   └── validation.util.ts  // Validation utilities
│   ├── config/
│   │   └── db.config.ts         // Database connection config
│   ├── index.ts                // Main entry point (Bun server)
├── .env                        // Environment variables
├── package.json
├── bun.lock
└── README.md