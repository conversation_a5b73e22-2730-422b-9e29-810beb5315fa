Processes to Authenticate Requests/Actions from the Finance-Repo (FAD):
-----------------------------------------------------------------------
1. User Attempts to Access a Protected Resource:
    - User tries to access a protected page or API endpoint in the Finance Analytics Dashboard (FAD)

2. Dashboard Checks for Authentication:
    - The FAD checks if the user is already authenticated,
    - By checking for a valid JWT in a cookie or the Authorization header.

3. If Not Authentication, Redirect to Login Page:
    - If the user is not authenticated, the FAD redirects them to the login page, of the IdP.

4. User Enters Credentials on the IdP:
    - The user then enters their credentials (email, password) on the IdP's login form.

5. IdP Authenticates User:
    - The IdP verifies the user's credentials against the stored data in the database.

6. IdP Issues a JWT:
    - If the credentials are correct, the IdP generates a JWT.

7. IdP Returns JWT to the Client:
    - The IdP sends the JWT back to the client (users browser)
    - This can be done in a few ways:
        a. HTTP Redirect with Token in Query Params (less secure)
            => e.g. http://localhost:3000/dashboard?token=eyJhbGciOiJIU...

        b. Setting a Cookie (if on the same domain/subdomain)
            => If the IdP & FAD are on the same domain/subdomain, the IdP can set an HTTP-only cookie containing the JWT.
            => The browser will then automatically send this cookie with every request to the FAD.

        c. Returning a JSON Response (most common & secure)
            => The IdP can return a JSON response to the client containing the JWT.
            => e.g. { "token": "eyJhbGciOiJIU..." }

8. Client Stores the JWT:
    - The client (JS code running in browser) receives the JWT and stores it securely,
    - e.g. In local storage or preferably in a secure HTTP-only cookie.

9. Client Sends JWT with Subsequent Requests:
    - For every subsequent request to the FAD,the client includes the JWT in the Authorization header.
    - e.g. Authorization: Bearer eyJhbGciOiJIU...

10. Dashboard Verifies the JWT:
    - The FAD receives the request, extracts the JWT from the Authorization header,
    - Then verifies its signature using the IdP's public key (or shared secret).

11. Dashboard Authorizes the User:
    - If the JWT is valid, the FAD extracts the user's information (e.g. user id, roles...) from the JWT claims.
    - It then uses this information to determine what resources the user is allowed to access.

12. Dashboard Processes the Request:
    - If the user is authorized, the FAD processes the request and returns the appropriate response.

SUMMARY:
--------
- The identity provider handles the authentication part (verifying who the user is).
- The finance analytics dashboard handles the authorization part (determining what the user is allowed to do based on their identity).
- The JWT acts as a secure, verifiable credential that is passed between the IdP and the dashboard.

This separation of concerns makes your system more secure, maintainable, and scalable.