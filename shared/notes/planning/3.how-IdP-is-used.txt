The IdP repo will act as a mini-IdP for the main Finance Analytics repo.

It will handle the following:
-----------------------------
1. User Registration:
    - New users will register an account through the IdP.
    - This involves collecting user information, validating data and storing it securely in the db.

2. User Login:
    - Existing users will log in to the IdP.
    - This involves verifying their credentials (email, password) against stored data.

3. Token Issuance:
    - Upon successful login, the IdP will generate JSON Web Token (JWT).
    - The JWT will contain claims (e.g. user id, email) that identifies the user.
    - The token is digitally signed by the IdP, so the receiving application can verify its integrity & authenticity.

4. Token Verification:
    - The finance-repo will receive the JWT from the client (Authorization header).
    - It will then use the IdP's public key (or shared secret) to verify that the token is valid and has not been tampered with.

5. Authorization:
    - After verifying the token, the finance-repo can use the claims in the token to determine what the user is authorized to do.
    - e.g. There might be different roles (admin, read-only user etc.) with different permissions.