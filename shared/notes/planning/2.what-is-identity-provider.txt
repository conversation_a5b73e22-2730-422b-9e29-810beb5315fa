
What is an Identity Provider (IdP)?
------------------------------------
- An identity provider is a service that manages digital identities and provides authentication as a service.
- Think of it as a central authority responsible for verifying who a user is.
- Instead of every application handling its own user accounts, passwords, and authentication logic,
   they can delegate this responsibility to a dedicated IdP.

Benefits of using an IdP:
-------------------------
Centralized Authentication:
- Manages user authentication in one place.
- This simplifies user management and improves security.

Single Sign-On (SSO):
- Users can log in once to the IdP and access multiple applications without re-entering their credentials.

Improved Security:
- The IdP can implement strong security measures centrally, protecting all connected applications.
- e.g. multi-factor authentication (MFA), password policies

Simplified Development:
- Applications don't have to worry about complex authentication logic. They simply trust the IdP.

Compliance:
- Helps meet regulatory requirements for user authentication and data security.