You've provided a very comprehensive set of configurations for your Excelytics project, <PERSON><PERSON>! This setup is excellent for ensuring code quality and consistency across your microservices.

Let's first clarify the `endOfLine` Prettier option and then move into the detailed documentation of your linting and Prettier configurations.

### Understanding Prettier's `endOfLine` Option

The `endOfLine` option in <PERSON><PERSON><PERSON> controls which **newline character** is used at the end of files. <PERSON><PERSON><PERSON>'s core design principle is to be opinionated and enforce consistent formatting, and this includes **always ensuring that there is exactly one newline character at the end of every file.**

**You cannot configure <PERSON><PERSON><PERSON> to *not* add a newline at the end of files.** This behavior (ensuring a single trailing newline) is fundamental to <PERSON><PERSON><PERSON> and is considered a best practice for several reasons:

1.  **Git Diffs:** Prevents "No newline at end of file" warnings and makes diffs cleaner when adding new lines at the end of a file.
2.  **Tool Compatibility:** Many build tools, linters, and Unix-like systems expect files to end with a newline.
3.  **Consistency:** Ensures uniform file endings across different operating systems and development environments.

The `endOfLine` option only dictates the *type* of newline character:

*   **`"lf"` (Line Feed, `\n`)**: (Default) Unix-style line endings. This is the most common and recommended setting for cross-platform development, especially for projects committed to Git.
*   **`"crlf"` (Carriage Return + Line Feed, `\r\n`)**: Windows-style line endings.
*   **`"cr"` (Carriage Return, `\r`)**: Old Mac-style line endings (rarely used now).
*   **`"auto"`**: Prettier will check the first line ending it finds in a file and apply that style. If no line endings are present, it defaults to `"lf"`. This can lead to inconsistencies if developers work on files with mixed line endings.

**Your current setting `endOfLine: "lf"` is the most robust and recommended choice.** It ensures Unix-style newlines and is what most modern development environments prefer. Prettier will, with this setting, always ensure a single `\n` at the end of every file it processes.

---

### Comprehensive Documentation: Linting and Formatting Configuration

This document outlines the linting, formatting, and type-checking setup for the `Introspection.Finance.Identity` microservice. This configuration ensures consistent code style, identifies potential errors, enforces best practices, and maintains type safety across the project, which is crucial for a microservices architecture.

#### **Summary**

This project leverages a robust set of tools for code quality:

*   **Prettier**: For consistent, automated code formatting across all supported file types (TypeScript, JSON, Vue, etc.), ensuring a uniform aesthetic without manual intervention.
*   **ESLint**: For identifying and enforcing coding standards, detecting potential bugs, and ensuring adherence to best practices in JavaScript and TypeScript code. It integrates `typescript-eslint` for TS-specific checks, `eslint-plugin-import` for import conventions, and `eslint-plugin-perfectionist` for code structure and sorting.
*   **TypeScript (`tsc`)**: For compile-time type checking, enhancing code reliability and maintainability.
*   **Bun Test Runner**: For efficient unit and integration testing, with various CLI options for focused, watched, or coverage-based test runs.

Together, these tools form a powerful static analysis pipeline that promotes high-quality, maintainable, and error-free code.

---

#### **1. Code Formatting with Prettier**

Prettier is an opinionated code formatter that enforces a consistent style by parsing your code and re-printing it with its own rules. It eliminates bikeshedding over code style within the team.

*   **Configuration File:** `.prettierrc`
*   **Ignored Files:** `.prettierignore`

##### **1.1. `.prettierrc` - Formatting Rules**

This JSON file defines Prettier's core formatting rules.

```json
{
	"$schema": "https://json.schemastore.org/prettierrc",
	"semi": true,
	"tabWidth": 4,
	"useTabs": true,
	"printWidth": 120,
	"singleQuote": true,
	"quoteProps": "as-needed",
	"trailingComma": "none",
	"bracketSpacing": true,
	"bracketSameLine": false,
	"arrowParens": "avoid",
	"endOfLine": "lf",
	"proseWrap": "preserve",
	"htmlWhitespaceSensitivity": "css",
	"embeddedLanguageFormatting": "auto",
	"singleAttributePerLine": false,
	"requirePragma": false,
	"insertPragma": false,
	"overrides": [
		{
			"files": ["*.json", "*.jsonc", "tsconfig*.json"],
			"options": {
				"tabWidth": 4,
				"useTabs": true
			}
		},
		{
			"files": ["*.md", "*.mdx"],
			"options": {
				"tabWidth": 4,
				"useTabs": true,
				"printWidth": 120,
				"proseWrap": "never"
			}
		},
		{
			"files": ["*.yml", "*.yaml"],
			"options": {
				"tabWidth": 2,
				"useTabs": false
			}
		}
	]
}
```

**Key Options Explained:**

*   `$schema`: Provides JSON schema for auto-completion and validation in editors.
*   `semi`: (`true`) Print semicolons at the end of statements.
*   `tabWidth`: (`4`) Specifies the number of spaces per indentation level.
*   `useTabs`: (`true`) Indent using tabs instead of spaces.
*   `printWidth`: (`120`) Specifies the line length that Prettier will try to wrap lines at.
*   `singleQuote`: (`true`) Use single quotes instead of double quotes for string literals.
*   `quoteProps`: (`"as-needed"`) Only add quotes around object properties when necessary.
*   `trailingComma`: (`"none"`) No trailing commas in array/object literals.
*   `bracketSpacing`: (`true`) Print spaces between brackets in object literals (`{ foo: bar }`).
*   `bracketSameLine`: (`false`) Put the `>` of a multi-line HTML (or JSX) element on a new line.
*   `arrowParens`: (`"avoid"`) Omit parentheses for single arrow function parameters (`x => x`).
*   `endOfLine`: (`"lf"`) Ensures Unix-style line endings (`\n`). Prettier will *always* ensure a single newline character at the end of files it processes for consistency and tool compatibility.
*   `proseWrap`: (`"preserve"`) How to wrap prose (like Markdown text). `preserve` wraps based on `printWidth`.
*   `htmlWhitespaceSensitivity`: (`"css"`) Handles whitespace in HTML, Vue, Angular, and Handlebars. `css` means the default value of CSS `display` property is used to determine whitespace sensitivity.
*   `embeddedLanguageFormatting`: (`"auto"`) Format embedded code (e.g., in Markdown backticks or Vue templates).
*   `singleAttributePerLine`: (`false`) Do not force single HTML attributes onto a new line if `printWidth` is not exceeded.
*   `requirePragma`: (`false`) Don't require `@prettier` pragma to format files.
*   `insertPragma`: (`false`) Don't insert `@prettier` pragma at the top of files.

**`overrides`:** This powerful feature allows specific formatting options to be applied only to files matching certain glob patterns.

*   **JSON/TSConfig:** Uses 4-space tabs (consistent with main config).
*   **Markdown/MDX:** Uses 4-space tabs and `proseWrap: "never"` to prevent Prettier from re-wrapping markdown text.
*   **YAML:** Uses 2-space indentation with spaces (not tabs) for YAML files, which is a common convention for YAML.

##### **1.2. `.prettierignore` - Files to Exclude**

This file lists files and directories that Prettier should explicitly ignore during formatting operations. This prevents Prettier from modifying generated code, build outputs, or specific files where manual formatting is preferred or not necessary.

```
# Dependencies
node_modules/
bun.lock

# Build outputs
dist/
build/
*.tsbuildinfo

# Logs
logs/
*.log

# Coverage
coverage/
*.lcov

# Environment files
.env*

# Cache files
.eslintcache
.cache/

# Generated files
types/
shared/

# Specific files to preserve formatting
CHANGELOG.md
LICENSE

# Config files with specific formatting
.gitignore
.dockerignore
Dockerfile*

# IDE and OS files
.vscode/
.idea/
.DS_Store
```

**Key Ignored Categories:**

*   **Dependencies:** `node_modules/`, `bun.lock`.
*   **Build Outputs:** `dist/`, `build/`, `.tsbuildinfo` (TypeScript build info file).
*   **Logs & Caches:** `logs/`, `.log`, `coverage/`, `.lcov`, `.eslintcache`, `.cache/`.
*   **Environment Files:** `.env*`.
*   **Generated Files:** `types/`, `shared/` (assuming these are generated outputs from a build step or shared modules that should not be formatted by this project's Prettier).
*   **Specific Files:** `CHANGELOG.md`, `LICENSE`, `.gitignore`, `.dockerignore`, `Dockerfile*` are often maintained with specific manual formatting.
*   **IDE/OS Files:** `.vscode/`, `.idea/`, `.DS_Store` are configuration files or system files irrelevant to code formatting.

---

#### **2. Code Linting with ESLint**

ESLint is used to identify problematic patterns found in JavaScript and TypeScript code, ensuring code quality, security, and adherence to defined coding standards. This project uses the new ESLint Flat Config system.

*   **Configuration File:** `eslint.config.mjs`

##### **2.1. `eslint.config.mjs` - Linting Rules**

This modular configuration imports various plugins and recommended rule sets, then adds custom rules.

```javascript
import perfectionist from 'eslint-plugin-perfectionist';
import eslintPluginImport from 'eslint-plugin-import';
import pluginNode from 'eslint-plugin-node';
import tseslint from 'typescript-eslint';
import pluginJs from '@eslint/js';
import globals from 'globals';

/** @type {import('eslint').Linter.Config[]} */
export default [
	// A dedicated, top-level ignores configuration.
	{
		ignores: ['dist/**', 'docs/**', 'logs/**', 'types/**', 'shared/**', '.eslintcache', 'node_modules/**'],
	},
	{ files: ['**/*.{js,mjs,cjs,ts}'] },
	{ languageOptions: { globals: globals.node } },
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	{
		plugins: {
			import: eslintPluginImport,
			perfectionist,
			node: pluginNode,
		},
		rules: {
			// Initial Rules
			'node/no-process-env': 'error',
			'no-mixed-spaces-and-tabs': 'error',
			'@typescript-eslint/no-explicit-any': 'warn',
			indent: ['error', 'tab', { SwitchCase: 1 }],
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all', // Check all variables
					args: 'after-used', // Ignore unused arguments after the last used argument
					argsIgnorePattern: '^_', // Ignore variables prefixed with _
					ignoreRestSiblings: true, // Ignore unused siblings in rest destructuring
				},
			],
			'no-warning-comments': [
				'warn',
				{
					terms: ['todo', 'fixme'],
					location: 'start',
				},
			],

			// --- CORRECTED 'sort-imports' RULE ---
			'perfectionist/sort-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
					groups: [['import']],
					newlinesBetween: 'always',
				},
			],
			// Sort the items inside the curly braces by length
			'perfectionist/sort-named-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
				},
			],

			// --- Keep these non-conflicting rules from eslint-plugin-import ---
			'import/order': 'off', // MUST be off to prevent conflicts
			'import/newline-after-import': ['error', { count: 1 }],
			'import/no-duplicates': 'error',
			'import/extensions': ['off', 'ignorePackages', { ts: 'never' }],

			// --- Other Quality Rules ---
			'prefer-const': 'error',
			'no-var': 'error',
			'no-console': 'off',
			'no-debugger': 'error',
			'no-unused-expressions': 'warn',
		},
		settings: {
			'import/resolver': {
				typescript: {
					// Tell the resolver where your tsconfig is
					project: './tsconfig.json',
					alwaysTryTypes: true, // Resolve type definitions as well
				},
			},
		},
	},
];
```

**Configuration Breakdown:**

1.  **Top-level `ignores`:**
	*   Defines files and directories that ESLint should *never* lint. This broadly ignores build outputs, documentation, logs, type definitions, shared modules, cache files, and `node_modules`.

2.  **File Targeting:**
	*   `{ files: ['**/*.{js,mjs,cjs,ts}'] }`: Ensures ESLint only processes JavaScript and TypeScript files.

3.  **Language Options:**
	*   `languageOptions: { globals: globals.node }`: Makes Node.js global variables (like `process`, `__dirname`, etc.) available to ESLint, preventing "no-undef" errors for them.

4.  **Recommended Base Configurations:**
	*   `pluginJs.configs.recommended`: Applies a baseline set of recommended JavaScript rules from `@eslint/js`.
	*   `...tseslint.configs.recommended`: Integrates the recommended rules from `typescript-eslint`, providing type-aware linting capabilities.

5.  **Plugins and Custom Rules:**
	*   **`plugins`**:
		*   `import: eslintPluginImport`: Provides rules for linting ES2015+ import/export syntax, preventing issues with paths, ordering, and duplication.
		*   `perfectionist`: Enforces consistent ordering of various code elements (e.g., imports, object keys) based on configurable rules.
		*   `node: pluginNode`: Rules specific to Node.js environments (e.g., `no-process-env`).
	*   **`rules`**:
		*   `node/no-process-env`: (`'error'`) Disallows direct `process.env` access, promoting a configuration library for environment variables.
		*   `no-mixed-spaces-and-tabs`: (`'error'`) Prevents mixing spaces and tabs for indentation.
		*   `@typescript-eslint/no-explicit-any`: (`'warn'`) Warns against using `any` type, encouraging more specific types.
		*   `indent`: (`['error', 'tab', { SwitchCase: 1 }]`) Enforces consistent indentation using tabs, with switch cases indented by one tab.
		*   `@typescript-eslint/no-require-imports`: (`'warn'`) Warns against `require()` statements in TypeScript, preferring ES module `import`.
		*   `@typescript-eslint/no-empty-object-type`: (`'warn'`) Warns about using `object` type (which is too broad), encouraging `Record<string, unknown>` or specific interfaces.
		*   `@typescript-eslint/no-unused-vars`: (`['warn', {...}]`) Configures warnings for unused variables, allowing `_` prefix for intentionally unused arguments and ignoring rest siblings in destructuring.
		*   `no-warning-comments`: (`['warn', {...}]`) Warns about `TODO` and `FIXME` comments, aiding in code cleanup.
		*   **`perfectionist/sort-imports`**: (`'error'`) Sorts import declarations by line length in descending order, with always a newline between import groups.
		*   **`perfectionist/sort-named-imports`**: (`'error'`) Sorts items inside curly braces of named imports by line length in descending order.
		*   **`import/order`: (`'off'`)**: Explicitly turned off because `perfectionist/sort-imports` handles import sorting and these rules would conflict.
		*   `import/newline-after-import`: (`['error', { count: 1 }]`) Ensures exactly one newline after the last import statement.
		*   `import/no-duplicates`: (`'error'`) Prevents multiple import statements from the same module.
		*   `import/extensions`: (`['off', 'ignorePackages', { ts: 'never' }]`) Manages file extension rules for imports; turned off for better compatibility, but configured to never require `.ts` extension in imports.
		*   `prefer-const`: (`'error'`) Enforces `const` over `let` where a variable is never reassigned.
		*   `no-var`: (`'error'`) Disallows `var` declarations, promoting `let` and `const`.
		*   `no-console`: (`'off'`) Allows `console.log` statements (useful during development/debugging).
		*   `no-debugger`: (`'error'`) Disallows `debugger` statements in production code.
		*   `no-unused-expressions`: (`'warn'`) Warns about unused expressions.

6.  **ESLint Settings:**
	*   `settings['import/resolver'].typescript`: Configures `eslint-plugin-import` to resolve imports using your TypeScript `tsconfig.json`, ensuring it understands your path aliases (`@/*`, `@app-types/*`) and resolves type definitions.

---

#### **3. `package.json` Scripts for Dev/Lint/Test**

The `scripts` section in `package.json` provides convenient CLI commands to automate development tasks, integrating ESLint, Prettier, TypeScript, and Bun's test runner.

```json
{
  "name": "introspection.finance.identity",
  // ... other fields
  "scripts": {
    // Development & Server Management
    "dev": "NODE_ENV=development bun --watch src/server.ts", // Starts dev server with watch mode and development environment
    "dev:local": "NODE_ENV=local bun --watch src/server.ts", // Starts dev server for local environment
    "start:staging": "NODE_ENV=staging bun --watch src/server.ts", // Starts server for staging with watch
    "start:production": "NODE_ENV=production bun run src/server.ts", // Starts server for production

    // Type Checking
    "log:tsc": "npx tsc --noEmit", // Runs TypeScript compiler to check types without emitting JS files
    "check:types": "npx tsc --noEmit", // Alias for `log:tsc`

    // Linting (ESLint)
    "lint": "eslint", // Runs ESLint on all files
    "lint:errors": "eslint . --max-warnings 0", // ESLint, treating warnings as errors (strict for CI)
    "lint:fix": "eslint . --fix", // ESLint, automatically fixes fixable issues
    "lint:strict": "eslint . --max-warnings 0", // Alias for `lint:errors`
    "lint:errors-only": "eslint . --quiet", // ESLint, only reports errors, suppresses warnings
    "lint:dry-fix": "eslint . --fix-dry-run --format compact", // Shows what fixes ESLint *would* apply without saving
    "lint:output:json": "eslint . --format json", // Outputs ESLint results in JSON format
    "lint:cache": "eslint . --cache", // Runs ESLint with caching for faster subsequent runs
    "lint:report:html": "eslint . --format html --output-file eslint-report.html", // Generates an HTML report of linting issues
    "lint:compact-output": "eslint . --format compact", // Outputs ESLint results in a concise compact format
    "lint:codeframe-output": "eslint . --format codeframe", // Outputs ESLint results with code snippets for context
    "lint:cache-purge": "rm -f .eslintcache", // Manually removes ESLint cache

    // Formatting (Prettier)
    "check:format": "prettier --check .", // Checks if files are formatted according to Prettier rules
    "format:fix": "prettier --write .", // Formats files using Prettier

    // Testing (Bun Test)
    "test:deprecated:middleware": "bun test tests/deprecated/middleware.test.ts", // Runs specific deprecated middleware tests
    "test:auth": "bun test tests/authentication.test.ts", // Runs authentication tests
    "test:health": "bun test tests/health.test.ts", // Runs health check tests
    "test:tokens": "bun test tests/token-management.test.ts", // Runs token management tests
    "test:security": "bun test tests/security.test.ts", // Runs security tests
    "test:identity": "bun test tests/identity-management.test.ts", // Runs identity management tests
    "test:session": "bun test tests/session.test.ts", // Runs session tests
    "test:integration": "bun test tests/integration.test.ts", // Runs integration tests
    "test:all": "bun test tests/", // Runs all tests in the `tests/` directory
    "test:watch": "bun test tests/ --watch", // Runs tests in watch mode
    "test:coverage": "bun test tests/ --coverage", // Runs tests and generates a code coverage report
    "test:list-names": "find tests -name '*.test.ts' -exec basename {} \\;", // Lists test file names (shell command)
    "test:count": "find tests -name '*.test.ts' | wc -l", // Counts test files (shell command)
    "test:file": "bun test", // Generic command to run a specific test file (append path when running)
    "test:grep": "bun test --grep", // Filters tests by name (append regex when running)
    "test:filter": "bun test --filter", // Filters test files by path (append path when running)
    "test:debug": "bun --inspect-brk test", // Runs tests with Bun debugger attached
    "test:silent": "bun test --silent", // Runs tests with minimal output
    "test:verbose": "bun test --verbose", // Runs tests with detailed output
    "test:bail": "bun test --bail", // Stops tests immediately after the first failure
    "test:failed": "bun test --only-failed", // Reruns only tests that failed previously
    "test:todo": "bun test --todo", // Lists tests marked as todo
    "test:focus": "bun test --only", // Runs only tests marked with `.only()`

    // Custom CLI Scripts (from src/config/scripts/cli.ts)
    "reinstall": "bun run src/config/scripts/cli.ts reinstall", // Custom script to reinstall dependencies
    "clean:imports": "bun run src/config/scripts/cli.ts clean:imports", // Custom script for import cleanup
    "map:all": "bun run src/config/scripts/cli.ts map", // Custom script for mapping (all files)
    "map:folders": "bun run src/config/scripts/cli.ts map --folders-only", // Custom script for mapping (folders only)
    "map:hide": "bun run src/config/scripts/cli.ts map --show-all-with-hide-list", // Custom mapping script with hide list
    "map:sonnet:hide": "bun run src/config/scripts/cli.ts map:sonnet . --show-all-with-hide-list" // Custom mapping script (Sonnet variant)
  }
}
```

**Key Highlights:**

*   **Environment Variables:** Scripts like `dev`, `start:staging`, `start:production` demonstrate passing `NODE_ENV` to control application behavior.
*   **Granular Control:** Numerous ESLint and Bun test commands provide fine-grained control for debugging, focused testing, and detailed reporting.
*   **Custom CLI:** Integration of custom `cli.ts` scripts showcases advanced project automation.

---

#### **4. TypeScript Configuration (`tsconfig.json`)**

TypeScript is essential for type safety and provides excellent developer experience through autocompletion and early error detection.

```json
{
    "compilerOptions": {
        // Language and Environment
        "typeRoots": ["./node_modules/@types", "./types"], // Directories for type definitions
        "types": ["bun-types", "node"], // Include Bun and Node.js specific types
        "lib": ["ESNext", "DOM"], // Include standard library definitions
        "target": "ESNext", // Compile to the latest ECMAScript version

        // Modules
        "module": "ESNext", // Output ES modules
        "moduleDetection": "force", // Ensure all files are treated as modules
        "rootDir": ".", // Root directory for source files
        "baseUrl": ".", // Base directory for module resolution
        "paths": { // Path aliases for easier imports
            "@/*": ["src/*", "tests/*"],
            "@app-types/*": ["types/*"]
        },

        // Bundler Mode
        "allowImportingTsExtensions": true, // Allow .ts and .tsx file extensions in imports
        "moduleResolution": "bundler", // Use bundler-style module resolution (e.g., for Bun, Webpack)
        "verbatimModuleSyntax": true, // Ensure output modules closely resemble source imports
        "noEmit": true, // Do not emit JavaScript files, only perform type checking

        // Interop Constraints
        "esModuleInterop": true, // Enables better compatibility for default imports from CommonJS modules
        "allowSyntheticDefaultImports": true, // Allows default imports from modules with no default export
        "forceConsistentCasingInFileNames": true, // Enforces consistent casing in file names

        // Type Checking & Completeness
        "strict": true, // Enable all strict type-checking options (recommended)
        "skipLibCheck": true, // Skip type checking of all .d.ts files (improves build speed)
        "resolveJsonModule": true, // Allows importing .json files as modules
        "skipDefaultLibCheck": true, // Skip type checking .d.ts files that are included with TypeScript.
        "noFallthroughCasesInSwitch": true, // Report errors for fallthrough cases in switch statements

        // Enable Typegoose Decorator usage
        "emitDecoratorMetadata": true, // Emit design-type metadata for decorators
        "experimentalDecorators": true, // Enable experimental support for decorators
        "strictPropertyInitialization": false // Disable strict property initialization checks (often needed with ORMs/Typegoose)
    },
    "include": [ // Files to include in the compilation
        "src/**/*",
        "types/**/*",
        "tests/**/*",
        "src/server.ts"
    ],
    "exclude": ["node_modules", "dist", "shared"] // Files/directories to exclude
}
```

**Key Aspects:**

*   **Type Roots & Libs:** Defines where TypeScript should look for type declarations and which standard libraries to include.
*   **Path Aliases:** The `paths` option is critical for simplifying imports (e.g., `@/utils/constants` instead of `../../utils/constants`).
*   **Bundler Mode:** `moduleResolution: "bundler"` is optimized for modern bundlers like Bun, ensuring correct module resolution.
*   **`noEmit: true`:** Perfect for projects where Bun (or another tool) handles the transpilation, as TypeScript is only used for type checking.
*   **Strictness:** `strict: true` enforces a high level of type safety.
*   **Typegoose Support:** `emitDecoratorMetadata` and `experimentalDecorators` are enabled specifically for libraries that rely on decorators, such as Typegoose (used with Mongoose).

---

#### **5. Integration and Workflow**

This comprehensive setup ensures a streamlined development workflow:

1.  **Local Development:**
	*   Use `bun run dev` or `bun run dev:local` for watched development, benefiting from Bun's fast refresh.
	*   Regularly run `bun run check:types` for type validation.
	*   Use `bun run lint` to catch issues, and `bun run lint:fix` or `bun run format:fix` to auto-correct them.
	*   Leverage specific `bun test` commands (e.g., `test:file`, `test:grep`, `test:watch`) for efficient test-driven development.
2.  **Pre-commit Hooks (Recommended):**
	*   Integrate `husky` and `lint-staged` to automatically run `lint:fix` and `format:fix` on staged files before commits. This ensures that only well-formatted and linted code makes it into your version control.
3.  **CI/CD Pipeline:**
	*   In your CI/CD pipelines (e.g., on your Unraid server's DevOps agent), ensure the following steps:
		*   `bun install`
		*   `bun run check:types` (or `log:tsc`)
		*   `bun run lint:strict` (to ensure zero warnings/errors)
		*   `bun run check:format` (to ensure code is formatted)
		*   `bun run test:all` (or `bun run test:coverage`)
		*   Consider `bun run test --shard X/Y` for parallel test execution in larger projects.

This documentation should serve as a clear reference for anyone working on the Introspection Consulting projects, ensuring consistent quality and smooth collaboration.