BUN's Test Runner:
------------------
MAIN METHODS:
describe(name: string, func: () => void):
- Groups related tests together into a logical unit and provides a descriptive name for the group.
-- name: A string describing the test suite
-- func: A function that contains the individual test cases (defined using 'it')

  it(name: string, func: () => Promise<void> | void): OR
test(name: string, func: () => Promise<void> | void):
- Defines an individual test case.
- Essentially specifies a specific scenario/behaviour that you want to verify.
--name: A string describing the specific test
--func: A function that contains the code to execute the test & make assertions.

expect(actualValue):
- Creates an 'expect' object, which provides assertion methods to check if the 'actualValue' matches the expected value.
-- actualValue: The value you want to test
Extension Methods:
-- .toBe(expectedValue):                      Checks if the actual-value is strictly equal to the expected-value (using ===)
-- .toEqual(expectedValue):                   Checks if the actual-value is   deeply equal to the expected-value (object/array)
-- .toContain(expectedValue):                 Checks if the actual-value contains the expected-value (strings/array)
-- .toBeTruthy():                             Checks if the actual-value is truthy (e.g. true, non-empty string, non-zero number)
-- .toBeFalsy():                              Checks if the actual-value is  falsy (e.g. false, null, undefined, empty string, zero number)
-- .toBeGreaterThan(number):                  Checks if the actual-value is greater than the number
-- .toBeLessThan(number):                     Checks if the actual-value is    less than the number
-- .toBeInstanceOf(constructor: Function):    Checks if actual-value is an instance of 'constructor'
-- .toThrow(error?: Error | string):          Checks if the function throws an error

(GENERATED BY SUPERMAVEN):
-- .toBeCalled():                             Checks if the function was called
-- .toBeCalledWith(expectedValue):            Checks if the function was called with the expected value
-- .toBeCalledTimes(number):                  Checks if the function was called the expected number of times
-- .toBeDefined():                            Checks if the actual-value is defined
-- .toBeUndefined():                          Checks if the actual-value is undefined
-- .toBeNull():                               Checks if the actual-value is null

------------------------------------------------------------------------------------------------------------------------------------------------------
SUPERTEST:
----------
- This library simplifies testing HTTP APIs.
- It allows you to make HTTP requests to your Express application and inspect the responses in your tests.

supertest(app: Express.Application):
- Creates a Supertest agent that wraps your Express application.
- The agent allows you to chain methods to define and send HTTP requests.

REQUEST METHODS:
- get(url: string)       Makes a GET request to the specified URL.
- post(url: string)      Makes a POST request to the specified URL.
- put(url: string)       Makes a PUT request to the specified URL.
- patch(url: string)     Makes a PATCH request to the specified URL.
- delete(url: string)    Makes a DELETE request to the specified URL.
- head(url: string)      Makes a HEAD request.
- options(url: string)   Makes an OPTIONS request.
--Example:
    const request = supertest(app);
    await request.get('/api/v1/users').expect(200);

CHAINING METHODS:
- Supertest allows you to chain methods to configure the request:
-- set(header: string, value: string)   => Sets a request header (e.g. Authorization, Content-Type, etc.)
-- send(data: any)                      => Sends data in the request body (e.g. JSON, form-data, etc.)
-- query(data: object)                  => Adds query parameters to the request URL
-- attach(field: string, file: string)  => Attaches a file to the request (for file uploads)
---Example:
    const response = await request
        .post('/api/v1/login')
        .set('Content-Type', 'application/json')
        .send({ email: '<EMAIL>', password: 'password' })
        .expect(200);

RESPONSE OBJECT:
- The await result of a Supertest request is a response object that contains information about the HTTP response.
-- response.status:  The HTTP status code (e.g. 200, 404, 500 etc.)
-- response.header:  An object containing the response headers
-- response.body:    The response body parsed as a JSON object (if the Content-Type is application/json)
-- response.text:    The response body as a string

SUMMARY:
--------
1. Structure:        Use 'describe' to group your tests logically (e.g., by route or component).
2. Individual Tests: Use 'it' to define individual test cases that verify specific behaviors.
3. Supertest:        Use Supertest to make HTTP requests to your Express application.
4. Assertions:       Use 'expect' to make assertions about the response status, headers, and body.