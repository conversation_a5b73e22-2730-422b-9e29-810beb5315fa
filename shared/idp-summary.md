# Identity Provider (IdP) Summary

## Overview
Your IdP is a standalone microservice that handles authentication and authorization for the entire Excelytics platform. It issues JWT tokens and provides token validation services to other microservices.

## Core Functionality

### **1. User Registration & Authentication**
- **Registration**: Users register with email/password, automatically logged in after successful registration
- **Login**: Email/password authentication with JWT token generation
- **Password Security**: Uses bcrypt hashing for secure password storage

### **2. Token Management**
- **Access Tokens**: Short-lived (1h), contain user info and optional permissions, used for API authentication
- **Refresh Tokens**: Long-lived (7d), used to obtain new access tokens without re-authentication
- **Token Format**: JWT with Zod validation for type safety

### **3. Token Validation Services**
- **Introspection Endpoint**: Other microservices validate tokens by calling `/auth/introspection-token`
- **Internal Validation**: Health check middleware provides token verification utilities
- **Stateless Design**: No token storage required, all validation done via JWT signature verification

## API Endpoints

| Endpoint | Method | Purpose | Returns |
|----------|--------|---------|---------|
| `/auth/register` | POST | User registration + auto-login | Access + Refresh tokens |
| `/auth/login` | POST | User authentication | Access + Refresh tokens |
| `/auth/refresh-token` | POST | Refresh access token | New access token |
| `/auth/introspection-token` | POST | Token validation for services | User info if valid |

## Token Usage Patterns

### **Client Applications**
1. Register/Login → Receive access + refresh tokens
2. Use access token for API calls
3. When access token expires → Use refresh token to get new access token
4. Store tokens securely (httpOnly cookies recommended)

### **Microservices (Finance, Calc, Client)**
1. Receive access token from client requests
2. Call IdP's introspection endpoint to validate token
3. Extract user information for authorization decisions
4. Proceed with business logic if token is valid

## Security Features
- Password hashing with bcrypt
- JWT signature validation
- Token expiration handling
- Type-safe token validation with Zod schemas
- Separate secrets for access and refresh tokens

---

# Mermaid Diagrams

## Simple Flow Diagram

```mermaid
graph TD
    A[Client App] -->|Register/Login| B[IdP - Auth Service]
    B -->|Access + Refresh Tokens| A
    
    A -->|API Request + Access Token| C[Finance Service]
    C -->|Validate Token| B
    B -->|User Info| C
    C -->|Response| A
    
    A -->|Refresh Token| B
    B -->|New Access Token| A
    
    style B fill:#e1f5fe
    style A fill:#f3e5f5
    style C fill:#e8f5e8
```

## Complex Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Client<br/>Vue.js]
        MOB[Mobile App]
        API_CLIENT[API Client]
    end
    
    subgraph "Identity Provider (IdP)"
        AUTH_CTRL[Auth Controller]
        AUTH_SVC[Auth Service]
        JWT_UTILS[JWT Utils]
        HEALTH_MW[Health Check Middleware]
        
        AUTH_CTRL --> AUTH_SVC
        AUTH_SVC --> JWT_UTILS
        HEALTH_MW --> JWT_UTILS
    end
    
    subgraph "Microservices"
        FINANCE[Finance Service<br/>Introspection.Finance]
        CALC[Calc Service<br/>Introspection.Finance.Calc]
        CLIENT_SVC[Client Service<br/>Introspection.Finance.Client]
    end
    
    subgraph "Database"
        MONGO[(MongoDB<br/>User Data)]
    end
    
    subgraph "Shared Library"
        SHARED[excelytics.shared-internals<br/>Types, Schemas, Enums]
    end
    
    %% Client to IdP flows
    WEB -->|1. POST /auth/register| AUTH_CTRL
    WEB -->|2. POST /auth/login| AUTH_CTRL
    WEB -->|3. POST /auth/refresh-token| AUTH_CTRL
    MOB -->|Auth Requests| AUTH_CTRL
    API_CLIENT -->|Auth Requests| AUTH_CTRL
    
    %% IdP internal flows
    AUTH_SVC --> MONGO
    JWT_UTILS -->|Generate/Verify JWT| AUTH_SVC
    
    %% Service to IdP introspection
    FINANCE -->|POST /auth/introspection-token| HEALTH_MW
    CALC -->|POST /auth/introspection-token| HEALTH_MW
    CLIENT_SVC -->|POST /auth/introspection-token| HEALTH_MW
    
    %% Client to Services with tokens
    WEB -->|API Requests + Access Token| FINANCE
    WEB -->|API Requests + Access Token| CALC
    WEB -->|API Requests + Access Token| CLIENT_SVC
    
    %% Shared library usage
    AUTH_CTRL -.->|Uses Types| SHARED
    AUTH_SVC -.->|Uses Types| SHARED
    JWT_UTILS -.->|Uses Schemas| SHARED
    HEALTH_MW -.->|Uses Validation| SHARED
    
    %% Response flows
    AUTH_CTRL -->|Access + Refresh Tokens| WEB
    HEALTH_MW -->|User Info/Validation| FINANCE
    HEALTH_MW -->|User Info/Validation| CALC
    HEALTH_MW -->|User Info/Validation| CLIENT_SVC
    
    FINANCE -->|Protected Data| WEB
    CALC -->|Calculations| WEB
    CLIENT_SVC -->|UI Data| WEB
    
    %% Styling
    classDef client fill:#f9f,stroke:#333,stroke-width:2px
    classDef idp fill:#bbf,stroke:#333,stroke-width:2px
    classDef service fill:#bfb,stroke:#333,stroke-width:2px
    classDef database fill:#fbb,stroke:#333,stroke-width:2px
    classDef shared fill:#ffb,stroke:#333,stroke-width:2px
    
    class WEB,MOB,API_CLIENT client
    class AUTH_CTRL,AUTH_SVC,JWT_UTILS,HEALTH_MW idp
    class FINANCE,CALC,CLIENT_SVC service
    class MONGO database
    class SHARED shared
```

## Token Lifecycle Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant I as IdP
    participant F as Finance Service
    participant DB as MongoDB
    
    Note over C,DB: Registration/Login Flow
    C->>I: POST /auth/register {email, password}
    I->>DB: Save hashed password
    I->>I: Generate Access + Refresh tokens
    I->>C: {accessToken, refreshToken, user}
    
    Note over C,DB: API Request Flow
    C->>F: GET /financial-data + Authorization: Bearer {accessToken}
    F->>I: POST /auth/introspection-token {token}
    I->>I: Validate JWT signature & expiry
    I->>F: {active: true, user: {...}}
    F->>C: Protected financial data
    
    Note over C,DB: Token Refresh Flow
    C->>I: POST /auth/refresh-token {refreshToken}
    I->>I: Validate refresh token
    I->>I: Generate new access token
    I->>C: {accessToken, tokenPayload}
    
    Note over C,DB: Token Expiry Handling
    C->>F: API Request with expired token
    F->>I: Token introspection
    I->>F: {active: false, error: "expired"}
    F->>C: 401 Unauthorized
    C->>I: Use refresh token
    I->>C: New access token
    C->>F: Retry with new token
    F->>C: Success response
```

## Key Benefits of This Design

1. **Centralized Authentication**: Single source of truth for user identity
2. **Stateless Tokens**: No session storage required, scales horizontally
3. **Service Decoupling**: Each microservice can independently validate tokens
4. **Type Safety**: Shared types ensure consistency across services
5. **Security**: Separate secrets, token expiration, password hashing
6. **Flexibility**: Optional permissions system for fine-grained access control