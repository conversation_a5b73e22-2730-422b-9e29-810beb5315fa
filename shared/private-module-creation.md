Okay, creating a shared TypeScript module/NPM package for your types, enums, constants, and Zod schemas is an excellent way to maintain consistency and reduce boilerplate across your microservices. Here's a step-by-step guide on how to set it up and publish it to a private artifact repository (like Nexus, Artifactory, GitHub Packages, GitLab Package Registry, Azure Artifacts, etc.).

**1. Create the Package Directory and Initialize**

```bash
mkdir shared-internals # Or your preferred package name
cd shared-internals
npm init -y # Or bun init -y, then adjust package.json as needed
```

When `npm init` asks for the package name, it's good practice to use a scoped name for private packages, e.g., `@your-org/shared-internals` or `@your-app-name/shared-internals`.

**2. Structure Your Files**

Organize your files within a `src` directory. This is a common convention.

```
shared-internals/
├── src/
│   ├── types/
│   │   ├── express.d.ts         # If augmenting Express
│   │   ├── global.d.ts          # For global type declarations
│   │   ├── response.types.ts
│   │   └── token.types.ts
│   ├── schemas/
│   │   └── token.schema.ts      # Your Zod schemas
│   ├── enums/                   # Or constants/
│   │   ├── api-version.enum.ts  # Changed from .ts to .enum.ts for consistency
│   │   ├── client-origin.enum.ts
│   │   ├── client-path.enum.ts
│   │   ├── token-type.enum.ts
│   │   ├── env.enum.ts
│   │   └── index.ts             # Barrel file for enums
│   ├── constants/
│   │   ├── error-codes.constants.ts
│   │   ├── http-status.constants.ts
│   │   ├── log-levels.constants.ts # Or log-levels.enum.ts if it's an enum
│   │   └── index.ts                # Barrel file for constants
│   └── index.ts                 # Main barrel file for the package
├── package.json
├── tsconfig.json
└── .npmrc                       # For configuring private registry (optional here, can be global)
```

*   **Barrel Files (`index.ts`):** These files re-export everything from their respective directories, making imports cleaner for consumers.
    *   `src/enums/index.ts`:
        ```typescript
        export * from './api-version.enum';
        export * from './client-origin.enum.ts';
        // ... and so on for all enums
        ```
    *   `src/constants/index.ts`:
        ```typescript
        export * from './error-codes.constants';
        // ... and so on for all constants
        ```
    *   `src/index.ts` (main entry):
        ```typescript
        export * as SharedTypes from './types'; // If you want them namespaced
        // Or export individually if preferred:
        // export * from './types/response.types';
        // export * from './types/token.types';

        export * as SharedSchemas from './schemas';
        // export * from './schemas/token.schema';

        export * from './enums'; // Re-exports from src/enums/index.ts
        export * from './constants'; // Re-exports from src/constants/index.ts

        // For global.d.ts and express.d.ts, they are typically picked up
        // by the TypeScript compiler if referenced correctly in tsconfig
        // or if the consuming project's tsconfig can see them.
        // You might not need to explicitly export them here unless they define
        // exportable members you want to directly import.
        ```

**3. Install Dependencies**

You'll need `typescript` as a dev dependency. If your schemas use `zod`, add it as a peer dependency or a regular dependency.

```bash
npm install --save-dev typescript @types/node
# or
bun add -d typescript @types/node

# If using Zod for schemas:
npm install zod
# or make it a peer dependency if consuming services will also install Zod
npm install --save-peer zod
# or
bun add zod # or bun add --peer zod
```

**4. Configure `tsconfig.json`**

This file tells TypeScript how to compile your code.

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020", // Or a newer version like ES2022
    "module": "CommonJS", // CommonJS is safe for Node.js libraries
    "lib": ["ES2020", "DOM"], // Add DOM if any types reference browser globals
    "declaration": true, // Crucial: Generates .d.ts files
    "declarationMap": true, // Generates source maps for .d.ts files
    "sourceMap": true, // Generates .js.map source maps
    "outDir": "./dist", // Where compiled files will go
    "rootDir": "./src", // Root directory of source files
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "baseUrl": "./src", // Allows for cleaner imports within the package
    "paths": {
      "@/*": ["*"] // Example: import { MyEnum } from '@/enums';
    }
  },
  "include": ["src/**/*"], // Which files to compile
  "exclude": ["node_modules", "dist"] // Which files to ignore
}
```

**5. Update `package.json`**

```json
{
  "name": "@your-org/shared-internals", // Use your scoped name
  "version": "0.1.0", // Start with an initial version
  "description": "Shared types, enums, constants, and schemas for microservices.",
  "main": "dist/index.js", // Entry point for CommonJS consumers
  "types": "dist/index.d.ts", // Entry point for TypeScript type definitions
  "files": [ // Files to include when the package is published
    "dist",
    "src" // Optional: include src for source maps and easier debugging
  ],
  "scripts": {
    "clean": "rm -rf dist", // Or rimraf dist for cross-platform
    "build": "npm run clean && tsc",
    "prepublishOnly": "npm run build" // Ensures build runs before publishing
  },
  "keywords": [
    "shared",
    "types",
    "typescript",
    "enums",
    "constants"
  ],
  "author": "Your Name <<EMAIL>>",
  "license": "ISC", // Or your preferred license
  "devDependencies": {
    "@types/node": "^20.0.0", // Use appropriate version
    "typescript": "^5.0.0" // Use appropriate version
  },
  "peerDependencies": { // If you decided on peer dependencies
    "zod": "^3.20.0" // Match the version your services use
  }
  // "dependencies": { // If Zod is a regular dependency
  //   "zod": "^3.20.0"
  // },
  // "publishConfig": { // Important for scoped packages and private registries
  //   "registry": "https://your-artifact-repo.example.com/repository/npm-private/"
  // }
}
```
*   **`main`**: Points to the compiled JavaScript entry file.
*   **`types`**: Points to the main declaration file (`.d.ts`). This is crucial for TypeScript consumers.
*   **`files`**: Specifies which files/folders get included in the published NPM package.
*   **`scripts.build`**: The command to compile your TypeScript.
*   **`scripts.prepublishOnly`**: A script that runs automatically before `npm publish`.
*   **`publishConfig.registry`**: If you're publishing to a private registry, you often need to specify it here, especially for scoped packages if it's not your default npm registry. Alternatively, this can be configured in `.npmrc`.

**6. Global Type Declarations (`global.d.ts`, `express.d.ts`)**

*   **`src/types/global.d.ts`**:
    ```typescript
    // Example: Adding a property to the global NodeJS namespace
    declare global {
      namespace NodeJS {
        interface ProcessEnv {
          MY_GLOBAL_VAR?: string;
        }
      }
    }
    // To make this file a module (if it doesn't export anything else)
    export {};
    ```
*   **`src/types/express.d.ts`**:
    ```typescript
    // Example: Augmenting Express's Request object
    import { DecodedToken } from './token.types'; // Assuming you have this

    declare global {
      namespace Express {
        interface Request {
          user?: DecodedToken; // Or whatever custom property you add
        }
      }
    }
    // This file doesn't need an export {} if it's augmenting an existing module.
    ```
    For these global augmentations to be picked up by consuming projects, the consuming projects' `tsconfig.json` might need to be able to "see" these files, or you might need to reference them. Often, just installing the package and having `typeRoots` or `types` configured correctly in the consumer's `tsconfig.json` is enough.

**7. Build the Package**

```bash
npm run build
# or
bun run build
```
This will compile your TypeScript in `src` to JavaScript and `.d.ts` files in the `dist` folder.

**8. Configure NPM for Your Private Artifact Repository**

This step depends on your artifact repository.

*   **Login:** You'll typically need to log in to your private registry.
    ```bash
    npm login --registry=https://your-artifact-repo.example.com/repository/npm-private/
    # Follow prompts for username, password, and email.
    ```
*   **`.npmrc` file:**
    You can create an `.npmrc` file in the root of your package or in your user home directory (`~/.npmrc`) to associate your scope with your private registry:
    ```
    // .npmrc
    @your-org:registry=https://your-artifact-repo.example.com/repository/npm-private/
    //your-artifact-repo.example.com/repository/npm-private/:_authToken=YOUR_AUTH_TOKEN
    ```
    (Using `_authToken` is common for CI environments or if your registry supports token-based auth directly in `.npmrc`). Consult your artifact repository's documentation.

**9. Publish the Package**

```bash
npm publish
```
If you used a scoped name (e.g., `@your-org/shared-internals`), it will be published as a private package by default if your registry supports it, or you might need to add `--access=public` if it were for the public NPM registry (but you want private). For private registries, the scope and registry configuration usually handle this.

**10. Consume the Package in Your Microservices**

In each microservice:

1.  **Configure NPM (if not globally configured):** Ensure the microservice's environment can access your private registry (e.g., via its own `.npmrc` or a global one).
2.  **Install the package:**
    ```bash
    npm install @your-org/shared-internals
    # or
    bun add @your-org/shared-internals
    ```
    If the registry isn't automatically picked up for the scope, you might need:
    ```bash
    npm install @your-org/shared-internals --registry=https://your-artifact-repo.example.com/repository/npm-private/
    ```
3.  **Use the shared items:**
    ```typescript
    // In a microservice file
    import { EnvEnum, ApiVersionEnum } from '@your-org/shared-internals/enums'; // If using sub-path from barrel
    import { TokenSchema, SharedTypes } from '@your-org/shared-internals'; // Importing from main index

    const currentEnv: EnvEnum = EnvEnum.Development;
    const apiV: ApiVersionEnum = ApiVersionEnum.V1;

    function handleRequest(req: Express.Request) {
      // If you augmented Express.Request in your shared package
      const user = req.user; // user type will be from DecodedToken
      console.log(user?.userId);
    }

    const validateTokenPayload = (payload: unknown): SharedTypes.Token.DecodedToken | null => {
        const result = TokenSchema.safeParse(payload);
        if (result.success) {
            return result.data;
        }
        console.error(result.error.flatten());
        return null;
    }
    ```

**11. Versioning and Workflow**

*   Use Semantic Versioning (SemVer) for your shared package (e.g., `0.1.0`, `0.2.0`, `1.0.0`).
*   When you make changes to the shared package:
    1.  Update the version number in `package.json`.
    2.  `npm run build`
    3.  `npm publish`
    4.  In each microservice, update the dependency to the new version:
        ```bash
        npm update @your-org/shared-internals
        # or
        npm install @your-org/shared-internals@latest
        # or specify a version
        npm install @your-org/shared-internals@0.2.0
        ```

**Local Development with `npm link` or `bun link`**

During development, constantly publishing and reinstalling is slow. Use `npm link` (or `bun link`):

1.  **In your `shared-internals` package directory:**
    ```bash
    npm run build # Build it first
    npm link
    # or
    bun link
    ```
2.  **In each microservice directory that needs to use it:**
    ```bash
    npm link @your-org/shared-internals
    # or
    bun link @your-org/shared-internals
    ```
Now, the microservice will use your local `shared-internals` build. When you make changes in `shared-internals` and run `npm run build` (or have a watch script), the changes are immediately reflected in the linked microservices (you might need to restart the microservice's dev server).

This setup provides a robust way to manage and share common code elements across your microservices. Remember to consult the specific documentation for your chosen artifact repository for precise configuration details.

# Why use CommonJS in private modules/packages?
You've hit on a very common point of confusion! Let's clear this up.

**1. The `dist` Folder is CRUCIAL, Even for Backend-Only Shared Packages**

*   When your `identity` microservice (or any other microservice) installs your `shared-internals` package (e.g., via `npm install @your-org/shared-internals` or `bun add @your-org/shared-internals`), it's **not** directly using the TypeScript files from your `shared-internals/src` directory.
*   Instead, it uses the **compiled JavaScript files and the TypeScript declaration files (`.d.ts`)** that are generated by `tsc` and placed into the `shared-internals/dist` directory.
*   The `package.json` of `shared-internals` tells NPM/Bun where to find these important files:
    *   `"main": "dist/index.js"` (or similar for ESM)
    *   `"types": "dist/index.d.ts"`
*   So, the `dist` folder is absolutely essential. It contains the "distributable" version of your library that other projects consume.

**2. How You Write Code vs. How It's Compiled vs. How It's Consumed**

This is the key:

*   **A. How YOU WRITE code in `shared-internals/src`:**
    *   You will **always** use modern ES Module syntax: `import { ... } from '...';` and `export ...;`.
    *   You do **not** use `require()`.
    *   You do **not** add `.js` extensions to your internal imports (e.g., `import { MyEnum } from './enums';`).

*   **B. How `tsc` COMPILES your code into `shared-internals/dist` (based on `tsconfig.json`'s `module` option):**
    *   **If `module: "CommonJS"` in `shared-internals/tsconfig.json`:**
        *   The `.js` files in `dist/` will be in CommonJS format (using `require()` and `module.exports`).
        *   The `.d.ts` files will still describe your original ES Module structure for type-checking purposes.
    *   **If `module: "ESNext"` in `shared-internals/tsconfig.json` (and `type: "module"` in `package.json`):**
        *   The `.js` files in `dist/` will also be in ES Module format (using `import`/`export`).

*   **C. How your `identity` microservice CONSUMES the `shared-internals` package:**
    *   This depends on how the `identity` microservice itself is configured (its own `package.json` and `tsconfig.json`).
    *   **If your `identity` service is an ES Module project (likely, given your main `tsconfig.json` uses `module: "ESNext"` and you're using Bun):**
        *   You will use `import { ... } from '@your-org/shared-internals';` in your `identity` service.
        *   You will **NOT** use `require()`.
        *   This works **even if `shared-internals` was compiled to CommonJS in its `dist` folder.** Node.js (and Bun, which is compatible) has good interoperability that allows ES Module projects to import CommonJS modules seamlessly. The `.d.ts` files from `shared-internals` ensure your TypeScript experience in `identity` is based on the original ES Module structure.

**So, to directly answer your question:**

> "so you saying using commonjs is fine and wont mean i consume the package contents using require?"

**Yes, that's correct, *if your `identity` service is itself an ES Module project.***

*   Setting `module: "CommonJS"` in the `shared-internals/tsconfig.json` **only affects the format of the JavaScript files inside `shared-internals/dist`**.
*   It does **not** force your `identity` service (the consumer) to use `require()`.
*   Your `identity` service will use its own native module syntax (`import` if it's an ESM project, `require` if it's a CJS project) to consume the `shared-internals` package.

**Why `module: "CommonJS"` for the library output is a safe default:**

*   It provides the best compatibility if you *ever* have a consumer (another microservice, a script, a test runner) that is a CommonJS project.
*   ESM projects can import CJS modules without issue.

**If you are 100% certain that ALL consumers of `shared-internals` (now and in the foreseeable future) will be ES Module projects:**

*   Then, setting `module: "ESNext"` in `shared-internals/tsconfig.json` (and `"type": "module"` in its `package.json`) is also perfectly fine and perhaps more "modern" as the `dist` files will also be ESM.
*   Your `identity` service (being ESM) would still consume it with `import`.

**In your specific case:**

Since your main `tsconfig.json` (which I assume is for services like `identity`) uses `module: "ESNext"`, your `identity` service is an ES Module project.

Therefore:

1.  You will write `import/export` in `shared-internals/src`.
2.  You will write `import/export` in your `identity` service when importing from `shared-internals`.
3.  The choice of `module: "CommonJS"` or `module: "ESNext"` for the `shared-internals` library's *output* primarily affects the format of files in `shared-internals/dist` and its compatibility with potential *other* CommonJS consumers. For your ESM `identity` service, either will work for consumption via `import`.

Given your ecosystem seems to be leaning towards ESNext, using `module: "ESNext"` for your `shared-internals` library (along with `"type": "module"` in its `package.json`) is a very reasonable and consistent choice. My initial suggestion for `CommonJS` was based on maximizing general library compatibility, but if your ecosystem is homogenous, aligning is good.


# Excelytics Package Information
```bash
reza@Rezas-MacBook-Pro shared-internals % bun link
bun link v1.2.5 (013fdddc)
Success! Registered "excelytics.shared-internals"

To use excelytics.shared-internals in a project, run:
  bun link excelytics.shared-internals

Or add it in dependencies in your package.json file:
  "excelytics.shared-internals": "link:excelytics.shared-internals"
```
linking:
```bash
reza@Rezas-MacBook-Pro Introspection.Identity % bun link excelytics.shared-internals
[0.72ms] ".env"
bun link v1.2.5 (013fdddc)

+ @types/express@5.0.1
+ @types/supertest@6.0.2

installed excelytics.shared-internals@link:excelytics.shared-internals

28 packages installed [141.00ms]
reza@Rezas-MacBook-Pro Introspection.Identity % 
```
usage once linked:
```ts
import { SharedSchemas } from 'excelytics.shared-internals';
```