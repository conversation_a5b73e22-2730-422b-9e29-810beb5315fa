# API Endpoints V1
`Dated: 21.03.2025`

## Authentication
### 1. Login
> Endpoint: `/api/v1/auth/login`

###### JSON Body
```json
{
  "email": string,          // Required
  "password": string        // Required
}
```

### 2. Register
> Endpoint: `/api/v1/auth/register`
> 
###### JSON Body
```json
{
  "clientId": string,       // Required
  "clientOrigin": number,   // Required (EnumClientOrigin)
  "email": string,          // Required
  "password": string,       // Required
  "firstName": string,      // Optional
  "lastName": string,       // Optional
  "userRole": number,       // Optional (EnumUserRoles)
  "cellNumber": string,     // Optional
  "referralCode": string    // Optional
}
```
---
## User
**JSON Body**

#### 1. Get Users
> Endpoint: `/api/v1/user`

#### 2. Get User By Email
> Endpoint: `/api/v1/user/email/:email`

#### 3. Get Users By Client Origin
> Endpoint: `/api/v1/user/clientOrigin/:clientOrigin`

#### 4. Update User By Email
> Endpoint: `/api/v1/user`

#### 5. Delete User By Email
> Endpoint: `/api/v1/user/:email`
