#--------------------------------------------------------------------------
# Introspection.Identity - Environment Configuration
#--------------------------------------------------------------------------

# --- MongoDB Credentials (safer) ---
MONGODB_URI=transformed-by-env-file
MONGO_USER=admin
MONGO_PASS="u?yNxEWg#3"
MONGO_HOST=************
MONGO_PORT=27017
MONGO_DB_NAME=introspection
MONGO_AUTH_SOURCE=admin

# MongoDB URIs
MONGODB_URI_UNRAID=********************************************************************************
MONGODB_URI_LOCAL=mongodb://localhost:27017/introspection
MONGODB_URI_STAGNG=NONE
MONGODB_URI_PROD=NONE

# --- Service Identification ---
SERVICE_NAME=excelytics.identity
API_VERSION=v1
VERSION=1.1.2

# --- Environment & Network ---
TAILSCALE_IP=************
ENV=development
PORT=6002
VPN=true

# --- Redis Configuration ---
REDIS_URI_UNRAID=************
REDIS_PASSWORD=undefined
REDIS_HOST=************
REDIS_PORT=6379

# --- Microservices URLs (Revised Ports) ---
IDP_SERVICE_URL=http://localhost:6002
CALC_SERVICE_URL=http://localhost:6003
CLIENT_SERVICE_URL=http://localhost:4200
FINANCE_SERVICE_URL=http://localhost:6001

# --- IdP Specific Configuration ---
# These are crucial for OAuth/OIDC flows to know where to redirect users.
# Often, allowed redirect URIs are stored per client in the database
IDP_BASE_URL=https://auth.excelytics.co.za
CLIENT_APP_LOGOUT_SUCCESS_REDIRECT_URI=https://app.excelytics.co.za/login
CLIENT_APP_LOGIN_SUCCESS_REDIRECT_URI=https://app.excelytics.co.za/dashboard
COOKIE_DOMAIN=.excelytics.co.za

# --- Thresholds & Expiration Times (5s & 1h) ---
SLOW_REQUEST_THRESHOLD_MS=5000
SESSION_MAX_AGE_MS=3600000
ACCESS_TOKEN_EXPIRY=30m
REFRESH_TOKEN_EXPIRY=7d

# --- Rate Limiting ---
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# --- Session Store (MongoStore, 14days & 24h) ---
SESSION_STORE_TTL_SECONDS=1209600
SESSION_STORE_TOUCH_AFTER_SECONDS=86400
SESSION_STORE_COLLECTION_NAME=sessions_idp
#SESSION_STORE_CRYPTO_SECRET=undefined

# --- IdP Secrets --- .env.development
JWT_SECRET=dev_Kx9vR2mP8wQnE5tY7uI0oP3aS6dF9gH2jK5lZ8xC1vB4nM7qW0eR3tY6uI
REFRESH_TOKEN_SECRET=dev_Mz8qW3eR6tY9uI2oP5aS8dF1gH4jK7lZ0xC3vB6nM9qW2eR5tY8u
COOKIE_SECRET=dev_Bx4nM7qW0eR3tY6uI9oP2aS5dF8gH1jK4lZ7xC0vB3nM6qW9eR2t
SESSION_SECRET=dev_Fx7gH0jK3lZ6xC9vB2nM5qW8eR1tY4uI7oP0aS3dF6gH9jK2lZ5x
SESSION_STORE_CRYPTO_SECRET=dev_Hx2jK5lZ8xC1vB4nM7qW0eR3tY6uI9oP2aS5dF8gH1jK
#JWT_SECRET=excelytics.u?yNxWg3.jwt.secret
#COOKIE_SECRET=excelytics.u?yNxWg3.cookie.secret
#SESSION_SECRET=excelytics.u?yNxWg3.session.secret
#REFRESH_TOKEN_SECRET=excelytics.u?yNxWg3.refresh.token.secret

# --- Log Level ---
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, SILENT
LOG_LEVEL=DEBUG

# --- Error Handling & Logging Configuration ---
# Enable verbose error logging (overrides environment-based defaults)
DEBUG_ERRORS=false

# Enable logging of sensitive information (development only)
# WARNING: Only enable in development - never in production
LOG_SENSITIVE=false

# Enable verbose logging in tests (overrides environment-based defaults)
TEST_VERBOSE_LOGGING=false

# test-verbose-logging (LEVELS: TRACE, DEBUG, INFO, WARN, ERROR, SILENT)
# this is necessary to keep logging tests seperate from normal logging, as we still want the default logger to kick in based on LOG_LEVEL
TEST_LOG_LEVEL=DEBUG

# --- SECRETS SHOULD NOT BE STORED HERE ---
# The following values MUST be set as environment variables in your shell
# or your CI/CD pipeline (e.g., `export JWT_SECRET=...` in .zshrc)
#
# - ADO_PAT
# - JWT_SECRET
# - COOKIE_SECRET
# - SESSION_SECRET
# - REFRESH_TOKEN_SECRET
# - SESSION_STORE_CRYPTO_SECRET  # NEW AND IMPORTANT!
#
# These should be read from your shell environment, not hardcoded
# MONGO_USER=${MONGO_USER}
# MONGO_PASS=${MONGO_PASS}
# REDIS_PASSWORD=${REDIS_PASSWORD} # Add when you have one
#