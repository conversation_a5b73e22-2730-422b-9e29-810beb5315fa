# Method Wrapping Preservation Guide

## 🎯 Problem

Prettier sometimes unwraps method calls that you've intentionally wrapped for readability, especially when they have multiple parameters.

## ✅ Solution: Use `// prettier-ignore`

### For Single Method Calls

```typescript
// prettier-ignore
const registerResponse = await request
	.post('/api/v1/auth/register')
	.send(testUser);
```

### For Method Chains

```typescript
// prettier-ignore
const response = await request
	.post('/api/v1/auth/register')
	.send(invalidData)
	.expect(HttpStatus.BAD_REQUEST);
```

### For Function Calls with Many Parameters

```typescript
// prettier-ignore
const result = await someFunction(
	param1,
	param2,
	param3,
	param4
);
```

## 🔧 Configuration Changes Made

### Reduced Print Width

- **Changed from 120 to 100 characters**
- **Reason**: Shorter lines encourage wrapping, making Prettier less likely to unwrap your intentional breaks

### Conservative Settings

```json
{
	"printWidth": 100,
	"trailingComma": "es5",
	"bracketSameLine": false
}
```

## 📋 Best Practices

### When to Use `// prettier-ignore`

1. **Method chains with 3+ calls**
2. **Function calls with 4+ parameters**
3. **Complex expressions that are more readable when wrapped**
4. **Test assertions that benefit from vertical alignment**

### Example: Test Code

```typescript
// prettier-ignore
const registerResponse = await request
	.post('/api/v1/auth/register')
	.send(testUser);

if (registerResponse.status === 200 || registerResponse.status === 201) {
	// prettier-ignore
	const loginResponse = await request
		.post('/api/v1/auth/login')
		.send(loginCredentials);
}
```

### Example: Service Calls

```typescript
// prettier-ignore
const user = await UserService.createUser(
	userData.email,
	userData.password,
	userData.firstName,
	userData.lastName,
	userData.role
);
```

## 🚀 Usage Commands

### Format with Preserved Wrapping

```bash
# Standard formatting (respects prettier-ignore)
bun run format:fix

# Check formatting
bun run check:format
```

### Adding prettier-ignore Comments

1. **Identify methods** you want to keep wrapped
2. **Add `// prettier-ignore`** on the line before
3. **Run formatting** - Prettier will skip those lines

## 💡 Pro Tips

### 1. Use Block Comments for Multiple Lines

```typescript
/* prettier-ignore */
const response = await request
	.post('/api/v1/auth/register')
	.send(testUser)
	.expect(200);
```

### 2. Ignore Entire Blocks

```typescript
// prettier-ignore-start
const registerResponse = await request.post('/api/v1/auth/register').send(testUser);

const loginResponse = await request.post('/api/v1/auth/login').send(loginCredentials);
// prettier-ignore-end
```

### 3. Use ESLint Disable for Complex Cases

```typescript
// eslint-disable-next-line
// prettier-ignore
const complexCall = await someMethod(param1, param2, param3, param4);
```

## 🎯 Result

With these changes:

- ✅ **Prettier respects your wrapping** when you use `// prettier-ignore`
- ✅ **Shorter print width** (100) encourages natural wrapping
- ✅ **No extra newlines** at end of files
- ✅ **Comments preserved** as-is
- ✅ **Manual control** over method call formatting

## 📝 Quick Reference

| Scenario                | Solution                               |
| ----------------------- | -------------------------------------- |
| **Method chain**        | `// prettier-ignore` before the chain  |
| **4+ parameters**       | `// prettier-ignore` before the call   |
| **Test assertions**     | `// prettier-ignore` for readability   |
| **Complex expressions** | `// prettier-ignore` or block comments |

This approach gives you **complete control** over which method calls stay wrapped while letting Prettier handle the rest of your code formatting! 🎉
