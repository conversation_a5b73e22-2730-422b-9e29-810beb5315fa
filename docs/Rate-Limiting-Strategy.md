# Rate Limiting Strategy for Excelytics Identity Provider

## Overview

This document outlines the comprehensive rate limiting strategy for the Excelytics Identity Provider, designed to protect against abuse while maintaining excellent user experience across different environments.

## Rate Limiting Architecture

### **Three-Tier Rate Limiting System**

1. **General API Limiter** - Baseline protection for all endpoints
2. **Authentication Limiter** - Specialized protection for auth endpoints
3. **Password Reset Limiter** - Ultra-strict protection for sensitive operations

## Environment-Specific Configuration

### **Development Environment**
**Purpose**: Maximum flexibility for developers and testing

| Limiter Type | Window | Limit | Rationale |
|--------------|--------|-------|-----------|
| **General API** | 15 min | 1000 requests | High limit for rapid development/testing |
| **Authentication** | 15 min | 50 failed attempts | Allows extensive auth testing |
| **Password Reset** | 1 hour | 20 attempts | Generous for development workflows |

### **Test Environment**
**Purpose**: Automated testing and CI/CD pipelines

| Limiter Type | Window | Limit | Rationale |
|--------------|--------|-------|-----------|
| **General API** | 15 min | 1000 requests | High limit for test suites |
| **Authentication** | 15 min | 100 failed attempts | Very high for automated tests |
| **Password Reset** | 1 hour | 50 attempts | Accommodates test scenarios |

### **UAT (User Acceptance Testing) Environment**
**Purpose**: Real user testing with production-like constraints

| Limiter Type | Window | Limit | Rationale |
|--------------|--------|-------|-----------|
| **General API** | 15 min | 500 requests | Moderate limit for user testing |
| **Authentication** | 15 min | 25 failed attempts | Realistic but forgiving |
| **Password Reset** | 1 hour | 10 attempts | Production-like security |

### **Staging Environment**
**Purpose**: Final pre-production testing with near-production settings

| Limiter Type | Window | Limit | Rationale |
|--------------|--------|-------|-----------|
| **General API** | 15 min | 300 requests | Close to production |
| **Authentication** | 15 min | 15 failed attempts | Slightly higher than prod |
| **Password Reset** | 1 hour | 7 attempts | Near-production security |

### **Production Environment**
**Purpose**: Live system with maximum security

| Limiter Type | Window | Limit | Rationale |
|--------------|--------|-------|-----------|
| **General API** | 15 min | 200 requests | Conservative for security |
| **Authentication** | 15 min | 10 failed attempts | Strict anti-brute force |
| **Password Reset** | 1 hour | 5 attempts | Maximum security |

## Rate Limiting Logic

### **General API Limiter**
```typescript
// Applied to: /api/v1/*
// Counts: All requests (successful + failed)
// Purpose: Baseline protection against general abuse
```

**Behavior:**
- Tracks all API requests regardless of success/failure
- Provides broad protection against DoS attacks
- Allows legitimate users normal usage patterns

### **Authentication Limiter**
```typescript
// Applied to: /api/v1/auth/login, /api/v1/auth/register
// Counts: Only failed attempts (skipSuccessfulRequests: true)
// Purpose: Prevent brute force attacks
```

**Behavior:**
- Only counts failed authentication attempts
- Successful logins don't consume the rate limit
- Protects against credential stuffing and brute force

### **Password Reset Limiter**
```typescript
// Applied to: /api/v1/auth/reset-password
// Counts: All attempts (successful + failed)
// Purpose: Prevent abuse of password reset functionality
```

**Behavior:**
- Counts all password reset requests
- Prevents email flooding and system abuse
- Most restrictive limiter due to sensitive nature

## Financial SaaS Considerations

### **Business User Patterns**
For a financial analytics platform like Excelytics:

1. **Typical Usage**: 50-100 API calls per session
2. **Peak Usage**: End-of-month reporting periods
3. **User Types**: 
   - **Analysts**: High API usage for data processing
   - **Managers**: Moderate usage for dashboards
   - **Executives**: Low usage for summary reports

### **Recommended Production Limits**

Based on financial SaaS usage patterns:

```typescript
// For Financial Analytics SaaS
const PRODUCTION_LIMITS = {
  // General API: Accommodates heavy data analysis
  generalApi: 200,        // ~13 requests/minute average
  
  // Authentication: Strict security for financial data
  authentication: 10,     // Strong protection for sensitive data
  
  // Password Reset: Ultra-strict for compliance
  passwordReset: 5        // Regulatory compliance consideration
};
```

## Implementation Details

### **Rate Limit Headers**
All responses include standard rate limit headers:

```http
X-RateLimit-Limit: 200
X-RateLimit-Remaining: 195
X-RateLimit-Reset: 1640998800
Retry-After: 60
```

### **Error Responses**
When rate limits are exceeded:

```json
{
  "success": false,
  "message": "Too many requests, please try again later.",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded"
  },
  "retryAfter": 60
}
```

## Monitoring and Alerting

### **Key Metrics to Monitor**

1. **Rate Limit Hit Rate**: % of requests hitting limits
2. **False Positives**: Legitimate users being blocked
3. **Attack Patterns**: Unusual traffic spikes
4. **Performance Impact**: Latency added by rate limiting

### **Recommended Alerts**

```yaml
# High rate limit hit rate (potential attack)
- alert: HighRateLimitHitRate
  expr: rate_limit_hits_per_minute > 50
  severity: warning

# Authentication brute force detected
- alert: AuthBruteForceDetected
  expr: auth_rate_limit_hits_per_minute > 10
  severity: critical

# Password reset abuse detected
- alert: PasswordResetAbuse
  expr: password_reset_rate_limit_hits_per_hour > 20
  severity: high
```

## Best Practices

### **1. Gradual Rollout**
- Start with higher limits in production
- Monitor user behavior patterns
- Gradually tighten limits based on data

### **2. User Communication**
- Clear error messages explaining rate limits
- Provide retry-after information
- Document rate limits in API documentation

### **3. Whitelist Considerations**
For financial SaaS, consider whitelisting:
- Known corporate IP ranges
- Verified business partners
- Internal monitoring systems

### **4. Dynamic Adjustment**
- Increase limits during known peak periods (month-end)
- Implement circuit breakers for system protection
- Consider user-tier based limits (premium vs basic)

## Security Considerations

### **Financial Data Protection**
- Authentication limits are intentionally strict
- Password reset limits prevent account takeover
- General limits prevent data exfiltration

### **Compliance Requirements**
- Rate limiting helps meet SOC 2 requirements
- Audit logs for all rate limit violations
- Incident response procedures for attacks

## Testing Strategy

### **Load Testing**
- Test rate limits under various load conditions
- Verify legitimate traffic isn't blocked
- Ensure graceful degradation under attack

### **Security Testing**
- Penetration testing against rate limits
- Brute force attack simulations
- DDoS resilience testing

---

## Summary

The Excelytics Identity Provider implements a sophisticated three-tier rate limiting strategy that:

✅ **Protects** against brute force and DoS attacks
✅ **Scales** appropriately across environments
✅ **Accommodates** financial SaaS usage patterns
✅ **Maintains** excellent user experience
✅ **Complies** with security best practices
✅ **Provides** comprehensive monitoring capabilities

This strategy ensures robust security while supporting the high-performance requirements of financial analytics workflows.
