# CORS Configuration for Introspection.Finance.Identity (IdP)

## Overview

This document outlines the Cross-Origin Resource Sharing (CORS) configuration for the Introspection.Finance.Identity service, explaining the rationale, implementation, and microservices architecture considerations.

## Table of Contents

- [What is CORS?](#what-is-cors)
- [Why CORS Matters for IdP](#why-cors-matters-for-idp)
- [Our CORS Configuration](#our-cors-configuration)
- [Microservices Architecture Considerations](#microservices-architecture-considerations)
- [Security Implications](#security-implications)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## What is CORS?

Cross-Origin Resource Sharing (CORS) is a security feature implemented by web browsers that restricts web pages from making requests to a different domain, protocol, or port than the one serving the web page. This is known as the "same-origin policy."

### The Same-Origin Policy Problem

Without CORS, a web application running on `https://app.excelytics.co.za` cannot make API calls to `https://auth.excelytics.co.za` because they are different origins.

### How CORS Solves This

CORS allows servers to specify which origins are permitted to access their resources by including specific HTTP headers in responses.

## Why CORS Matters for IdP

As an Identity Provider (IdP) in a microservices architecture, our service needs to:

1. **Accept authentication requests** from multiple client applications
2. **Provide tokens** to various frontend applications
3. **Handle token validation** requests from other microservices
4. **Maintain security** while enabling cross-origin communication

### Our Microservices Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Other         │
│   Applications  │    │   (Optional)    │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Web App       │    │ • Route         │    │ • Finance API   │
│ • Mobile App    │    │ • Load Balance  │    │ • Calc Engine   │
│ • Admin Panel   │    │ • Rate Limit    │    │ • File Service  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────┐
                    │  Identity Provider      │
                    │  (This Service)         │
                    │                         │
                    │ • Authentication        │
                    │ • Token Generation      │
                    │ • Token Validation      │
                    │ • User Management       │
                    └─────────────────────────┘
```

## Our CORS Configuration

### Allowed Origins

```typescript
// From excelytics.shared-internals
export const AllowedOrigins = [
    // Local development
    'http://localhost:6002',  // Identity service
    'http://localhost:6003',  // Finance API
    'http://localhost:4200',  // Frontend application
    'http://localhost:6001',  // Calc engine
    
    // Unraid server (development/staging)
    'http://************:6002',
    'http://************:4200',
    'http://************:6001',
    
    // Production domains
    'https://auth.excelytics.co.za',    // Identity service
    'https://app.excelytics.co.za',     // Main application
    'https://excelytics.co.za',         // Marketing site
    'https://www.excelytics.co.za',     // Marketing site (www)
    'https://api.excelytics.co.za'      // API gateway
];
```

### HTTP Methods

```typescript
methods: ['GET', 'POST', 'PUT', 'OPTIONS']
```

**Why these methods?**
- `GET`: Reading user data, health checks, token validation
- `POST`: Authentication, registration, token refresh
- `PUT`: User profile updates, password changes
- `OPTIONS`: Preflight requests (required for CORS)
- `DELETE`: Intentionally excluded - user deletion should be admin-only

### Allowed Headers

```typescript
allowedHeaders: [
    // Standard HTTP headers
    'Content-Type',           // JSON/form data
    'Authorization',          // Bearer tokens
    'Accept',                // Content negotiation
    'Accept-Language',       // Internationalization
    'Accept-Encoding',       // Compression
    'Origin',                // CORS origin
    'Referer',               // Security checks
    'User-Agent',            // Client identification
    
    // AJAX/API headers
    'X-Requested-With',      // AJAX identification
    
    // Custom service headers
    'X-Service-Name',        // Service identification
    'X-Request-ID',          // Request tracing
    'X-Correlation-ID',      // Distributed tracing
    'X-Client-Version',      // Client version tracking
    
    // Proxy/Load balancer headers
    'X-Forwarded-For',       // Client IP through proxy
    'X-Real-IP',             // Alternative client IP
    
    // Caching headers
    'Cache-Control',         // Cache directives
    'Pragma',                // Legacy cache control
    'If-None-Match',         // ETag-based caching
    'If-Modified-Since'      // Last-Modified caching
]
```

### Exposed Headers

```typescript
exposedHeaders: [
    // Request tracking
    'X-Request-ID',
    'X-Correlation-ID',
    
    // Rate limiting
    'X-RateLimit-Limit',
    'X-RateLimit-Reset',
    'X-RateLimit-Remaining',
    'Retry-After',
    
    // Standard HTTP response headers
    'Location',              // Redirects
    'Content-Length',        // Response size
    'Content-Range',         // Partial content
    'ETag',                  // Caching
    'Last-Modified',         // Caching
    
    // Pagination (for future list endpoints)
    'X-Total-Count',         // Total items
    'X-Page-Count',          // Total pages
    'Link',                  // Pagination links (RFC 5988)
    
    // Service information
    'X-Service',
    'X-API-Version',
    'X-Environment',
    'X-No-Compression',
    
    // Security headers (for client inspection)
    'X-Frame-Options',
    'X-Content-Type-Options',
    'X-XSS-Protection',
    'X-Permitted-Cross-Domain-Policies'
]
```

### Additional Configuration

```typescript
credentials: true,           // Enable cookies/sessions
optionsSuccessStatus: 200,   // Legacy browser support
maxAge: 86400               // Cache preflight for 24 hours
```

## Microservices Architecture Considerations

### 1. Service-to-Service Communication

**Internal Services** (same network):
- Services within the same network can bypass CORS
- Use internal service discovery
- Direct HTTP calls without browser restrictions

**Browser-Initiated Requests**:
- All browser requests must comply with CORS
- Frontend → IdP authentication
- Frontend → API Gateway → Services

### 2. Token Flow Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Browser   │────▶│     IdP     │────▶│  Database   │
│             │     │             │     │             │
│ 1. Login    │     │ 2. Validate │     │ 3. User     │
│    Request  │     │    Creds    │     │    Lookup   │
└─────────────┘     └─────────────┘     └─────────────┘
       ▲                     │
       │                     ▼
       │            ┌─────────────┐
       │            │   JWT       │
       │            │   Service   │
       │            │             │
       └────────────│ 4. Return   │
          5. Token  │    Token    │
                    └─────────────┘

┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Browser   │────▶│   Finance   │────▶│     IdP     │
│             │     │   API       │     │             │
│ 6. API Call │     │             │     │ 8. Validate │
│ + Token     │     │ 7. Forward  │     │    Token    │
└─────────────┘     │    Token    │     └─────────────┘
       ▲            └─────────────┘              │
       │                     ▲                  │
       │                     │                  ▼
       └─────────────────────┼──────────────────┘
              10. Response   │        9. Valid
                            │
```

### 3. CORS Headers in Microservices

Each service should:
- **IdP**: Comprehensive CORS for browser clients
- **API Gateway**: Proxy CORS headers from upstream services
- **Internal APIs**: Minimal CORS (service-to-service doesn't need it)
- **Frontend Assets**: Static file serving with appropriate CORS

## Security Implications

### 1. Origin Validation

```typescript
origin: function (origin: any, callback: any) {
    // Allow requests with no origin (mobile apps, server-to-server)
    if (!origin) {
        return callback(null, true);
    }

    // Strict origin checking
    if (AllowedOrigins.indexOf(origin) !== -1) {
        return callback(null, true);
    } else {
        console.warn(`CORS blocked request from origin: ${origin}`);
        return callback(new Error('This origin is not allowed by CORS.'));
    }
}
```

### 2. Credentials Handling

```typescript
credentials: true  // Enables:
// - Cookies
// - Authorization headers
// - Client certificates
// - Session data
```

**Security Note**: When `credentials: true`, the `Access-Control-Allow-Origin` header cannot be `*`. It must be a specific origin.

### 3. Preflight Requests

For "complex" requests, browsers send a preflight OPTIONS request:

**Simple Requests** (no preflight):
- GET, HEAD, POST
- Standard headers only
- Content-Type: application/x-www-form-urlencoded, multipart/form-data, text/plain

**Complex Requests** (require preflight):
- PUT, DELETE, PATCH
- Custom headers (X-Request-ID, Authorization)
- Content-Type: application/json

## Best Practices

### 1. Environment-Specific Origins

```typescript
// Development
const devOrigins = ['http://localhost:*'];

// Staging
const stagingOrigins = ['https://staging.excelytics.co.za'];

// Production
const prodOrigins = ['https://excelytics.co.za'];
```

### 2. Header Minimization

Only expose headers that clients actually need:
- ✅ Rate limiting headers (for client-side handling)
- ✅ Request tracking (for debugging)
- ❌ Internal service headers
- ❌ Database connection info

### 3. Monitoring and Logging

```typescript
// Log blocked requests for security monitoring
console.warn(`CORS blocked request from origin: ${origin}`);
console.warn(`Allowed origins: ${AllowedOrigins.join(', ')}`);
```

### 4. Cache Optimization

```typescript
maxAge: 86400  // 24 hours
```

Reduces preflight requests for the same origin/method/headers combination.

## Troubleshooting

### Common CORS Errors

1. **"Access to fetch at '...' from origin '...' has been blocked by CORS policy"**
   - Check if origin is in AllowedOrigins
   - Verify the request method is allowed
   - Check if custom headers are in allowedHeaders

2. **"Response to preflight request doesn't pass access control check"**
   - Ensure OPTIONS method is allowed
   - Check allowedHeaders configuration
   - Verify credentials setting matches request

3. **"The request client is not a secure context"**
   - HTTPS required for certain features
   - Mixed content issues (HTTPS page → HTTP API)

### Debugging Tools

```bash
# Test CORS with curl
curl -H "Origin: http://localhost:4200" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type,Authorization" \
     -X OPTIONS \
     http://localhost:6002/api/v1/auth/login

# Check response headers
curl -H "Origin: http://localhost:4200" \
     -v \
     http://localhost:6002/api/v1/health
```

### Browser Developer Tools

1. **Network Tab**: Check preflight OPTIONS requests
2. **Console**: Look for CORS error messages
3. **Headers**: Verify Access-Control-* headers in responses

## Microservices Adherence Standards

All Introspection.Finance microservices should adhere to these CORS standards:

### 1. Shared Origins Configuration

```typescript
// All services use the same AllowedOrigins from shared-internals
import { AllowedOrigins } from 'excelytics.shared-internals';
```

### 2. Service-Specific Headers

Each service exposes relevant headers:

**Identity Service**:
- Authentication headers
- Rate limiting headers
- User session headers

**Finance API**:
- Pagination headers
- Data export headers
- Calculation status headers

**File Service**:
- Upload progress headers
- File metadata headers
- Processing status headers

### 3. Consistent Error Handling

```typescript
// Standard CORS error response
{
    "success": false,
    "error": {
        "code": "CORS_ERROR",
        "message": "Origin not allowed by CORS policy"
    }
}
```

### 4. Documentation Requirements

Each service must document:
- Allowed origins
- Required headers
- Exposed headers
- CORS-specific endpoints

---

## Conclusion

This CORS configuration balances security with functionality, enabling our microservices architecture while protecting against unauthorized cross-origin requests. Regular review and updates ensure continued security as our architecture evolves.

For questions or updates to this configuration, consult the development team and update both this documentation and the shared-internals package.
