# Test Scripts Quick Reference

## 🎯 Fixed & New Test Scripts

### ✅ **FIXED: `test:list-names`**

```bash
bun run test:list-names
```

**Output:** Lists all test file names without running them

```
security.test.ts
token-expiration.test.ts
token-management.test.ts
session.test.ts
integration.test.ts
identity-management.test.ts
authentication.test.ts
health.test.ts
session-middleware.test.ts
middleware.test.ts
```

### 🆕 **NEW: `test:count`**

```bash
bun run test:count
```

**Output:** Shows total number of test files

```
10
```

## 🧪 Enhanced Test Scripts

### **File-Specific Testing**

```bash
# Run a specific test file
bun run test:file tests/authentication.test.ts

# Search for specific test patterns
bun run test:grep "Should successfully register"

# Filter tests by suite name
bun run test:filter "Authentication Validation"
```

### **Debugging & Development**

```bash
# Debug tests with inspector
bun run test:debug tests/authentication.test.ts

# Silent output (minimal logging)
bun run test:silent

# Verbose output (detailed logging)
bun run test:verbose
```

### **Test Control**

```bash
# Stop on first failure
bun run test:bail

# Re-run only failed tests
bun run test:failed

# Run only tests marked with .only()
bun run test:focus

# Show tests marked as .todo()
bun run test:todo
```

## 📊 Current Test Files (10 total)

| Test File                     | Purpose             | Script                               |
| ----------------------------- | ------------------- | ------------------------------------ |
| `authentication.test.ts`      | Auth & registration | `bun run test:auth`                  |
| `health.test.ts`              | Health endpoints    | `bun run test:health`                |
| `identity-management.test.ts` | User management     | `bun run test:identity`              |
| `integration.test.ts`         | End-to-end tests    | `bun run test:integration`           |
| `security.test.ts`            | Security tests      | `bun run test:security`              |
| `session.test.ts`             | Session management  | `bun run test:session`               |
| `token-management.test.ts`    | Token operations    | `bun run test:tokens`                |
| `token-expiration.test.ts`    | Token expiration    | _(no dedicated script)_              |
| `session-middleware.test.ts`  | Session middleware  | _(no dedicated script)_              |
| `middleware.test.ts`          | Legacy middleware   | `bun run test:deprecated:middleware` |

## 🚀 Usage Examples

### Development Workflow

```bash
# List all test files
bun run test:list-names

# Run specific test suite
bun run test:auth

# Debug a failing test
bun run test:debug tests/authentication.test.ts

# Re-run only failed tests after fixes
bun run test:failed
```

### CI/CD Pipeline

```bash
# Run all tests and stop on first failure
bun run test:bail

# Run all tests with coverage
bun run test:coverage

# Count total test files for reporting
bun run test:count
```

### Targeted Testing

```bash
# Find tests with specific patterns
bun run test:grep "token validation"

# Run tests for specific feature
bun run test:filter "Identity Management"

# Run a single test file
bun run test:file tests/health.test.ts
```

## 💡 Pro Tips

1. **Use `test:list-names` first** to see all available test files
2. **Use `test:grep` with specific test names** for focused testing
3. **Use `test:bail` in CI** to fail fast and save resources
4. **Use `test:failed` during development** for quick iteration
5. **Use `test:debug` with VS Code** for step-through debugging
