# Cross-Service User Management Strategy

## Overview

Managing user data across multiple microservices (Identity, Finance, Calc, Client) requires careful consideration of data consistency, synchronization, and service boundaries. This document outlines the recommended approach for your Excelytics ecosystem.

## Architecture Principles

### 1. **Single Source of Truth per Domain**
- **Identity Service**: Authentication, authorization, basic user identity
- **Finance Service**: Business user data, organization relationships, financial permissions
- **Calc Service**: User calculation preferences, history, cached results
- **Client Service**: UI preferences, dashboard configurations, user settings

### 2. **Service Ownership Model**

```mermaid
graph TB
    subgraph "Identity Service (IdP)"
        ID_AUTH[Authentication Data]
        ID_ROLES[Roles & Permissions]
        ID_SECURITY[Security Metadata]
    end
    
    subgraph "Finance Service"
        FIN_PROFILE[User Profile]
        FIN_ORG[Organization Data]
        FIN_BUSINESS[Business Metadata]
    end
    
    subgraph "Calc Service"
        CALC_PREFS[Calculation Preferences]
        CALC_HISTORY[Calculation History]
        CALC_CACHE[Cached Results]
    end
    
    subgraph "Client Service"
        CLIENT_UI[UI Preferences]
        CLIENT_DASH[Dashboard Config]
        CLIENT_SETTINGS[User Settings]
    end
    
    ID_AUTH --> FIN_PROFILE
    ID_AUTH --> CALC_PREFS
    ID_AUTH --> CLIENT_UI
    
    classDef identity fill:#e8f5e8
    classDef finance fill:#e3f2fd
    classDef calc fill:#fff3e0
    classDef client fill:#fce4ec
    
    class ID_AUTH,ID_ROLES,ID_SECURITY identity
    class FIN_PROFILE,FIN_ORG,FIN_BUSINESS finance
    class CALC_PREFS,CALC_HISTORY,CALC_CACHE calc
    class CLIENT_UI,CLIENT_DASH,CLIENT_SETTINGS client
```

## Data Synchronization Strategies

### 1. **Event-Driven Synchronization (Recommended)**

```typescript
// Event types for user management
export enum UserEvents {
    USER_CREATED = 'user.created',
    USER_UPDATED = 'user.updated',
    USER_DEACTIVATED = 'user.deactivated',
    USER_DELETED = 'user.deleted',
    EMAIL_CHANGED = 'user.email.changed',
    PROFILE_COMPLETED = 'user.profile.completed'
}

// Event payload structure
export interface UserEvent {
    eventType: UserEvents;
    identityId: string;
    timestamp: Date;
    data: {
        email?: string;
        isActive?: boolean;
        roles?: string[];
        // ... other relevant fields
    };
    metadata: {
        source: string;
        version: string;
        correlationId: string;
    };
}
```

### 2. **Service-to-Service Communication**

```typescript
// Identity Service API for other services
export class IdentityServiceClient {
    async getUserById(identityId: string): Promise<IdentityUser> {
        const response = await this.httpClient.get(`/api/v1/identity/${identityId}`);
        return response.data;
    }
    
    async validateToken(token: string): Promise<TokenValidationResult> {
        const response = await this.httpClient.post('/api/v1/auth/introspect', { token });
        return response.data;
    }
    
    async getUsersByIds(identityIds: string[]): Promise<IdentityUser[]> {
        const response = await this.httpClient.post('/api/v1/identity/batch', { ids: identityIds });
        return response.data;
    }
}
```

## Implementation Recommendations

### 1. **For Each Microservice**

#### **Finance Service User Model**
```typescript
export class User {
    @prop({ required: true, unique: true })
    public identityId!: string; // Link to Identity service
    
    // Business-specific fields
    @prop({ ref: () => Organization, required: true })
    public organizationId!: Ref<Organization>;
    
    @prop({ enum: EnumUserTypes, required: true })
    public userTypeCode!: EnumUserTypes;
    
    // Synced from Identity (cached for performance)
    @prop({ required: true })
    public email!: string;
    
    @prop({ default: true })
    public isActive!: boolean;
    
    // Sync tracking
    @prop()
    public identityLastSyncAt?: Date;
}
```

#### **Calc Service User Model**
```typescript
export class CalcUser {
    @prop({ required: true, unique: true })
    public identityId!: string; // Link to Identity service
    
    // Calc-specific preferences
    @prop({ default: 'standard' })
    public calculationMode!: string;
    
    @prop({ default: 2 })
    public decimalPrecision!: number;
    
    @prop({ default: 'USD' })
    public defaultCurrency!: string;
    
    // Performance settings
    @prop({ default: true })
    public enableCaching!: boolean;
    
    @prop({ default: 1000 })
    public maxCalculationRows!: number;
}
```

#### **Client Service User Model**
```typescript
export class ClientUser {
    @prop({ required: true, unique: true })
    public identityId!: string; // Link to Identity service
    
    // UI preferences
    @prop({ default: 'light' })
    public theme!: string;
    
    @prop({ default: 'en' })
    public language!: string;
    
    @prop({ default: 'UTC' })
    public timezone!: string;
    
    // Dashboard configuration
    @prop({ type: [String], default: [] })
    public favoriteCharts!: string[];
    
    @prop({ type: Object })
    public dashboardLayout?: Record<string, any>;
}
```

### 2. **Cross-Service Query Patterns**

```typescript
// Example: Get complete user data across services
export class UserAggregationService {
    async getCompleteUserProfile(identityId: string) {
        const [identity, finance, calc, client] = await Promise.all([
            identityService.getUser(identityId),
            financeService.getUser(identityId),
            calcService.getUser(identityId),
            clientService.getUser(identityId)
        ]);
        
        return {
            identity: {
                id: identity.id,
                email: identity.email,
                roles: identity.roles,
                isActive: identity.isActive,
                lastLogin: identity.lastLogin
            },
            profile: {
                firstName: finance.firstName,
                lastName: finance.lastName,
                organization: finance.organizationId,
                userType: finance.userTypeCode
            },
            preferences: {
                calculation: {
                    mode: calc.calculationMode,
                    precision: calc.decimalPrecision,
                    currency: calc.defaultCurrency
                },
                ui: {
                    theme: client.theme,
                    language: client.language,
                    timezone: client.timezone
                }
            }
        };
    }
}
```

### 3. **Data Consistency Patterns**

#### **Eventual Consistency with Compensation**
```typescript
export class UserConsistencyService {
    async handleUserEmailChange(identityId: string, newEmail: string) {
        const services = ['finance', 'calc', 'client'];
        const results = [];
        
        // Attempt to update all services
        for (const service of services) {
            try {
                await this.updateUserEmail(service, identityId, newEmail);
                results.push({ service, status: 'success' });
            } catch (error) {
                results.push({ service, status: 'failed', error });
            }
        }
        
        // Handle failures with compensation
        const failures = results.filter(r => r.status === 'failed');
        if (failures.length > 0) {
            await this.scheduleRetry(identityId, newEmail, failures);
        }
        
        return results;
    }
}
```

## Security Considerations

### 1. **Token Validation Across Services**
```typescript
// Middleware for non-Identity services
export const validateIdentityToken = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const token = extractBearerToken(req);
        
        // Validate with Identity service
        const validation = await identityService.validateToken(token);
        
        if (!validation.active) {
            throw new UnauthorizedError('Invalid token');
        }
        
        // Attach user info to request
        req.user = {
            identityId: validation.userId,
            email: validation.email,
            roles: validation.roles,
            permissions: validation.permissions
        };
        
        next();
    } catch (error) {
        next(error);
    }
};
```

### 2. **Service-to-Service Authentication**
```typescript
// Internal service authentication
export class ServiceAuthClient {
    private serviceToken: string;
    
    async getServiceToken(): Promise<string> {
        if (!this.serviceToken || this.isTokenExpired(this.serviceToken)) {
            this.serviceToken = await this.requestServiceToken();
        }
        return this.serviceToken;
    }
    
    async makeAuthenticatedRequest(url: string, options: RequestOptions) {
        const token = await this.getServiceToken();
        return this.httpClient.request(url, {
            ...options,
            headers: {
                ...options.headers,
                'Authorization': `Bearer ${token}`,
                'X-Service-Name': 'finance-service'
            }
        });
    }
}
```

## Monitoring & Observability

### 1. **Cross-Service User Tracking**
```typescript
export class UserActivityTracker {
    async trackUserAction(identityId: string, action: string, service: string, metadata?: any) {
        await this.logger.info('user_action', {
            identityId,
            action,
            service,
            timestamp: new Date(),
            metadata
        });
        
        // Update user activity across services if needed
        await this.updateLastActivity(identityId, service);
    }
}
```

### 2. **Data Sync Monitoring**
```typescript
export class SyncMonitor {
    async checkSyncHealth() {
        const services = ['finance', 'calc', 'client'];
        const results = [];
        
        for (const service of services) {
            const outdatedUsers = await this.findOutdatedUsers(service);
            results.push({
                service,
                outdatedCount: outdatedUsers.length,
                lastSyncCheck: new Date()
            });
        }
        
        return results;
    }
}
```
