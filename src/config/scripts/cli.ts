import {
	createSonnetFileStructureMap,
	createFileStructureMap,
	reinstallDependencies,
	cleanupImports
} from 'excelytics.shared-models';
import path from 'path';

// This script acts as a command-line dispatcher for consuming scripts from the excelytics.shared-models package.
async function main() {
	// The first argument is the command (e.g., 'map', 'reinstall')
	const [command, ...args] = process.argv.slice(2);

	switch (command) {
		//* Create a file structure map with advanced metrics
		case 'map:sonnet': {
			const rootArg = args.find(arg => !arg.startsWith('--'));
			if (!rootArg) {
				console.error("❌ Error: 'map' command requires a path.");
				console.log('Usage: bun script map <path> [--folders-only]');
				process.exit(1);
			}

			const rootPath = path.resolve(rootArg);
			const options = {
				root: './../..',
				foldersOnly: args.includes('--folders-only'),
				showAll: args.includes('--show-all'),
				showAllWithHideList: args.includes('--show-all-with-hide-list')
			};

			await createSonnetFileStructureMap(rootPath, options);
			break;
		}
		//* Create a file structure map
		case 'map': {
			console.log("Running 'map' command from shared scripts...");
			const rootPath = path.resolve('.'); // Always map the current project
			const options = {
				foldersOnly: args.includes('--folders-only'),
				showAll: args.includes('--show-all'),
				showAllWithHideList: args.includes('--show-all-with-hide-list')
			};
			await createFileStructureMap(rootPath, options);
			break;
		}

		//* Reinstall dependencies
		case 'reinstall': {
			console.log("Running 'reinstall' command from shared scripts...");
			await reinstallDependencies();
			break;
		}

		//* Clean up import statements
		case 'clean:imports': {
			console.log("Running 'clean:imports' command from shared scripts...");
			await cleanupImports();
			break;
		}

		default:
			console.error('❌ Error: Unknown or missing command.');
			console.log('\nUsage: bun script <command>');
			console.log('\nAvailable commands:');
			console.log('  map [--folders-only|--show-all]   - Create a file structure map');
			console.log('  reinstall                         - Clean and reinstall dependencies');
			console.log('  clean:imports                     - Clean up TS import statements');
			process.exit(1);
	}
}

main().catch(err => {
	console.error(`\n🚨 Script execution failed for command:`, err);
	process.exit(1);
});