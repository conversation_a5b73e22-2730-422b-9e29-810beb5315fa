import {
	createMongooseHealthCheck,
	type <PERSON>CheckFunction,
	createRedisHealthCheck,
	createHealthHelper,
	ServiceNames
} from 'excelytics.shared-internals';
import { env_idp } from '@app-types/env_';
import mongoose from 'mongoose';

// Define the list of local checks for the IdP
const localChecks: HealthCheckFunction[] = [
	createMongooseHealthCheck(mongoose)
	//createRedisHealthCheck()
];

// Create an instance of the HealthHelper with the IdP's specific configuration
// Tell the factory which service this is, it will automatically exclude this service from the external health checks.
export const healthHelper = createHealthHelper({
	version: env_idp.VERSION as string,
	serviceName: ServiceNames.IDENTITY,
	checks: localChecks
});