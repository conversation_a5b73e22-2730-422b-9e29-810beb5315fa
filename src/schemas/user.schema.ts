import { EnumClientOrigin } from 'excelytics.shared-internals';
import { z } from 'zod';

const UsersSchema = z.object({
	_id: z.string().optional(), // MongoDB's automatically generated _id field
	id: z.string().optional(), // Virtual property (not stored in the database)
	clientId: z.string().min(1, 'Client Id is required'),
	clientOrigin: z.nativeEnum(EnumClientOrigin),
	email: z.string().email('Invalid email format'),
	password: z.string().min(8, 'Password must be at least 8 characters long')
});

export default UsersSchema;