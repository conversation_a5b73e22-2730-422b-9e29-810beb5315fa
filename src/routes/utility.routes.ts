import {
	type AccessTokenPayload,
	CustomSuccessResponse,
	CustomErrorResponse,
	type TokenPayload,
	UnauthorizedError,
	HttpStatus,
	BaseError
} from 'excelytics.shared-internals';
import { UtilityMiddleware } from '@/middleware/utility.middleware';
import type { Response, Request } from 'express';
import asyncHand<PERSON> from 'express-async-handler';
import { Router } from 'express';

const router = Router();

const errorResponder = new CustomErrorResponse();
const utilityMiddleware = new UtilityMiddleware();
const successResponder = new CustomSuccessResponse();

// --- Optional: Simple Access Token Verification Utility Endpoint ---
// This is a non-standard endpoint, primarily for debugging or simple client-side checks.
// The /auth/introspect endpoint is the standard for service-to-service token validation.
router.post(
	'/verify-access-token',
	asyncHandler(async (req: Request, res: Response) => {
		const { token } = req.body;
		if (!token || typeof token !== 'string') {
			// Use your errorResponder for consistency
			errorResponder.SendBadRequestError(res, 'Token (string) is required in the request body.');
			return;
		}

		const result: { valid: boolean; user?: TokenPayload; error?: BaseError } =
			await utilityMiddleware.verifyAccessTokenUtility(token);

		if (result.valid && result.user) {
			// Construct a success response with relevant claims
			successResponder.SendSuccessResponse(res, 'Token is valid and active.', {
				active: true,
				// Expose claims from result.user (TokenPayload) as needed
				sub: result.user.userId,
				email: result.user.email,
				client_id: result.user.clientId,
				token_type: result.user.tokenType,
				iat: Math.floor(result.user.issuedAt.getTime() / 1000),
				exp: result.user.expiresAt ? Math.floor(result.user.expiresAt.getTime() / 1000) : undefined,
				// Add other claims like scope if present in AccessTokenPayload
				scope:
					'permissions' in result.user
						? (result.user as AccessTokenPayload).permissions?.join(' ')
						: undefined
			});
		} else {
			// Token is invalid, expired, or utility encountered an issue.
			// Send a 200 OK with active: false, including error details if available.
			const errorDetail = result.error
				? result.error.toApiResponseError()
				: new UnauthorizedError('Invalid or expired token');

			res.status(HttpStatus.OK).json({
				success: true, // The endpoint itself processed the request
				data: { active: false },
				error: errorDetail
			});
		}
	})
);

export default router;