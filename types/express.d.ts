/// <reference types="express" />
/// <reference types="express-session" />

import type { AccessTokenPayload } from 'excelytics.shared-internals';

declare global {
	namespace Express {
		interface Request {
			user?: AccessTokenPayload;
			roles?: string[] | undefined;
		}
	}
}

declare module 'express-session' {
	interface SessionData {
		data?: {
			created: string;
			lastAccessed: string;
			userId?: string;
			email?: string;
			initialized?: boolean;
		};
	}
}

// This ensures the file becomes a module
export {};