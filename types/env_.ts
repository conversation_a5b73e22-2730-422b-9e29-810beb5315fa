import {
	parseEnvWithZodError<PERSON><PERSON>atter,
	IdpEnvironmentSchema,
	EnumLogLevel
} from 'excelytics.shared-internals';
import { z } from 'zod';

// Parse the base IdP environment first
const env_idp_base = IdpEnvironmentSchema.parse(Bun.env);

// Parse the additional test variable separately
const testConfigSchema = z.object({
	/** Flag to enable verbose logging in tests */
	TEST_VERBOSE_LOGGING: z
		.preprocess(val => String(val).toLowerCase() === "true", z.boolean())
		.default(false),

	TEST_LOG_LEVEL: z.nativeEnum(EnumLogLevel).default(EnumLogLevel.Debug)
});

const testConfig = testConfigSchema.parse(Bun.env);

// Combine them into the final env_idp export
export const env_idp = {
	...env_idp_base,
	...testConfig
};

export const env_idp_parsed = parseEnvWithZodErrorFormatter(IdpEnvironmentSchema, Bun.env);