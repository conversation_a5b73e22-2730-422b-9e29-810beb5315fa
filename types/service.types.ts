import type { BaseError , AccessTokenPayload, TokenPayload } from 'excelytics.shared-internals';

import type { Identity } from '@/models/identity.model';

// A generic base for service operation results, indicating success or failure
export interface ServiceOperationResult<TData = undefined, TError = BaseError> {
	success: boolean;
	data?: TData;
	error?: TError;
}

// Result type for user registration operations
export type RegistrationServiceResult = ServiceOperationResult<
	// Exclude password from user data
	{ user: Partial<Omit<Identity, 'password'>> }
>;

// Result type for user login and initial token generation
export type LoginServiceResult = ServiceOperationResult<
	{
		accessToken: string;
		refreshToken: string;
		user: Partial<Omit<Identity, 'password'>>;
		tokenPayload: AccessTokenPayload; // Payload of the new access token
	}
>;

// Result type for refreshing an access token
export type RefreshTokenServiceResult = ServiceOperationResult<
	{
		accessToken: string;
		newRefreshToken?: string; // If implementing token rotation
		tokenPayload: AccessTokenPayload; // Payload of the new access token
	}
>;

// Result type for token introspection
export type ExcelyticsServiceResult = ServiceOperationResult<
	{
		active: boolean;
		claims?: TokenPayload; // The claims from the token if active
	}
	// If !active, data might be undefined or { active: false }
	// The 'active' field can also be part of the TData for simplicity:
	// ServiceOperationResult<{ active: boolean; claims?: TokenPayload }>
	// Let's refine this one slightly for clarity:
>;

export interface ExcelyticsData {
	active: boolean;
	/** Claims from the token if active and valid. */
	claims?: TokenPayload;
}

export type ExcelyticsServiceResultRefined = ServiceOperationResult<ExcelyticsData>;


// Result type for generic identity (user) data retrieval or manipulation
export type IdentityServiceDataResult<TIdentityData> = ServiceOperationResult<TIdentityData>;
// Examples:
// IdentityServiceDataResult<Partial<Omit<Identity, 'password'>> | null> for getByEmail
// IdentityServiceDataResult<Partial<Omit<Identity, 'password'>>[]> for getAllUsers
// IdentityServiceDataResult<{ id: string } | null> for delete operations