import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';

/** @type {import('eslint').Linter.Config[]} */
export default [
	{ files: ["**/*.{js,mjs,cjs,ts}"] },
	{ languageOptions: { globals: globals.node } },
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	{
		// Ignore the following directories
		ignores: [
			'dist',
			'documentation',
			'packages/Excelytics.SharedModels/dist',
			'packages/Excelytics.SharedInternals/dist',
			'packages/Excelytics.SharedDtos/dist',
			'**/node_modules/**',
			'**/*.md'
		],
	},
	{
		rules: {
			'@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
			'@typescript-eslint/no-explicit-any': 'warn',
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'indent': ['error', 'tab', { SwitchCase: 1 }],

			'no-warning-comments': [
				'warn',
				{
					'terms': ['todo', 'fixme'],
					'location': 'start'
				}
			],

			// Rule to enforce no extensions for TS imports
			'import/extensions': [
				'off',
				'ignorePackages',
				{ ts: 'never' }
			],
		},
	},
	{
		// Special rules for scripts directory
		files: ["scripts/**/*.ts"],
		rules: {
			'@typescript-eslint/no-unused-vars': 'off', // Scripts might have unused imports
			'no-console': 'off', // Allow console.log in scripts
			'@typescript-eslint/no-explicit-any': 'off' // Allow any in scripts for flexibility
		}
	}
];