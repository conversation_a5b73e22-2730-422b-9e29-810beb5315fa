{"name": "introspection.shared", "description": "Shared packages and utilities for Introspection microservices", "version": "1.0.1", "type": "module", "private": true, "scripts": {"clean": "bun run --filter='*' clean", "changeset:add": "bun run changeset add", "release": "bun run build:all && bun changeset version && bun changeset publish", "build:all": "for dir in packages/*/; do cd \"$dir\" && bun run build; cd -; done", "lint": "eslint .", "lint:fix": "eslint . --fix", "pipeline:lint:output": "bunx eslint . --format json --output-file eslint-results.json", "reinstall": "bun run packages/Excelytics.SharedModels/src/scripts/cli.ts reinstall", "clean:imports": "bun run packages/Excelytics.SharedModels/src/scripts/cli.ts clean:imports", "analyze:structure:simple": "bun run packages/Excelytics.SharedModels/src/scripts/cli.ts map . --show-all-with-hide-list", "analyze:structure:sonnet": "bun run packages/Excelytics.SharedModels/src/scripts/cli.ts map:enhanced . --show-all-with-hide-list", "map:root:sonnet": "bun run packages/Excelytics.SharedModels/src/scripts/cli.ts map:enhanced . --show-all-with-hide-list"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/bun": "latest", "eslint": "^9.29.0", "globals": "^15.15.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}, "engines": {"bun": ">=1.0.0"}}