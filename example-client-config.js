// Example: How consumers would configure their hide/ignore/code_ext lists
/** @type {import('@excelytics/shared-models').ExcelyticsConfig} */
module.exports = {
	// Frontend-specific ignores (extending defaults)
	ignoresList: {
		add: [
			// Next.js specific
			'.next',
			'out',
      
			// Build artifacts
			'build',
			'dist',
      
			// Environment files
			'.env.local',
			'.env.development.local',
			'.env.production.local',
      
			// Testing
			'coverage',
			'.nyc_output',
      
			// IDE
			'.vscode/settings.json',
      
			// Temporary files
			'*.tmp',
			'*.temp'
		]
	},
  
	// Hide sensitive content (extending defaults)
	hideContentsList: {
		add: [
			// Environment files
			'.env',
			'.env.development',
			'.env.production',

			// Config files with sensitive data
			'next.config.js',
			'tailwind.config.js',

			// API keys or secrets
			'firebase-config.js',

			// Large generated files
			'package-lock.json',
			'yarn.lock',
			'bun.lock'
		]
	},

	// Frontend-specific code extensions (extending defaults)
	codeExtensions: {
		add: [
			// React/Next.js specific
			'.mdx',
			'.astro',

			// Styling
			'.styl',
			'.sass',

			// Config files
			'.config.js',
			'.config.ts',

			// GraphQL
			'.graphql',
			'.gql',

			// Testing
			'.test.js',
			'.test.ts',
			'.spec.js',
			'.spec.ts',

			// Documentation
			'.stories.js',
			'.stories.ts'
		]
	}
};