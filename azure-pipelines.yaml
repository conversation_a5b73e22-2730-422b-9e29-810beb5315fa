# azure-pipelines.yaml
# Renamed from azure-pipelines.yml
trigger:
  - master

pr:
  branches:
    include:
      - master

variables:
  - group: SharedFeedCredentials

pool:
  name: 'Unraid'

jobs:
  - job: AnalyzeAndBuild
    displayName: 'Analyze and Build'
    steps:
      - template: pipelines/steps/setup-environment.yaml
      - template: pipelines/steps/install-dependencies.yaml
      - template: pipelines/steps/analyze-structure.yaml
      - template: pipelines/steps/build-and-test.yaml
      - template: pipelines/steps/run-tests.yaml
        parameters:
          testEnvironment: 'test'
          enableVerboseLogging: false
          enableSensitiveLogging: false

      - script: |
          echo "Generating build summary..."
          PACKAGE_COUNT=$(find packages/ -name "*.ts" 2>/dev/null | grep -v node_modules | wc -l || echo "0")
          TOTAL_PACKAGES=$(find packages/ -maxdepth 1 -type d | tail -n +2 | wc -l || echo "0")

          cat > build-summary.md << EOF
          # 🚀 Build Summary - Introspection Shared Packages

          ## 📊 Build Information
          - **Build Number:** $(Build.BuildNumber)
          - **Source Branch:** $(Build.SourceBranchName)
          - **Commit:** $(Build.SourceVersion)
          - **Build Time:** $(date)

          ## 📦 Analysis
          - **Total Packages:** $TOTAL_PACKAGES
          - **TypeScript Files:** $PACKAGE_COUNT
          - **Structure File:** $(StructureFileName)

          ## 🧪 Test Results
          - **Tests Passed:** $(TestsPassed)
          - **Tests Failed:** $(TestsFailed)
          - **Tests Skipped:** $(TestsSkipped)
          - **Success Rate:** $(TestSuccessRate)%

          ## 🏗️ Packages
          $(find packages/ -maxdepth 1 -type d -name "*" | tail -n +2 | sed 's/packages\//- /')
          EOF
        displayName: 'Generate Build Summary'

      - task: CopyFiles@2
        displayName: 'Prepare Artifacts'
        inputs:
          Contents: |
            $(StructureFilePath)
            build-summary.md
            eslint-results.json
            $(Build.ArtifactStagingDirectory)/test-results/**
          TargetFolder: '$(Build.ArtifactStagingDirectory)/reports'
          flattenFolders: false

      - task: PublishMarkdownReports@1
        displayName: 'Publish Analysis Reports'
        inputs:
          contentPath: '$(Build.ArtifactStagingDirectory)/reports'
          indexFile: '$(StructureFileName)'
          latexFormula: true
        condition: succeeded()

      - task: PublishBuildArtifacts@1
        displayName: 'Publish Artifacts'
        inputs:
          PathtoPublish: '$(Build.ArtifactStagingDirectory)'
          ArtifactName: 'introspection-shared-$(Build.BuildNumber)'

# TEMPORARILY REMOVED TILL READY TO FIX
#  - job: PublishPackages
#    displayName: 'Publish Changed Packages'
#    dependsOn: AnalyzeAndBuild
#    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
#    steps:
#      - template: pipelines/steps/setup-environment.yaml
#      - template: pipelines/steps/install-dependencies.yaml
#      - template: pipelines/steps/check-versions.yaml
#        parameters:
#          shouldPublish: true
#      - template: pipelines/steps/publish-packages.yaml