{
    "compilerOptions": {
        // Language and Environment
        "types": ["bun-types", "node"],
        "lib": ["ESNext"],
        "target": "ESNext",

        // Modules
        "module": "ESNext",
        "moduleDetection": "force",
        "rootDir": ".",
        "baseUrl": ".",

        // Bundler Mode
        "moduleResolution": "bundler",
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "noEmit": true,

        // Type Checking
        "strict": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "resolveJsonModule": true
    },
    "include": [
        "scripts/**/*"
    ],
    "exclude": [
        "dist",
        "node_modules",
        "packages/*/dist",
        "packages/*/node_modules"
    ]
}