/**
 * Company entities module.
 * Centralizes model exports to prevent the "chicken and egg" problem of circular dependencies when consuming models from this package.
 * @module company-entities
 */
import { getModelForClass } from '@typegoose/typegoose';

// 1. Import all the CLASSES from their files
import { Company } from './company.model';
import { Individual } from './individual.model';
import { CompanyGroup } from './company-group.model';
import { CompanyDirectory } from './company-directory.model';

/**
 * Mongoose model for Company entities.
 * Used for database operations on the Company collection.
 */
export const CompanyModel = getModelForClass(Company);

/**
 * Mongoose model for Individual entities.
 * Used for database operations on the Individual collection.
 */
export const IndividualModel = getModelForClass(Individual);

/**
 * Mongoose model for CompanyGroup entities.
 * Used for database operations on the CompanyGroup collection.
 */
export const CompanyGroupModel = getModelForClass(CompanyGroup);

/**
 * Mongoose model for CompanyDirectory entities.
 * Used for database operations on the CompanyDirectory collection.
 */
export const CompanyDirectoryModel = getModelForClass(CompanyDirectory);

// 3. Re-export the CLASSES so you can use them for type-hinting
export { Company, CompanyDirectory, CompanyGroup, Individual };