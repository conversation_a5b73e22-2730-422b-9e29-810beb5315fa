/**
 * Company Group model definition.
 * Represents a parent organization that can contain multiple companies.
 * @module company-group.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';
import { EnumEditions } from '../../constants';

/**
 * Company Group entity class.
 * Represents a top-level organization that can contain multiple companies.
 * Used for multi-company organizations with shared management.
 */
@index({ email: 1, name: 1 })
@modelOptions({ schemaOptions: { collection: 'CompanyGroup' } })
export class CompanyGroup {
	/**
	 * Edition code for the group's subscription level.
	 * Determines feature access and limitations.
	 */
	@prop({ enum: EnumEditions, required: true, type: Number })
	public editionCode!: EnumEditions;

	/**
	 * Group name.
	 * Required field for group identification.
	 */
	@prop({ required: true })
	public name!: string;

	/**
	 * Group email address.
	 * Required and must be unique across all company groups.
	 */
	@prop({ required: true, unique: true })
	public email!: string;

	/**
	 * Group contact phone number.
	 * Required for communication purposes.
	 */
	@prop({ required: true })
	public cellNumber!: string;
}