/**
 * Company Directory model definition.
 * Represents a registered company in the business directory.
 * @module company-directory.model
 */
import { index, modelOptions, prop, type Ref } from '@typegoose/typegoose';
import { CompanyGroup } from './company-group.model';
import { Individual } from './individual.model';
import { Company } from './company.model';

/**
 * Company Directory entity class.
 * Stores official company registration information.
 * Can be associated with a CompanyGroup, Company, or Individual.
 */
@index({ fkCompanyGroupId: 1, companyRegistrationNumber: 1 }, { unique: true, sparse: true })
@index({ fkCompanyId: 1, companyRegistrationNumber: 1 }, { unique: true, sparse: true })
@index({ fkIndividualId: 1, companyRegistrationNumber: 1 }, { unique: true, sparse: true })
@modelOptions({ schemaOptions: { collection: 'CompanyDirectory' }})
export class CompanyDirectory {
    /**
     * Reference to a company group.
     * Optional - used when the directory entry is associated with a group.
     */
    @prop({ ref: () => CompanyGroup })
	public fkCompanyGroupId?: Ref<CompanyGroup>;

    /**
     * Reference to a company.
     * Optional - used when the directory entry is associated with a company.
     */
    @prop({ ref: () => Company })
    public fkCompanyId?: Ref<Company>;

    /**
     * Reference to an individual.
     * Optional - used when the directory entry is associated with an individual.
     */
    @prop({ ref: () => Individual })
    public fkIndividualId?: Ref<Individual>;

    /**
     * Official company registration number.
     * Required and must be unique across the directory.
     */
    @prop({ required: true, unique: true })
    public companyRegistrationNumber!: string;

    /**
     * Official registered company name.
     * Required field for legal identification.
     */
    @prop({ required: true })
    public companyName!: string;
}