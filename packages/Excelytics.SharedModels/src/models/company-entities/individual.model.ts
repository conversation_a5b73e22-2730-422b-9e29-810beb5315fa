/**
 * Individual model definition.
 * Represents a person who can be a standalone user or associated with a company.
 * @module individual.model
 */
import { index, modelOptions, prop, type Ref } from '@typegoose/typegoose';
import { EnumEditions } from '../../constants';
import { Company } from './company.model';

/**
 * Individual entity class.
 * Represents a person who can be a standalone user or an employee of a company.
 */
@index({ email: 1, name: 1 })
@modelOptions({ schemaOptions: { collection: 'Individual' }})
export class Individual {
	// TODO: Future iteration
	// if fkCompanyId is null that mean it is a individual level
	// if has CompanyId it is an employee account linking to that company
    /**
     * Reference to the associated company.
     * Optional - if null, this is a standalone individual account.
     */
    @prop({ ref: () => Company })
	public fkCompanyId?: Ref<Company>;

    /**
     * Edition code for the individual's subscription level.
     * Determines feature access and limitations.
     */
    @prop({ enum: EnumEditions, required: true, type: Number })
    public editionCode!: EnumEditions;

    /**
     * Individual's name.
     * Required field for user identification.
     */
    @prop({ required: true })
    public name!: string;

    /**
     * Individual's email address.
     * Required and must be unique across all individuals.
     */
    @prop({ required: true, unique: true })
    public email!: string;

    /**
     * Individual's contact phone number.
     * Required for communication purposes.
     */
    @prop({ required: true })
    public cellNumber!: string;
}