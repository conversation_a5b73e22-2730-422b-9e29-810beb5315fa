/**
 * Company model definition.
 * Represents a company entity in the system.
 * @module company.model
 */
import { index, modelOptions, prop, type Ref } from '@typegoose/typegoose';
import { CompanyGroup } from './company-group.model';
import { EnumEditions } from '../../constants';

/**
 * Company entity class.
 * Represents a business entity that can belong to a company group.
 * Companies can have individual users associated with them.
 */
@index({ email: 1, name: 1 })
@modelOptions({ schemaOptions: { collection: 'Company' }})
export class Company {
	// TODO: Future iteration
	// if fkCompanyGroupId is null then it is just a company level
	// if fkCompanyGroupId has value it belongs in that group
    /**
     * Reference to the parent company group.
     * Optional - if null, this is a standalone company.
     */
    @prop({ ref: () => CompanyGroup })
	public fkCompanyGroupId?: Ref<CompanyGroup>;

    /**
     * Edition code for the company's subscription level.
     * Determines feature access and limitations.
     */
    @prop({ enum: EnumEditions, required: true, type: Number })
    public editionCode!: EnumEditions;

    /**
     * Company name.
     * Required field for company identification.
     */
    @prop({ required: true })
    public name!: string;

    /**
     * Company email address.
     * Required and must be unique across all companies.
     */
    @prop({ required: true, unique: true })
    public email!: string;

    /**
     * Company contact phone number.
     * Required for communication purposes.
     */
    @prop({ required: true })
    public cellNumber!: string;
}