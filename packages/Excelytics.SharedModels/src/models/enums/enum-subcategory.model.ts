/**
 * Subcategory enum model definition.
 * Represents the different subcategories available in the system.
 * @module enum-subcategory.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';

/**
 * EnumSubCategory entity class.
 * Stores subcategory reference data that belongs to parent categories.
 * Maps to the EnumSubCategory collection in the database.
 */
// Note SubCategoryCode is not unique as they can have te same code if they're in a different category
@index({ subCategoryCode: 1, description: 1 })
@modelOptions({ schemaOptions: { collection: 'EnumSubCategory' }})
export class EnumSubCategory {
    /**
     * Identifier for the subcategory.
     * Not unique on its own - uniqueness is determined by combination with categoryCode.
     */
    @prop({ required: true })
	public subCategoryCode!: number;

    /**
     * Reference to the parent category.
     * Required to establish the category-subcategory relationship.
     */
    @prop({ required: true })
    public categoryCode!: number;

    /**
     * Human-readable description of the subcategory.
     * Required field for display purposes.
     */
    @prop({ required: true })
    public description!: string;
}