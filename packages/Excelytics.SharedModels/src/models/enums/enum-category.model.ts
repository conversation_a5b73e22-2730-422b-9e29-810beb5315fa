/**
 * Category enum model definition.
 * Represents the different categories available in the system.
 * @module enum-category.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';

/**
 * EnumCategory entity class.
 * Stores category reference data used for classifying items.
 * Maps to the EnumCategory collection in the database.
 */
@index({ editionCode: 1, description: 1 })
@modelOptions({ schemaOptions: { collection: 'EnumCategory' }})
export class EnumCategory {
    /**
     * Unique identifier for the category.
     * Required and must be unique across all categories.
     */
    @prop({ required: true, unique: true })
	public categoryCode!: number;

    /**
     * Human-readable description of the category.
     * Required field for display purposes.
     */
    @prop({ required: true })
    public description!: string;
}