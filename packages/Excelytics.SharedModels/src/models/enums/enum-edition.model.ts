/**
 * Edition enum model definition.
 * Represents the different product editions available in the system.
 * @module enum-edition.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';

/**
 * EnumEdition entity class.
 * Stores edition reference data used for subscription levels.
 * Maps to the EnumEdition collection in the database.
 */
@index({ editionCode: 1, description: 1  })
@modelOptions({ schemaOptions: { collection: 'EnumEdition' }})
export class EnumEdition {
    /**
     * Unique identifier for the edition.
     * Required and must be unique across all editions.
     */
    @prop({ required: true, unique: true })
	public editionCode!: number;

    /**
     * Human-readable description of the edition.
     * Required field for display purposes.
     */
    @prop({ required: true })
    public description!: string;
}