/**
 * Enum models module.
 * Centralizes model exports to prevent the "chicken and egg" problem of circular dependencies when consuming models from this package.
 * @module enum-models
 */
import { getModelForClass } from '@typegoose/typegoose';

// 1. Import all the CLASSES from their files
import { EnumAccountType } from './enum-account-type.model';
import { EnumCategory } from './enum-category.model';
import { EnumEdition } from './enum-edition.model';
import { EnumSubCategory } from './enum-subcategory.model';
import { EnumUserType } from './enum-user-type.model';

/**
 * Mongoose model for EnumAccountType entities.
 * Used for database operations on the EnumAccountType collection.
 */
export const EnumAccountTypeModel = getModelForClass(EnumAccountType);

/**
 * Mongoose model for EnumCategory entities.
 * Used for database operations on the EnumCategory collection.
 */
export const EnumCategoryModel = getModelForClass(EnumCategory);

/**
 * Mongoose model for EnumEdition entities.
 * Used for database operations on the EnumEdition collection.
 */
export const EnumEditionModel = getModelForClass(EnumEdition);

/**
 * Mongoose model for EnumSubCategory entities.
 * Used for database operations on the EnumSubCategory collection.
 */
export const EnumSubCategoryModel = getModelForClass(EnumSubCategory);

/**
 * Mongoose model for EnumUserType entities.
 * Used for database operations on the EnumUserType collection.
 */
export const EnumUserTypeModel = getModelForClass(EnumUserType);

// 3. Re-export the CLASSES so you can use them for type-hinting
export { EnumAccountType, EnumCategory, EnumEdition, EnumSubCategory, EnumUserType };