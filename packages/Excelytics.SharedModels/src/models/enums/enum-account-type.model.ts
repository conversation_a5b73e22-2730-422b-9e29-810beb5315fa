/**
 * Account Type enum model definition.
 * Represents the different types of accounts in the system.
 * @module enum-account-type.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';

/**
 * EnumAccountType entity class.
 * Stores account type reference data used for categorizing accounts.
 * Maps to the EnumAccountType collection in the database.
 */
@index({ categoryCode: 1, description: 1 })
@modelOptions({ schemaOptions: { collection: 'EnumAccountType' }})
export class EnumAccountType {
    /**
     * Unique identifier for the account type.
     * Required and must be unique across all account types.
     */
    @prop({ required: true, unique: true })
	public accountTypeCode!: number;

    /**
     * Human-readable description of the account type.
     * Required field for display purposes.
     */
    @prop({ required: true })
    public description!: string;
}