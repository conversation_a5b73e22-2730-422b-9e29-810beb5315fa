/**
 * User Type enum model definition.
 * Represents the different types of users in the system.
 * @module enum-user-type.model
 */
import { index, modelOptions, prop } from '@typegoose/typegoose';

/**
 * EnumUserType entity class.
 * Stores user type reference data used for categorizing users.
 * Maps to the EnumUserType collection in the database.
 */
// Indexing userTypeCode and description for efficient searching and retrieval
@index({ userTypeCode: 1, description: 1 })
@modelOptions({ schemaOptions: { collection: 'EnumUserType' }})
export class EnumUserType {
    /**
     * Unique identifier for the user type.
     * Required and must be unique across all user types.
     */
    @prop({ required: true, unique: true })
	public userTypeCode!: number;

    /**
     * Human-readable description of the user type.
     * Required field for display purposes.
     */
    @prop({ required: true })
    public description!: string;
}