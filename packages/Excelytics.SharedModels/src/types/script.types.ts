/**
 * Script-related types, used for defining CLI options and script flags across the application.
 * The CLI options are passed in from the consuming project's dispatcher script.
 * This will be the single source of truth for command-line driven flags.
 * @module script.types
 */

// --- CLI and script types ---
/**
 * Options for the CLI commands.
 */
export interface CliOptions {
    showAll?: boolean;
    foldersOnly?: boolean;
    showAllWithHideList?: boolean;
}

/**
 * Options for the import cleanup function.
 */
export interface CleanupImportOptions {
    /**
     * An array of directory paths to clean up.
     */
    directories?: string[];
}

// --- Advanced analytics types ---
/**
 * Metrics for the project.
 */
export interface ProjectMetrics {
    totalFiles: number;
    totalDirectories: number;
    totalSize: number;
    linesOfCode: number;
    filesByExtension: Map<string, number>;
    locByExtension: Map<string, number>;
    largestFiles: Array<{name: string, size: number, path: string}>;
    oldestFiles: Array<{name: string, modified: Date, path: string}>;
    newestFiles: Array<{name: string, modified: Date, path: string}>;
}

// --- Legacy constants for backward compatibility ---
/**
 * @deprecated Use ConfigManager.getConfig().codeExtensions instead.
 * File extensions for LOC counting.
 */
export const CODE_EXTENSIONS: Set<string> = new Set([
	'.ts', '.js', '.tsx', '.jsx', '.cs', '.sql', '.vue', '.html',
	'.css', '.scss', '.less', '.json', '.yaml', '.yml', '.md', '.env',
	'.env.development', 'json'
]);

/**
 * @deprecated Use CliOptions instead.
 */
export interface MapStructureOptions {
    showAll?: boolean;
    foldersOnly?: boolean;
    showAllWithHideList?: boolean;
}

/**
 * @deprecated Use ScriptFlagOption instead.
 */
export enum ScriptFlagOption {
    Default = 'default',
    ShowAll = 'showAll',
    FoldersOnly = 'foldersOnly',
    ShowAllWithHideList = 'showAllWithHideList'
}

/**
 * @deprecated Use ScriptHelper.processUserFlagOptions instead.
 */
export interface ScriptFlags {
    foldersOnly: boolean;
    showAll: boolean;
    option: ScriptFlagOption;
}

/**
 * @deprecated Use ConfigManager.getConfig().ignoresList instead.
 */
export const IGNORE_LIST: Set<string> = new Set([
	'node_modules',
	'.DS_Store',
	'.cache',
	'dist',
	'.git'
]);

/**
 * @deprecated Use ConfigManager.getConfig().hideContentsList instead.
 */
export const HIDE_CONTENTS_LIST: Set<string> = new Set([
	'package-lock.json',
	'bun.lock'
]);