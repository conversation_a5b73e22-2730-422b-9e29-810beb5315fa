/**
 * Company directory-related types.
 * Used for representing and managing company directory data.
 * @module company-directory.types
 */
import { Types } from 'mongoose';

/**
 * Represents the data structure of a company directory entry.
 * Used for both database and API communication.
 */
export type CompanyDirectoryData = {
    _id?: Types.ObjectId;
    companyRegistrationNumber: string;
    companyName: string;
    fkCompanyId?: string;
    fkCompanyGroupId?: string;
    fkIndividualId?: string;
};

/**
 * Represents the keys of a company directory entry.
 * Used for filtering and querying company directory data.
 */
export type CompanyDirectoryKeys = {
    fkIndividualId?: string | undefined;
    fkCompanyId?: string | undefined;
    fkCompanyGroupId?: string | undefined;
};

/**
 * String representation of the keys of a company directory entry.
 * Used for filtering and querying company directory data.
 */
export const CompanyDirectoryStringKeys = [
	'fkCompanyGroupId',
	'fkIndividualId',
	'fkCompanyId'
] as const;