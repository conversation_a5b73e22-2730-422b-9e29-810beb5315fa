/**
 * FileStructureHelper provides functions for generating file structure maps.
 * @module file-structure.helper
 */
import path from 'path';
import fs from 'fs/promises';

import { ListConfigHelper } from './list-config.helper';
import type { ExcelyticsConfig, CliOptions } from '../types';
import { TreeGenerationHelper } from './tree-generation.helper';
import { MetricsAnalysisHelper } from './metrics-analysis.helper';
import { EnhancedMarkdownHelper } from './enhanced-markdown.helper';

/**
 * Validate that the given path is a directory
 */
async function validateDirectory(rootPath: string): Promise<void> {
	try {
		const stats = await fs.stat(rootPath);
		if (!stats.isDirectory()) {
			console.error(`❌ Error: The path "${rootPath}" is not a directory.`);
			process.exit(1);
		}
	} catch (error) {
		console.error(`❌ Error: The path "${rootPath}" does not exist.`);
		process.exit(1);
	}
}

/**
 * Generate a simple file structure map using pre-loaded configuration
 */
export async function generateSimpleFileStructure(
	rootPath: string,
	cliOptions: CliOptions,
	config: Required<ExcelyticsConfig>
): Promise<void> {
	console.log("📁 Generating simple file structure map...");

	// Validate the root path
	await validateDirectory(rootPath);

	// Resolve the ListConfig types to Set<string>
	const resolvedIgnoresList = ListConfigHelper.resolveListConfigToSet(config.ignoresList);
	const resolvedHideContentsList = ListConfigHelper.resolveListConfigToSet(config.hideContentsList);

	// Set up internal ScriptFlags based on the incoming CliOptions
	const flags = ListConfigHelper.processUserFlagOptions(cliOptions);

	// Generate the tree structure string
	const treeStructure = await TreeGenerationHelper.generateMarkdownTree(
		rootPath,
		flags,
		resolvedIgnoresList,
		resolvedHideContentsList
	);

	// Generate markdown content
	const rootFolderName = path.basename(rootPath);
	const markdownContent = EnhancedMarkdownHelper.generateSimpleMarkdownContent(
		rootFolderName,
		treeStructure
	);

	// Generate filename and write file
	const outputFileName = EnhancedMarkdownHelper.generateOutputFileName(rootFolderName);
	await fs.writeFile(outputFileName, markdownContent);
	console.log(`✅ Success! Project structure map saved to: ${outputFileName}`);
}

/**
 * Generate an enhanced file structure map with metrics using preloaded configuration
 */
export async function generateEnhancedFileStructure(
	rootPath: string,
	cliOptions: CliOptions,
	config: Required<ExcelyticsConfig>
): Promise<void> {
	console.log("📊 Generating enhanced file structure map with metrics...");

	// Validate the root path
	await validateDirectory(rootPath);

	// Resolve the ListConfig types to Set<string>
	const resolvedIgnoresList = ListConfigHelper.resolveListConfigToSet(config.ignoresList);
	const resolvedHideContentsList = ListConfigHelper.resolveListConfigToSet(config.hideContentsList);
	const resolvedCodeExtensions = ListConfigHelper.resolveListConfigToSet(config.codeExtensions);

	// Set up internal ScriptFlags based on the incoming CliOptions
	const flags = ListConfigHelper.processUserFlagOptions(cliOptions);

	// Generate the tree structure string
	const treeStructure = await TreeGenerationHelper.generateMarkdownTree(
		rootPath,
		flags,
		resolvedIgnoresList,
		resolvedHideContentsList
	);

	// Generate metrics using the helper
	const metrics = await MetricsAnalysisHelper.generateProjectMetrics(rootPath, flags, resolvedIgnoresList, resolvedCodeExtensions);

	// Get additional info
	const gitInfo = await MetricsAnalysisHelper.getGitInfo(rootPath);
	const packageInfo = await MetricsAnalysisHelper.analyzePackageJson(rootPath);

	// Generate enhanced markdown content with metrics
	const rootFolderName = path.basename(rootPath);
	const markdownContent = EnhancedMarkdownHelper.generateEnhancedMarkdownContent(
		rootFolderName,
		rootPath,
		treeStructure,
		metrics,
		gitInfo,
		packageInfo
	);

	// Generate filename and write file
	const outputFileName = EnhancedMarkdownHelper.generateOutputFileName(rootFolderName, 'enhanced');
	await fs.writeFile(outputFileName, markdownContent);
	console.log(`✅ Success! Enhanced project structure map saved to: ${outputFileName}`);
}

/**
 * FileStructureHelper provides functions for generating file structure maps.
 * @module file-structure.helper
 */
export const FileStructureHelper = {
	generateEnhancedFileStructure,
	generateSimpleFileStructure,
	validateDirectory
};