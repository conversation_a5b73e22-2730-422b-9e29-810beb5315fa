/**
 * TreeGenerationHelper provides functions for generating directory tree structures.
 * @module tree-generation.helper
 */
import path from 'path';
import fs from 'fs/promises';
import { statSync } from 'fs';
import { type ScriptFlags, ScriptFlagOption } from '../types';

/**
 * Recursively traverses a directory and builds a string representation of its structure.
 * Note, has a recursive call.
 * @param directoryPath The absolute path to the directory to traverse.
 * @param flags The command-line flags that control the output.
 * @param resolvedIgnoresList The Set of items to ignore.
 * @param resolvedHideContentsList The Set of items whose contents should be hidden.
 * @param prefix The string prefix to use for the current level of the tree.
 * @returns A promise that resolves to the string representation of the directory tree.
 */
async function generateMarkdownTree(
	directoryPath: string,
	flags: ScriptFlags,
	resolvedIgnoresList: Set<string>,
	resolvedHideContentsList: Set<string>,
	prefix: string = '',
): Promise<string> {
	let tree = '';
	try {
		const files: string[] = await fs.readdir(directoryPath);

		let filteredFiles: string[] = [];

		switch (flags.option) {
			case ScriptFlagOption.FoldersOnly:
				filteredFiles = files.filter((file) => {
					const filePath = path.join(directoryPath, file);
					const stats = statSync(filePath);
					return stats.isDirectory() && !resolvedIgnoresList.has(file);
				});
				break;
			case ScriptFlagOption.ShowAllWithHideList:
				filteredFiles = files.filter((file) =>
					!resolvedHideContentsList.has(file) &&
                    !resolvedIgnoresList.has(file)
				);
				break;
			case ScriptFlagOption.ShowAll:
				filteredFiles = files;
				break;
			default:
				filteredFiles = files.filter((file) => !resolvedIgnoresList.has(file));
		}

		for (let i = 0; i < filteredFiles.length; i++) {
			const file = filteredFiles[i] as string;
			const isLast = i === filteredFiles.length - 1;
			const filePath = path.join(directoryPath, file);
			const stats = await fs.stat(filePath);

			const connector = isLast ? "└── " : "├── ";
			const newPrefix = prefix + (isLast ? "    " : "│   ");

			if (stats.isDirectory()) {
				tree += `${prefix}${connector}${file}/\n`;
				// Recurse if --show-all is on, OR if the folder is not in the hide list.
				if (flags.showAll || !resolvedHideContentsList.has(file)) {
					tree += await generateMarkdownTree(
						filePath,
						flags,
						resolvedIgnoresList,
						resolvedHideContentsList,
						newPrefix
					);
				}
			} else if (!flags.foldersOnly) {
				tree += `${prefix}${connector}${file}\n`;
			}
		}
	} catch (error) {
		console.error(`Error reading directory ${directoryPath}:`, error);
	}

	return tree;
}

/**
 * Generate a simple tree structure without advanced filtering.
 * Note, has a recursive call.
 */
async function generateSimpleTree(
	directoryPath: string,
	ignoreSet: Set<string>,
	prefix: string = ''
): Promise<string> {
	let tree = '';
	try {
		const files = await fs.readdir(directoryPath);
		const filteredFiles = files.filter(file => !ignoreSet.has(file));

		for (let i = 0; i < filteredFiles.length; i++) {
			const file = filteredFiles[i];
			const isLast = i === filteredFiles.length - 1;
			const filePath = path.join(directoryPath, file);
			const stats = await fs.stat(filePath);

			const connector = isLast ? "└── " : "├── ";
			const newPrefix = prefix + (isLast ? "    " : "│   ");

			if (stats.isDirectory()) {
				tree += `${prefix}${connector}${file}/\n`;
				tree += await generateSimpleTree(filePath, ignoreSet, newPrefix);
			} else {
				tree += `${prefix}${connector}${file}\n`;
			}
		}
	} catch (error) {
		console.error(`Error reading directory ${directoryPath}:`, error);
	}

	return tree;
}

/**
 * Count total files and directories in a tree.
 * Note, has a recursive call.
 */
async function countTreeItems(
	directoryPath: string,
	ignoreSet: Set<string>
): Promise<{ files: number; directories: number }> {
	let files = 0;
	let directories = 0;

	try {
		const items = await fs.readdir(directoryPath);
		const filteredItems = items.filter(item => !ignoreSet.has(item));

		for (const item of filteredItems) {
			const itemPath = path.join(directoryPath, item);
			const stats = await fs.stat(itemPath);

			if (stats.isDirectory()) {
				directories++;
				const subCounts = await countTreeItems(itemPath, ignoreSet);
				files += subCounts.files;
				directories += subCounts.directories;
			} else {
				files++;
			}
		}
	} catch (error) {
		console.error(`Error counting items in ${directoryPath}:`, error);
	}

	return { files, directories };
}

/**
 * TreeGenerationHelper provides functions for generating directory tree structures.
 * @module tree-generation.helper
 */
export const TreeGenerationHelper = {
	generateMarkdownTree,
	generateSimpleTree,
	countTreeItems
};