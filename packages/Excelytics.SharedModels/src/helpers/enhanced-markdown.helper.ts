/**
 * EnhancedMarkdownHelper provides functions for generating markdown content with project analysis.
 * @module enhanced-markdown.helper
 */
import type { ProjectMetrics } from '../types';
import { MetricsAnalysisHelper } from './metrics-analysis.helper';

/**
 * Format date and time for South African timezone (SAST - UTC+2)
 * Returns format: (Mon) dd-mm-yyyy 10:22pm SAST
 */
function formatSouthAfricanDateTime(date: Date = new Date()): string {
	const options: Intl.DateTimeFormatOptions = {
		timeZone: 'Africa/Johannesburg',
		weekday: 'short',
		day: '2-digit',
		month: '2-digit',
		year: 'numeric',
		hour: '2-digit',
		minute: '2-digit',
		hour12: true
	};

	const formatter = new Intl.DateTimeFormat('en-ZA', options);
	const parts = formatter.formatToParts(date);

	const weekday = parts.find(part => part.type === 'weekday')?.value;
	const day = parts.find(part => part.type === 'day')?.value;
	const month = parts.find(part => part.type === 'month')?.value;
	const year = parts.find(part => part.type === 'year')?.value;
	const hour = parts.find(part => part.type === 'hour')?.value;
	const minute = parts.find(part => part.type === 'minute')?.value;
	const dayPeriod = parts.find(part => part.type === 'dayPeriod')?.value?.toLowerCase();

	return `(${weekday}) ${day}-${month}-${year} ${hour}:${minute}${dayPeriod} SAST`;
}

/**
 * Format date for South African timezone (SAST - UTC+2)
 * Returns format: dd-mm-yyyy
 */
function formatSouthAfricanDate(date: Date = new Date()): string {
	const options: Intl.DateTimeFormatOptions = {
		timeZone: 'Africa/Johannesburg',
		day: '2-digit',
		month: '2-digit',
		year: 'numeric'
	};

	const formatter = new Intl.DateTimeFormat('en-ZA', options);
	const parts = formatter.formatToParts(date);

	const day = parts.find(part => part.type === 'day')?.value;
	const month = parts.find(part => part.type === 'month')?.value;
	const year = parts.find(part => part.type === 'year')?.value;

	return `${day}-${month}-${year}`;
}

/**
 * Generate file extension distribution table
 */
function generateExtensionTable(metrics: ProjectMetrics): string {
	return Array.from(metrics.filesByExtension.entries())
		.sort((a, b) => b[1] - a[1])
		.slice(0, 15)
		.map(([ext, count]) => {
			const loc = metrics.locByExtension.get(ext) || 0;
			return `| ${ext || 'no ext'} | ${count} | ${loc.toLocaleString()} |`;
		})
		.join('\n');
}

/**
 * Generate largest files table
 */
function generateLargestFilesTable(metrics: ProjectMetrics): string {
	return metrics.largestFiles
		.slice(0, 10)
		.map(file => `| ${file.name} | ${MetricsAnalysisHelper.formatFileSize(file.size)} | ${file.path} |`)
		.join('\n');
}

/**
 * Generate recently modified files table
 */
function generateRecentFilesTable(metrics: ProjectMetrics): string {
	return metrics.newestFiles
		.slice(0, 5)
		.map(file => `| ${file.name} | ${formatSouthAfricanDate(file.modified)} | ${file.path} |`)
		.join('\n');
}

/**
 * Generate enhanced markdown content with full project analysis
 */
function generateEnhancedMarkdownContent(
	rootFolderName: string,
	rootPath: string,
	treeStructure: string,
	metrics: ProjectMetrics,
	gitInfo: string,
	packageInfo: string,
	startTime: number = Date.now()
): string {
	const extensionTable = generateExtensionTable(metrics);
	const largestFilesTable = generateLargestFilesTable(metrics);
	const recentFilesTable = generateRecentFilesTable(metrics);

	return `# 📊 Project Analysis: ${rootFolderName}

## 📋 Summary
- **Generated:** ${formatSouthAfricanDateTime()}
- **Analysis Duration:** ${Date.now() - startTime}ms
- **Root Path:** \`${rootPath}\`

## 🔍 Git Information
${gitInfo}

## 📦 Package Information  
${packageInfo}

## 📈 Project Metrics

### 📊 Overview
- **Total Files:** ${metrics.totalFiles.toLocaleString()}
- **Total Directories:** ${metrics.totalDirectories.toLocaleString()}
- **Total Size:** ${MetricsAnalysisHelper.formatFileSize(metrics.totalSize)}
- **Lines of Code:** ${metrics.linesOfCode.toLocaleString()}

### 📋 File Distribution
| Extension | Files | Lines of Code |
|-----------|-------|---------------|
${extensionTable}

### 📂 Largest Files
| File | Size | Path |
|------|------|------|
${largestFilesTable}

### 🕐 Recently Modified Files
| File | Modified | Path |
|------|----------|------|
${recentFilesTable}

## 🌳 Directory Structure
\`\`\`
${rootFolderName}/
${treeStructure}
\`\`\`

## 🏗️ Architecture Notes
This is part of the **Introspection Consulting** microservices architecture:
- **Backend (BE):** Introspection.Finance
- **Identity Provider (IdP):** Introspection.Finance.Identity  
- **Calculation Engine:** Introspection.Finance.Calc
- **Frontend (FE):** Introspection.Finance.Client

**Tech Stack:** MongoDB, TypeScript, Shadcn/ui, Bun
**Deployment:** Unraid Server with Nginx Gateway

---
*Generated by Enhanced Project Structure Analyzer*
`;
}

/**
 * Generate simple markdown content (basic structure only)
 */
function generateSimpleMarkdownContent(
	rootFolderName: string,
	treeStructure: string
): string {
	return `# Project Structure: ${rootFolderName}
*Generated on: ${formatSouthAfricanDateTime()}*

Here is the file and directory structure of the project:
\`\`\`
${rootFolderName}/
${treeStructure}
\`\`\`
`;
}

/**
 * Generate output filename with optional suffix
 */
function generateOutputFileName(rootFolderName: string, suffix?: string): string {
	const dateString = formatSouthAfricanDate(); // dd-mm-yyyy
	const suffixPart = suffix ? `_${suffix}` : '';
	return `${dateString}_${rootFolderName}${suffixPart}_structure.md`;
}

/**
 * EnhancedMarkdownHelper provides functions for generating markdown content with project analysis.
 * @module enhanced-markdown.helper
 */
export const EnhancedMarkdownHelper = {
	generateEnhancedMarkdownContent,
	generateSimpleMarkdownContent,
	generateLargestFilesTable,
	generateRecentFilesTable,
	generateExtensionTable,
	generateOutputFileName
};