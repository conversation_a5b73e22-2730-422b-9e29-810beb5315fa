/**
 * @deprecated This file is for backward compatibility only.
 * Please use the focused helpers directly.
 */

// Re-export focused helpers for backward compatibility and convenience
export { ListConfigHelper } from './list-config.helper';
export { TreeGenerationHelper } from './tree-generation.helper';
export { MetricsAnalysisHelper } from './metrics-analysis.helper';
export { EnhancedMarkdownHelper } from './enhanced-markdown.helper';

// Import the focused helpers
import { ListConfigHelper } from './list-config.helper';
import { TreeGenerationHelper } from './tree-generation.helper';
import { EnhancedMarkdownHelper } from './enhanced-markdown.helper';

// Import types
import type { CliOptions, ScriptFlags, ListConfig } from '../types';

/**
 * @deprecated Use ListConfigHelper.resolveListConfigToSet instead
 * Helper function to convert ListConfig to Set<string>.
 */
export function resolveListConfigToSet(configValue: string | string[] | ListConfig): Set<string> {
	return ListConfigHelper.resolveListConfigToSet(configValue);
}

/**
 * @deprecated Use TreeGenerationHelper.generateMarkdownTree instead
 */
async function generateMarkdownTree(
	directoryPath: string,
	flags: ScriptFlags,
	resolvedIgnoresList: Set<string>,
	resolvedHideContentsList: Set<string>,
	prefix: string = '',
): Promise<string> {
	return TreeGenerationHelper.generateMarkdownTree(
		directoryPath,
		flags,
		resolvedIgnoresList,
		resolvedHideContentsList,
		prefix
	);
}

/**
 * @deprecated Use ListConfigHelper.logResolvedConfiguration instead
 */
function logResolvedConfiguration(resolvedIgnoresList: Set<string>, resolvedHideContentsList: Set<string>) {
	return ListConfigHelper.logResolvedConfiguration(
		resolvedIgnoresList,
		resolvedHideContentsList
	);
}

/**
 * @deprecated Use ListConfigHelper.processUserFlagOptions instead
 */
function processUserFlagOptions(cliOptions: CliOptions): ScriptFlags {
	return ListConfigHelper.processUserFlagOptions(cliOptions);
}

/**
 * @deprecated Use DiagnosticHelper.logResolvedConfiguration instead
 */
export const DiagnosticHelper = {
	logResolvedConfiguration
};

/**
 * @deprecated Use ListConfigHelper directly
 */
export const DeprecatedScriptHelper = {
	processUserFlagOptions,
	resolveListConfigToSet,
};

/**
 * @deprecated Use EnhancedMarkdownHelper.generateSimpleMarkdownContent instead
 */
function generateMarkdownContent(rootFolderName: string, treeStructure: string, includeMetrics?: boolean, metrics?: any): string {
	if (includeMetrics && metrics) {
		// This is complex, delegate to enhanced helper
		return EnhancedMarkdownHelper.generateEnhancedMarkdownContent(
			rootFolderName,
			process.cwd(),
			treeStructure,
			metrics,
			'Git information not available',
			'Package information not available'
		);
	} else {
		return EnhancedMarkdownHelper.generateSimpleMarkdownContent(rootFolderName, treeStructure);
	}
}

/**
 * @deprecated Use EnhancedMarkdownHelper.generateOutputFileName instead
 */
function generateOutputFileName(rootFolderName: string, suffix: string = ''): string {
	return EnhancedMarkdownHelper.generateOutputFileName(rootFolderName, suffix);
}

/**
 * @deprecated Use fs.writeFile directly or create a file helper
 */
async function writeMarkdownFile(filename: string, content: string): Promise<void> {
	const fs = await import('fs/promises');
	try {
		await fs.writeFile(filename, content);
		console.log(`✅ Success! Project structure map saved to: ${filename}`);
	} catch (error) {
		console.error("❌ Error writing the output file:", error);
		process.exit(1);
	}
}

/**
 * @deprecated Use EnhancedMarkdownHelper and TreeGenerationHelper directly
 */
export const MarkdownGenerationHelper = {
	generateMarkdownContent,
	generateOutputFileName,
	generateMarkdownTree,
	writeMarkdownFile
};