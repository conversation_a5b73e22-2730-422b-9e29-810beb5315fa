/**
 * MetricsAnalysisHelper provides functions for analyzing project metrics.
 * @module metrics-analysis.helper
 */
import path from 'path';
import fs from 'fs/promises';
import { execSync } from 'child_process';
import type { ProjectMetrics, CliOptions } from '../types';

/**
 * Count lines in a file
 */
async function countLinesInFile(filePath: string): Promise<number> {
	try {
		const content = await fs.readFile(filePath, 'utf-8');
		return content.split('\n').length;
	} catch {
		return 0;
	}
}

/**
 * Format file size in human-readable format
 */
function formatFileSize(bytes: number): string {
	const units = ['B', 'KB', 'MB', 'GB'];
	let size = bytes;
	let unitIndex = 0;

	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}

	return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Get Git repository information
 */
async function getGitInfo(rootPath: string): Promise<string> {
	try {
		const branch = execSync('git rev-parse --abbrev-ref HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		const commit = execSync('git rev-parse --short HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		const commitCount = execSync('git rev-list --count HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		return `**Branch:** ${branch} | **Commit:** ${commit} | **Total Commits:** ${commitCount}`;
	} catch {
		return 'Git information not available';
	}
}

/**
 * Analyze package.json file
 */
async function analyzePackageJson(rootPath: string): Promise<string> {
	try {
		const packagePath = path.join(rootPath, 'package.json');
		const content = await fs.readFile(packagePath, 'utf-8');
		const pkg = JSON.parse(content);

		const deps = Object.keys(pkg.dependencies || {}).length;
		const devDeps = Object.keys(pkg.devDependencies || {}).length;

		return `**Dependencies:** ${deps} | **Dev Dependencies:** ${devDeps} | **Version:** ${pkg.version || 'N/A'}`;
	} catch {
		return 'Package.json not found or invalid';
	}
}

/**
 * Recursively collect project metrics
 */
async function collectMetrics(
	directoryPath: string,
	cliOptions: CliOptions,
	metrics: ProjectMetrics,
	ignoreSet: Set<string>,
	codeExtensions: Set<string>
): Promise<void> {
	try {
		const files = await fs.readdir(directoryPath);
		const filteredFiles = files.filter((file) => !ignoreSet.has(file));

		for (const file of filteredFiles) {
			const filePath = path.join(directoryPath, file);
			const stats = await fs.stat(filePath);

			if (stats.isDirectory()) {
				metrics.totalDirectories++;
				await collectMetrics(filePath, cliOptions, metrics, ignoreSet, codeExtensions);
			} else {
				metrics.totalFiles++;
				metrics.totalSize += stats.size;

				// File extension analysis
				const ext = path.extname(file).toLowerCase();
				metrics.filesByExtension.set(ext, (metrics.filesByExtension.get(ext) || 0) + 1);

				// Lines of code counting
				if (codeExtensions.has(ext)) {
					const lines = await countLinesInFile(filePath);
					metrics.linesOfCode += lines;
					metrics.locByExtension.set(ext, (metrics.locByExtension.get(ext) || 0) + lines);
				}

				// Track largest files
				metrics.largestFiles.push({
					name: file,
					size: stats.size,
					path: path.relative(process.cwd(), filePath)
				});

				// Track file ages
				metrics.oldestFiles.push({
					name: file,
					modified: stats.mtime,
					path: path.relative(process.cwd(), filePath)
				});

				metrics.newestFiles.push({
					name: file,
					modified: stats.mtime,
					path: path.relative(process.cwd(), filePath)
				});
			}
		}
	} catch (error) {
		console.error(`Error analyzing directory ${directoryPath}:`, error);
	}
}

/**
 * Initialize empty project metrics
 */
function initializeMetrics(): ProjectMetrics {
	return {
		totalFiles: 0,
		totalDirectories: 0,
		totalSize: 0,
		linesOfCode: 0,
		filesByExtension: new Map(),
		locByExtension: new Map(),
		largestFiles: [],
		oldestFiles: [],
		newestFiles: []
	};
}

/**
 * Process and sort metrics after collection
 */
function processMetrics(metrics: ProjectMetrics): ProjectMetrics {
	// Sort and limit arrays
	metrics.largestFiles.sort((a, b) => b.size - a.size).splice(10);
	metrics.oldestFiles.sort((a, b) => a.modified.getTime() - b.modified.getTime()).splice(10);
	metrics.newestFiles.sort((a, b) => b.modified.getTime() - a.modified.getTime()).splice(10);
    
	return metrics;
}

/**
 * Generate full project analysis with metrics
 */
async function generateProjectMetrics(
	rootPath: string,
	cliOptions: CliOptions,
	ignoreSet: Set<string>,
	codeExtensions: Set<string>
): Promise<ProjectMetrics> {
	console.log("📈 Analyzing project metrics...");

	const metrics = initializeMetrics();
	await collectMetrics(rootPath, cliOptions, metrics, ignoreSet, codeExtensions);

	return processMetrics(metrics);
}

/**
 * MetricsAnalysisHelper provides functions for analyzing project metrics.
 * @module metrics-analysis.helper
 */
export const MetricsAnalysisHelper = {
	generateProjectMetrics,
	analyzePackageJson,
	initializeMetrics,
	countLinesInFile,
	formatFileSize,
	collectMetrics,
	processMetrics,
	getGitInfo
};