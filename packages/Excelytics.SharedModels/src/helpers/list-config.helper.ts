/**
 * Helper functions for managing list configurations.
 * @module list-config.helper
 */
import { ScriptFlagOption } from '../types';
import type { ListConfig, ListExtension, CliOptions, ScriptFlags } from '../types';

/**
 * Check if a value is a ListExtension object
 */
function isListExtension(value: any): value is ListExtension {
	return value && typeof value === 'object' && 'add' in value;
}

/**
 * Resolve a ListConfig to a Set<string> for efficient lookups
 */
function resolveListConfigToSet(listConfig: ListConfig): Set<string> {
	if (Array.isArray(listConfig)) {
		return new Set(listConfig);
	} else if (isListExtension(listConfig)) {
		// For ListExtension, we need the base defaults to extend from
		// This should be handled at a higher level where defaults are available
		return new Set(listConfig.add);
	} else {
		return new Set([listConfig]);
	}
}

/**
 * Merge a ListConfig with default values
 */
function mergeListConfigWithDefaults(
	listConfig: ListConfig, 
	defaults: string[]
): Set<string> {
	if (Array.isArray(listConfig)) {
		// Replace defaults entirely
		return new Set(listConfig);
	} else if (isListExtension(listConfig)) {
		// Extend defaults
		return new Set([...defaults, ...listConfig.add]);
	} else {
		// Single value replaces defaults
		return new Set([listConfig]);
	}
}

/**
 * Process CLI options into internal ScriptFlags
 * @deprecated Use ScriptHelper.processUserFlagOptions instead.
 */
function processUserFlagOptions(cliOptions: CliOptions): ScriptFlags {
	const flags: ScriptFlags = {
		foldersOnly: !!cliOptions.foldersOnly,
		showAll: !!cliOptions.showAll,
		option: ScriptFlagOption.Default,
	};

	if (cliOptions.foldersOnly) {
		flags.option = ScriptFlagOption.FoldersOnly;
	} else if (cliOptions.showAll) {
		flags.option = ScriptFlagOption.ShowAll;
	} else if (cliOptions.showAllWithHideList) {
		flags.option = ScriptFlagOption.ShowAllWithHideList;
	}

	return flags;
}

/**
 * Validate that a path exists and is a directory
 */
async function validateDirectoryPath(dirPath: string): Promise<boolean> {
	try {
		const fs = await import('fs/promises');
		const stats = await fs.stat(dirPath);
		return stats.isDirectory();
	} catch {
		return false;
	}
}

/**
 * Log resolved configuration for debugging
 */
function logResolvedConfiguration(
	resolvedIgnoresList: Set<string>,
	resolvedHideContentsList: Set<string>
): void {
	console.log('📋 Using configuration:');
	console.log('   🚫 Ignoring:', Array.from(resolvedIgnoresList));
	console.log('   👁️  Hiding contents of:', Array.from(resolvedHideContentsList));
}

/**
 * ListConfigHelper provides functions for managing list configurations.
 * @module list-config.helper
 */
export const ListConfigHelper = {
	mergeListConfigWithDefaults,
	logResolvedConfiguration,
	resolveListConfigToSet,
	processUserFlagOptions,
	validateDirectoryPath,
	isListExtension
};