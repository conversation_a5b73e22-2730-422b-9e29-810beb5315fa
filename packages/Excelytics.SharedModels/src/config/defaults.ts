/**
 * Default configuration values for the Excelytics SharedModels package.
 * @module defaults
 */
import { ExcelyticsConfig } from '../types';

/**
 * The default configuration for the Excelytics SharedModels package.
 * These values are used when no user configuration is provided or when
 * a user extends a list rather than replacing it.
 */
export const defaultConfig: Required<ExcelyticsConfig> = {
	rootDirectory: process.cwd(), // Default to current working directory
	ignoresList: [
		'node_modules',
		'.DS_Store',
		'.cache',
		'dist',
		'.git'
	],
	hideContentsList: [
		'package-lock.json',
		'bun.lock'
	],
	codeExtensions: [
		'.ts', '.js', '.tsx', '.jsx', '.cs', '.sql', '.vue', '.html',
		'.css', '.scss', '.less', '.json', '.yaml', '.yml', '.md', '.env',
		'.env.development', 'json'
	]
};