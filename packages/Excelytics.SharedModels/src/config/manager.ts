/**
 * Configuration management for the Excelytics SharedModels package.
 * This module handles loading, merging, and caching of configuration.
 * @module config.manager
 */
import { ExcelyticsConfig, isListExtension, ListConfig, ListExtension } from '../types';
import { cosmiconfig, CosmiconfigResult } from 'cosmiconfig';
import { defaultConfig } from './defaults';
import deepmerge from 'deepmerge';

// The module name used by cosmiconfig to search for configuration files.
// e.g.,.excelyticsrc.json, excelytics.config.js, etc.
const MODULE_NAME = 'excelytics';

/**
 * A Singleton class to manage loading, merging, and caching of configuration.
 * This class ensures that configuration is loaded and processed only once.
 */
export class ConfigManager {
	private static instance: ConfigManager;
	private finalConfig: Required<ExcelyticsConfig> | null = null;
	private configSourcePath: string | null = null;

	// Private constructor to enforce the Singleton pattern.
	private constructor() {}

	/**
     * Gets the single instance of the ConfigManager.
     */
	public static getInstance(): ConfigManager {
		if (!ConfigManager.instance) {
			ConfigManager.instance = new ConfigManager();
		}

		return ConfigManager.instance;
	}

	/**
     * Retrieves the final, merged configuration.
     * On the first call, it searches for, loads, and processes the config.
     * On subsequent calls, it returns a cached version for performance.
     * @returns A promise that resolves to the final configuration object.
     */
	public async getConfig(): Promise<Required<ExcelyticsConfig>> {
		if (this.finalConfig) {
			return this.finalConfig;
		}

		const explorer = cosmiconfig(MODULE_NAME);
		const result: CosmiconfigResult = await explorer.search();

		const userConfig: Partial<ExcelyticsConfig> = result?.config?? {};
		this.configSourcePath = result?.filepath?? 'Defaults only';

		// For diagnostic purposes, log where the config was found.
		// This is invaluable for consumers debugging their setup. [1]
		console.log(`[Excelytics] Using configuration from: ${this.configSourcePath}`);

		// Custom merge logic for list properties
		const mergedConfig = deepmerge(defaultConfig, userConfig, {
			customMerge: (key) => {
				if (key === 'ignoresList' || key === 'hideContentsList' || key === 'codeExtensions') {
					// target will be from defaultConfig (string[])
					// source will be from userConfig (string | string[] | ListExtension | undefined)
					// This function is the custom merger for the specified keys.
					// It receives the two values to be merged: target (from defaults) and source (from user).
					return (target: ListExtension, source: ListConfig | undefined) => {
						let currentList: string[] = [];

						// 1. Determine the base list from the target (defaultConfig)
						if (Array.isArray(target)) {
							currentList = target;
						} else if (isListExtension(target)) {
							// This case shouldn't happen if defaultConfig uses string[]
							// but good for robustness if `target` somehow becomes ListExtension
							currentList = target.add;
						} else if (typeof target === 'string') {
							// If target is a string (e.g., from an earlier merge step that resolved to a string)
							// Treat it as an empty list or a list containing that single string, depending on intent.
							// For a list, typically it means a replacement. So the base is effectively empty or based on that.
							// For simplicity, if target is a string, we treat the base as empty.
							currentList = [];
						}

						// 2. Apply user-provided source configuration
						if (source === undefined) {
							// If user didn't provide this config, use the target (default)
							return currentList;
						} else if (isListExtension(source)) {
							// User wants to add to the list
							// Return the actual merged array, not another ListExtension object.
							// The `ListConfig` type for the final result of this `customMerge`
							// function should be `string[]` for these properties if we want to
							// consume them as `string[]`.
							return [...currentList, ...source.add]; // This returns string[]
						} else if (Array.isArray(source)) {
							// User wants to replace with a new array
							return source; // This returns string[]
						} else if (typeof source === 'string') {
							// User wants to replace with a single string (as per ListConfig string type)
							// This means the final config value for ignoresList/hideContentsList will be a string,
							// not a string[]. This is an important distinction for consumers.
							return source; // This returns string
						}

						// Fallback - should ideally not be reached if types are exhaustive
						return currentList;
					};
				}
				// For all other properties, return undefined to use the default deepmerge behavior.
				return undefined;
			},
		});

		// The result of deepmerge, with our custom merge, needs to be asserted.
		// If a user provides a `string` as ListConfig, then `ignoresList` will be `string`.
		// If a user provides `string[]` or `ListExtension`, then `ignoresList` will be `string[]`.
		// So, the final `ignoresList` and `hideContentsList` will be `string | string[]`.
		// The `Required<ExcelyticsConfig>` means these properties exist, but their *value* types
		// must still match the union `ListConfig`.
		this.finalConfig = mergedConfig as Required<ExcelyticsConfig>;

		return this.finalConfig;
	}

	/**
     * Clears the cached configuration, forcing a reload on the next getConfig() call.
     * This is useful primarily for testing environments.
     */
	public clearCache(): void {
		this.finalConfig = null;
		this.configSourcePath = null;
	}
}