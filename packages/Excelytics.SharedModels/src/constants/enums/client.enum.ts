/**
 * @module client.enum
 */

/**
 * Enum representing different client origins.
 * Used for tracking and managing client requests and responses.
 */
export enum EnumClientOrigin {
    /** Main ecosystem requests (our SaaS platform) */
    Excelytics = 1,
    /** Requests routed through our API Gateway/Nginx */
    API_Gateway = 2,
    /** External/third-party requests from unknown origins */
    Other = 3
}

/**
 * Enum representing different client paths.
 * Used for routing and managing client requests and responses.
 */
export enum EnumClientPath {
    /** Finance backend service */
    Finance = 'excelytics.finance',
    /** Identity provider service (IdP) */
    Identity = 'excelytics.identity',
    /** Frontend/web service */
    Client = 'excelytics.client',
    /** Calculation engine service */
    Calc = 'excelytics.calc',
    /** API Gateway service */
    Gateway = 'excelytics.gateway',
    /** Fallback: unknown or unspecified client path */
    Other = 'excelytics.other'
}