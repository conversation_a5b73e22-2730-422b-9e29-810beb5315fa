/**
 * @module user.enum
 */

/**
 * Enum representing different user types.
 * Used for categorizing users within the system.
 */
export enum EnumUserTypes {
    Employee = 1,
    Company = 2,
    Admin = 3
}

/**
 * Enum representing different account types.
 * Used for categorizing user accounts within the system.
 */
export enum EnumAccountTypes {
    Personal = 1,
    Business = 2,
    Organization = 3
}