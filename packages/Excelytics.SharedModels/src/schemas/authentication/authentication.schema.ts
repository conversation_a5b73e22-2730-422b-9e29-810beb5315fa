/**
 * Authentication-related Zod schemas for server-side validation.
 * Used for validating authentication requests on the server-side.
 * @module authentication.schema
 */
import { z } from 'zod';
import { EnumClientOrigin } from '../../constants';
import { ObjectIdInputSchema } from '../utility.schema';

/**
 * Schema for validating passwords.
 * Used for validating passwords on the server-side.
 */
const PasswordSchema = z.string()
	.min(8, 'Password must be at least 8 characters long')
	.regex(/[A-Z]/, "Password must contain at least one uppercase letter")
	.regex(/[a-z]/, "Password must contain at least one lowercase letter")
	.regex(/[0-9]/, "Password must contain at least one number")
	.regex(/[^A-Za-z0-9]/, "Password must contain at least one special character");

/**
 * Define the PRISTINE BASE schemas for the DTOs.
 * These schemas define the simple SHAPE of the data for type inference.
 * They must be "clean" (no ZodEffects).
 * Notice clientId is just a string here.
 */
const RegisterBaseSchema = z.object({
	clientId: z.string(),
	clientOrigin: z.nativeEnum(EnumClientOrigin),
	email: z.string().email('Invalid email format'),
	password: z.string()
});

const LoginBaseSchema = z.object({
	email: z.string().email('Invalid email format'),
	password: z.string()
});

/**
 * Export the DTO types by inferring from the CLEAN base schemas.
 * This is now safe and will not cause a recursive loop.
 */
export type RegisterDTO = z.infer<typeof RegisterBaseSchema>;
export type LoginDTO = z.infer<typeof LoginBaseSchema>;

/**
 * Define the powerful VALIDATION schemas which transforms the input.
 * These are used to validate incoming request bodies.
 * They can contain complex rules and transformations.
 */
export const RegisterValidationSchema = z.object({
	clientId: ObjectIdInputSchema, // Uses the powerful input schema here
	clientOrigin: z.nativeEnum(EnumClientOrigin),
	email: z.string().email('Invalid email format'),
	password: PasswordSchema
});

export const LoginValidationSchema = z.object({
	email: z.string().email('Invalid email format'),
	password: PasswordSchema
});