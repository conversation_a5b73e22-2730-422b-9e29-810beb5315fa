/**
 * Zod schemas for validating company directory data.
 * Used for validating company directory data across microservices.
 * @module company-directory.schema
 */
import { z } from 'zod';
import { ObjectIdStringSchema } from '../utility.schema';

/**
 * Define the PRISTINE BASE schemas for the DTOs.
 * These schemas define the simple SHAPE of the data for type inference.
 * They must be "clean" (no ZodEffects).
 */
const CompanyDirectoryResponseBaseSchema = z.object({
	fkCompanyGroupId: ObjectIdStringSchema.optional(),
	fkCompanyId: ObjectIdStringSchema.optional(),
	fkIndividualId: ObjectIdStringSchema.optional(),
	companyRegistrationNumber: z
		.string()
		.min(1, 'Company registration number is required'),
	companyName: z.string().min(1, 'Company name is required')
});

/**
 * This is the schema for a single response object.
 * It extends the base schema with the _id field.
 */
export const CompanyDirectoryResponseSchema =
    CompanyDirectoryResponseBaseSchema.extend({
    	_id: z.string()
    });

/**
 * This is the schema for an array of response objects.
 */
export const CompanyDirectoryArraySchema = z.array(
	CompanyDirectoryResponseSchema
);