/**
 * Zod schemas for validating company directory input.
 * Used for validating company directory input across microservices.
 * @module company-directory.input.schema
 */
import { z } from 'zod';
import { CompanyDirectoryStringKeys } from '../../types';
import { ObjectIdStringSchema } from '../utility.schema';

/**
 * Define the PRISTINE BASE schemas for the DTOs.
 * These schemas define the simple SHAPE of the data for type inference.
 * They must be "clean" (no ZodEffects).
 * Notice how the foreign keys are just strings here.
 */
const CompanyDirectoryInputBaseSchema = z.object({
	fkCompanyGroupId: ObjectIdStringSchema.optional(),
	fkCompanyId: ObjectIdStringSchema.optional(),
	fkIndividualId: ObjectIdStringSchema.optional(),
	companyRegistrationNumber: z
		.string()
		.min(1, 'Company registration number is required'),
	companyName: z.string().min(1, 'Company name is required')
});

/**
 * This is the schema for creating a new company directory entry.
 * It refines the base schema to ensure that only one of the foreign keys is provided.
 */
export const CompanyDirectoryCreateSchema = CompanyDirectoryInputBaseSchema.refine(
	data => {
		const count = CompanyDirectoryStringKeys.filter(
			key => data[key] != null,
		).length;
		return count === 1;
	},
	{
		message: 'Exactly one of fkCompanyGroupId, fkCompanyId, or fkIndividualId must be provided'
	}
);

/**
 * This is the schema for updating an existing company directory entry.
 * It allows all fields to be optional.
 */
export const CompanyDirectoryUpdateSchema =
    CompanyDirectoryInputBaseSchema.partial().extend({
    	id: z.string().optional(),
    });