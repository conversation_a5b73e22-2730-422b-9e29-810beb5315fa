/**
 * Zod schemas for company-related entities.
 * Used for validating company data across microservices.
 * @module company.schema
 */
import { z } from 'zod';
import { EnumEditions } from '../../constants';
import { ObjectIdInputSchema } from '../utility.schema';

/**
	z.lazy() is a wrapper that tells TypeScript and Zod:
		"Don't try to resolve the schema inside me right now. Wait until you actually need to parse or validate data with it."
	This defers the resolution, breaking the infinite loop at compile time.
*/

/**
 * The PRISTINE BASE schema for company-related entities.
 * Contains only the core fields, with no relationships.
 * Used for validating company data across microservices.
 */
const CompanyBaseSchema = z.object({
	_id: z.any().optional(),
	editionCode: z.nativeEnum(EnumEditions),
	name: z
		.string({ required_error: "Name is required" })
		.min(1, { message: 'Name cannot be empty' }),
	email: z
		.string({ required_error: "Email is required" })
		.email({ message: 'Invalid email address' }),
	cellNumber: z
		.string({ required_error: "Cell number is required" })
		.min(10, "Cell number must be at least 10 characters")
});


/**
 * A CompanyGroup is just the base fields.
 */
export const CompanyGroupSchema = CompanyBaseSchema;

/**
 * A Company is the base fields PLUS a relationship to a CompanyGroup
 */
export const CompanySchema = CompanyBaseSchema.extend({
	fkCompanyGroupId: z.lazy(() => ObjectIdInputSchema).optional()
});

/**
 * An Individual is the base fields PLUS a relationship to a Company
 */
export const IndividualSchema = CompanyBaseSchema.extend({
	fkCompanyId: z.lazy(() => CompanySchema).optional()
});