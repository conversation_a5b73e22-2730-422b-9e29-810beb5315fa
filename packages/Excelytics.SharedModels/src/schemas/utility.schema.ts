/**
 * Utility Zod schemas.
 * Used for common validation needs across microservices.
 * Updated to fix Circular Reference Errors & 'Type instantiation is excessively deep and possibly infinite'.
 * @module utility.schema
 */
import { z } from 'zod';
import mongoose from 'mongoose';

/**
 * A simple, "clean" schema for validating an ObjectId that has been converted to a string.
 * This is ideal for parsing the output of Mongoose `.lean()` queries.
 * It contains no ZodEffects, making it safe for type inference.
*/
export const ObjectIdStringSchema = z.string();


/**
 * A powerful schema for validating INPUT data.
 * It accepts either a valid ObjectId string (and transforms it to an ObjectId instance) or an existing ObjectId instance.
 * Use this for validating request bodies before saving to the database.
*/
export const ObjectIdInputSchema = z
	.union([
		z
		// First option: a string that represents a valid ObjectId
			.string()
			.refine(val => mongoose.Types.ObjectId.isValid(val), {
				message: 'Invalid ObjectId string',
			})
			.transform(val => new mongoose.Types.ObjectId(val)),

		// Second option: an ObjectId instance
		z.instanceof(mongoose.Types.ObjectId)
	]);