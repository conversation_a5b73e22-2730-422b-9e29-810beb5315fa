/**
 * This file defines the public API for the excelytics.shared-models package.
 * It re-exports functions and types from the internal scripts and helpers.
 *
 * The goal is to provide a clean, versioned interface for external consumers.
 *
 * Internal developers should import from the source files directly.
 * External consumers should import from this index file.
 *
 * Over time, internal imports can be migrated to this file as the API stabilizes.
 */

// Export File Structure Creator
export { createFileStructureMap } from './deprecated/file-structure-creator';
export { createSonnetFileStructureMap } from './deprecated/file-structure-creator-sonnet';

// Export Cleanup
export { cleanupImports } from './import-cleanup';

// Export Package Reinstall
export { reinstallDependencies } from './package-reinstall';

// Types
export type {CleanupImportOptions } from 'types/script.types';

// Helpers (for advanced usage)
export { ListConfigHelper } from '../helpers/list-config.helper';
export { FileStructureHelper } from '../helpers/file-structure.helper';
export { TreeGenerationHelper } from '../helpers/tree-generation.helper';
export { MetricsAnalysisHelper } from '../helpers/metrics-analysis.helper';
export { EnhancedMarkdownHelper } from '../helpers/enhanced-markdown.helper';
export {
	DeprecatedScriptHelper,
	MarkdownGenerationHelper,
	DiagnosticHelper
} from '../helpers/deprecated-script.helper';