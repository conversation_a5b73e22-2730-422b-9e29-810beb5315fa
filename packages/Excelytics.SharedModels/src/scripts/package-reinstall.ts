/**
 * This script is used to perform a clean re-installation of dependencies.
 * It removes the 'node_modules', 'package-lock.json', and 'bun.lock' files,
 * Then runs 'bun install' to fetch the latest versions of all dependencies.
 * @module package-reinstall
 */
import { spawn } from 'node:child_process';
import { rm } from 'node:fs/promises';
import { join } from 'node:path';

/**
 * A helper function to run a shell command and stream its output.
 * Returns a promise that resolves when the command completes successfully, or rejects if it fails.
 * @param command The command to execute (e.g., 'bun').
 * @param args An array of arguments for the command (e.g., ['install']).
 */
async function runCommand(command: string, args: string[]): Promise<void> {
	return new Promise((resolve, reject) => {
		// Spawn the child process
		const child = spawn(command, args, {
			// Use 'inherit' to stream the output directly to the parent's console
			// This makes it look like you're running 'bun install' directly.
			stdio: 'inherit',
			// Run in the shell to support commands like 'npm' on Windows
			shell: true,
		});

		// Listen for the close event
		child.on('close', (code) => {
			if (code === 0) {
				// Command was successful
				resolve();
			} else {
				// Command failed
				reject(new Error(`Command "${command} ${args.join(' ')}" failed with exit code ${code}`));
			}
		});

		// Listen for any errors during process spawning
		child.on('error', (err) => {
			reject(err);
		});
	});
}

/**
 * The main function to orchestrate the re-installation process.
 */
export async function reinstallDependencies() {
	console.log('🧹 Starting the clean reinstallation process...');

	const pathsToDelete = ['node_modules', 'package-lock.json', 'bun.lock'];

	for (const path of pathsToDelete) {
		const fullPath = join(process.cwd(), path);
		try {
			console.log(`  - Removing ${path}...`);

			// Use fs.rm for modern, recursive deletion.
			// { recursive: true } deletes folders.
			// { force: true } prevents errors if the path doesn't exist.
			await rm(fullPath, { recursive: true, force: true });
		} catch (error) {
			console.error(`  - Failed to remove ${path}:`, error);
		}
	}

	console.log('\n⏳ Waiting for 5 seconds to let things settle...');
	await new Promise((resolve) => setTimeout(resolve, 5000));

	console.log('\n📦 Installing fresh dependencies with Bun...');

	try {
		await runCommand('bun', ['install']);
		console.log('\n✅ Success! Your project is fresh and ready to go.');
	} catch (error) {
		console.error('\n❌ An error occurred during "bun install":', error);
		process.exit(1); // Exit with an error code
	}
}