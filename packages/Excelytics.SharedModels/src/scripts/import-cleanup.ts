/**
 * This script is used to clean up import statements in TypeScript files.
 * It removes the '.ts' extension from import paths and converts double quotes to single quotes.
 * @module import-cleanup
 */
import fs from 'fs';
import path from 'path';
import { CleanupImportOptions } from 'types';

// Match import/export statements with or without .ts extension & march single/double quotes
const importRegex = /from\s+(['"])([^'"]+?)(?:\.ts)?\1/g;

function traverseDirectory(dir: string) {
	const files = fs.readdirSync(dir);

	for (const file of files) {
		const filePath = path.join(dir, file);

		// Recursively traverse subdirectories
		if (fs.statSync(filePath).isDirectory()) {
			traverseDirectory(filePath);
		} else if (file.endsWith('.ts')) {
			updateFileImports(filePath);
		}
	}
}

function updateFileImports(filePath: string) {
	let content = fs.readFileSync(filePath, 'utf8');
	console.log(`Read file: ${filePath}`);

	// Log matches before quote swap, to see everything captured
	const matches = content.match(importRegex);
	if (matches) {
		console.log(`Matches in ${filePath} before quote swap:`, matches);
	}

	// Convert double quotes to single quotes
	const quoteSwappedContent = content.replace(/from\s+"([^"]+)"/g, "from '$1'");

	// Check for changes
	if (content !== quoteSwappedContent) {
		console.log(`Quote swapped in ${filePath}`);
		content = quoteSwappedContent;
	}

	// Remove '.ts' suffixes in import/export statements
	const updatedContent = content.replace(
		importRegex,
		(match, quote, path) => `from '${path}'` // Force single quotes
	);

	//Log matches after quote swap to see what changed.
	const matchesAfter = updatedContent.match(importRegex);
	if (matchesAfter) {
		console.log(`Matches in ${filePath} after quote swap:`, matchesAfter);
	}

	// Write updated content back to the file
	if (content !== updatedContent) {
		console.log(`Updating file: ${filePath}`);
		fs.writeFileSync(filePath, updatedContent, 'utf8');
	}
}

/**
 * Cleans up import statements in TypeScript files within specified directories.
 * It removes '.ts' extensions and normalizes quotes to single quotes.
 *
 * @param options Configuration for the cleanup operation.
 */
export async function cleanupImports(options: CleanupImportOptions = {}) {
	console.log("🧹 Starting import cleanup...");

	const defaultDirs = ["src", "tests", "types"];
	const targetDirectories = options.directories?.map((d) => path.resolve(process.cwd(), d))
        ?? defaultDirs.map((d) => path.resolve(process.cwd(), d));

	for (const dir of targetDirectories) {
		if (fs.existsSync(dir)) {
			console.log(`Traversing ${dir}...`);
			traverseDirectory(dir);
		} else {
			console.warn(`⚠️ Warning: Directory not found, skipping: ${dir}`);
		}
	}

	console.log("✅ Import cleanup complete!");
}