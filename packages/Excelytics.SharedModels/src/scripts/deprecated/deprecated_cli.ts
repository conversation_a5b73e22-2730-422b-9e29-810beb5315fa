import path from 'path';
import { cleanupImports } from '../import-cleanup';
import { reinstallDependencies } from '../package-reinstall';
import { createFileStructureMap } from './file-structure-creator';
import { createSonnetFileStructureMap } from './file-structure-creator-sonnet';

/**
 * CLI dispatcher. The first argument determines which script to run.
 */
async function main() {
	const [command, ...args] = process.argv.slice(2);

	switch (command) {
		case "map:sonnet": {
			const rootArg = args.find((arg) => !arg.startsWith("--"));
			if (!rootArg) {
				console.error("❌ Error: 'map' command requires a path.");
				console.log("Usage: bun script map <path> [--folders-only]");
				process.exit(1);
			}

			const rootPath = path.resolve(rootArg);
			const options = {
				foldersOnly: args.includes("--folders-only"),
				showAll: args.includes("--show-all"),
				showAllWithHideList: args.includes("--show-all-with-hide-list"),
			};

			await createSonnetFileStructureMap(rootPath, options);
			break;
		}
		//! Map the file structure
		case "map": {
			const rootArg = args.find((arg) => !arg.startsWith("--"));
			if (!rootArg) {
				console.error("❌ Error: 'map' command requires a path.");
				console.log("Usage: bun script map <path> [--folders-only]");
				process.exit(1);
			}

			const rootPath = path.resolve(rootArg);
			const options = {
				foldersOnly: args.includes("--folders-only"),
				showAll: args.includes("--show-all"),
				showAllWithHideList: args.includes("--show-all-with-hide-list"),
			};

			await createFileStructureMap(rootPath, options);
			break;
		}

		case "reinstall": {
			await reinstallDependencies();
			break;
		}



		case "clean:imports": {
			// For now, it runs with defaults. You could extend this to parse directory arguments from the command line.
			await cleanupImports();
			break;
		}

		default:
			console.log("❌ Error: Unknown or missing command.");
			console.log("\nAvailable commands:");
			console.log("  bun script map <path> [options]   - Create a file structure map");
			console.log("  bun script reinstall              - Clean and reinstall dependencies");
			console.log("  bun script clean:imports          - Clean up TS import statements");
			process.exit(1);
	}
}

if (import.meta.main) {
	main().catch((err) => {
		console.error("Script failed to execute:", err);
		process.exit(1);
	});
}