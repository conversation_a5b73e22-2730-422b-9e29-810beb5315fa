import { generateTree, getScriptFlags } from './file-structure-creator';
import fs from 'fs/promises';
import path from 'path';
import {
	MapStructureOptions,
	IGNORE_LIST
} from '../../types';
import {
	MetricsAnalysisHelper,
	EnhancedMarkdownHelper
} from '../../helpers/deprecated-script.helper';

/**
 * Creates a Markdown file representing the structure of a given directory.
 * This is the core library function, decoupled from the command line.
 *
 * @param rootPath The absolute path to the directory to map.
 * @param options Configuration options for generating the map.
 * @returns A promise that resolves when the file has been written.
 */
export async function createSonnetFileStructureMap(
	rootPath: string,
	options: MapStructureOptions = {}
) {

	// 2. Set up flags for the generateTree function based on options
	const flags = await getScriptFlags(options, rootPath);

	// Generate metrics using the helper (deprecated usage for backward compatibility)
	const cliOptions = {
		foldersOnly: flags.foldersOnly,
		showAll: flags.showAll,
		showAllWithHideList: flags.option === 'showAllWithHideList'
	};
	const metrics = await MetricsAnalysisHelper.generateProjectMetrics(rootPath, cliOptions, IGNORE_LIST, new Set([
		'.ts', '.js', '.tsx', '.jsx', '.cs', '.sql', '.vue', '.html',
		'.css', '.scss', '.less', '.json', '.yaml', '.yml', '.md', '.env',
		'.env.development', 'json'
	]));

	// Generate tree structure
	console.log("🌳 Generating directory tree...");
	const treeStructure = await generateTree(rootPath, flags);
	const rootFolderName = path.basename(rootPath);

	// Get additional info using helpers
	const gitInfo = await MetricsAnalysisHelper.getGitInfo(rootPath);
	const packageInfo = await MetricsAnalysisHelper.analyzePackageJson(rootPath);

	// Create enhanced markdown content using helper
	const markdownContent = EnhancedMarkdownHelper.generateEnhancedMarkdownContent(
		rootFolderName,
		rootPath,
		treeStructure,
		metrics,
		gitInfo,
		packageInfo,
		Date.now()
	);

	// Generate output filename
	const outputFileName = EnhancedMarkdownHelper.generateOutputFileName(rootFolderName, 'deep');

	// Write file
	try {
		await fs.writeFile(outputFileName, markdownContent);
		console.log(`✅ Enhanced project analysis saved to: ${outputFileName}`);
		return outputFileName;
	} catch (error) {
		console.error("❌ Error writing output file:", error);
		process.exit(1);
	}
}