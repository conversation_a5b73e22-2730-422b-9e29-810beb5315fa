import { statSync } from 'fs';
import fs from 'fs/promises';
import path from 'path';
import {
	MapStructureOptions,
	HIDE_CONTENTS_LIST,
	ScriptFlagOption,
	ScriptFlags,
	IGNORE_LIST
} from '../../types';

/**
 * Recursively traverses a directory and builds a string representation of its structure.
 * @param directoryPath The absolute path to the directory to traverse.
 * @param flags The command-line flags that control the output.
 * @param prefix The string prefix to use for the current level of the tree.
 * @returns A promise that resolves to the string representation of the directory tree.
 */
export async function generateTree(
	directoryPath: string,
	flags: ScriptFlags,
	prefix: string = '',
): Promise<string> {
	let tree = '';
	try {
		const files: string[] = await fs.readdir(directoryPath);

		// If --show-all is passed, the ignore list is skipped.
		let filteredFiles: string[] = [];

		switch (flags.option) {
			case ScriptFlagOption.FoldersOnly:
				filteredFiles = files.filter((file) => {
					const filePath = path.join(directoryPath, file);
					const stats = statSync(filePath);
					return stats.isDirectory() && !IGNORE_LIST.has(file);
				});
				break;
			case ScriptFlagOption.ShowAllWithHideList:
				filteredFiles = files.filter((file) =>
					!HIDE_CONTENTS_LIST.has(file) &&
					!IGNORE_LIST.has(file)
				);
				break;
			case ScriptFlagOption.ShowAll:
				filteredFiles = files;
				break;
			default:
				filteredFiles = files.filter((file) => !IGNORE_LIST.has(file));
		}

		// Debugging purposes:
		// console.log(filteredFiles);

		for (let i = 0; i < filteredFiles.length; i++) {
			const file = filteredFiles[i] as string;
			const isLast = i === filteredFiles.length - 1;
			const filePath = path.join(directoryPath, file);
			const stats = await fs.stat(filePath);

			const connector = isLast ? "└── " : "├── ";
			const newPrefix = prefix + (isLast ? "    " : "│   ");

			if (stats.isDirectory()) {
				tree += `${prefix}${connector}${file}/\n`;
				// Recurse if --show-all is on, OR if the folder is not in the hide list.
				if (flags.showAll || !HIDE_CONTENTS_LIST.has(file)) {
					tree += await generateTree(filePath, flags, newPrefix);
				}
			} else if (!flags.foldersOnly) {
				// Only add files to the tree if the --folders-only flag is NOT set.
				tree += `${prefix}${connector}${file}\n`;
			}
		}
	} catch (error) {
		console.error(`Error reading directory ${directoryPath}:`, error);
	}
	return tree;
}


/**
 * Creates a Markdown file representing the structure of a given directory.
 * This is the core library function, decoupled from the command line.
 *
 * @param rootPath The absolute path to the directory to map.
 * @param options Configuration options for generating the map.
 * @returns A promise that resolves when the file has been written.
 */
export async function createFileStructureMap(
	rootPath: string,
	options: MapStructureOptions = {}
) {
	const flags = await getScriptFlags(options, rootPath);

	// 3. Generate the tree structure string
	const treeStructure = await generateTree(rootPath, flags);
	const rootFolderName = path.basename(rootPath);
	const finalTree = `${rootFolderName}/\n${treeStructure}`;

	// 4. Create the file name and content
	const date = new Date();

	// Format date for South African timezone (dd-mm-yyyy)
	const formatSouthAfricanDate = (date: Date): string => {
		const options: Intl.DateTimeFormatOptions = {
			timeZone: 'Africa/Johannesburg',
			day: '2-digit',
			month: '2-digit',
			year: 'numeric'
		};
		const formatter = new Intl.DateTimeFormat('en-ZA', options);
		const parts = formatter.formatToParts(date);
		const day = parts.find(part => part.type === 'day')?.value;
		const month = parts.find(part => part.type === 'month')?.value;
		const year = parts.find(part => part.type === 'year')?.value;
		return `${day}-${month}-${year}`;
	};

	// Format date and time for South African timezone
	const formatSouthAfricanDateTime = (date: Date): string => {
		const options: Intl.DateTimeFormatOptions = {
			timeZone: 'Africa/Johannesburg',
			weekday: 'short',
			day: '2-digit',
			month: '2-digit',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: true
		};
		const formatter = new Intl.DateTimeFormat('en-ZA', options);
		const parts = formatter.formatToParts(date);
		const weekday = parts.find(part => part.type === 'weekday')?.value;
		const day = parts.find(part => part.type === 'day')?.value;
		const month = parts.find(part => part.type === 'month')?.value;
		const year = parts.find(part => part.type === 'year')?.value;
		const hour = parts.find(part => part.type === 'hour')?.value;
		const minute = parts.find(part => part.type === 'minute')?.value;
		const dayPeriod = parts.find(part => part.type === 'dayPeriod')?.value?.toLowerCase();
		return `(${weekday}) ${day}-${month}-${year} ${hour}:${minute}${dayPeriod} SAST`;
	};

	const dateString = formatSouthAfricanDate(date);
	const outputFileName = `${dateString}_${rootFolderName}_structure.md`;

	// 5. Create the Markdown content
	const markdownContent = `# Project Structure: ${rootFolderName}
*Generated on: ${formatSouthAfricanDateTime(date)}*

Here is the file and directory structure of the project:
\`\`\`
${finalTree}
\`\`\`
`;

	// 5. Write the content to the file
	try {
		await fs.writeFile(outputFileName, markdownContent);
		console.log(`✅ Success! Project structure map saved to: ${outputFileName}`);
	} catch (error) {
		console.error("❌ Error writing the output file:", error);
		process.exit(1);
	}
}

export async function getScriptFlags(options: MapStructureOptions, rootPath: string): Promise<ScriptFlags> {
	console.log("Starting to map project structure...");

	// 1. Validate the root path
	try {
		const stats = await fs.stat(rootPath);
		if (!stats.isDirectory()) {
			console.error(`❌ Error: The path "${rootPath}" is not a directory.`);
			process.exit(1);
		}
	} catch (error) {
		console.error(`❌ Error: The path "${rootPath}" does not exist.`);
		process.exit(1);
	}

	// 2. Set up flags for the generateTree function based on options
	const flags = {
		foldersOnly: !!options.foldersOnly,
		showAll: !!options.showAll,
		option: ScriptFlagOption.Default,
	};

	if (options.foldersOnly) {
		flags.option = ScriptFlagOption.FoldersOnly;
	} else if (options.showAll) {
		flags.option = ScriptFlagOption.ShowAll;
	} else if (options.showAllWithHideList) {
		flags.option = ScriptFlagOption.ShowAllWithHideList;
	}

	return flags;
}