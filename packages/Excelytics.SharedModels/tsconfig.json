{
    "compilerOptions": {
        // --- Project References Configuration ---
        "sourceMap": true,          // Generate .js.map files for debugging.
        "declaration": true,        // Generate .d.ts files for other projects to consume.
        "declarationMap": true,     // Improves "Go to Definition" across projects.

        // --- Output Configuration ---
        "outDir": "./dist",         // All output goes to the 'dist' folder.
        "rootDir": "./src",         // The root of the source files. Preserves folder structure in 'dist'.

        // --- Crucial for internal imports ---
        "baseUrl": "./src", // Base directory for resolving non-relative module names.

        // --- Module Configuration ---
        "target": "ESNext",
        "module": "ESNext",
        "lib": ["ESNext", "DOM"],
        "moduleResolution": "bundler",

        // --- Code Quality & Strictness ---
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": false, // Set to false. Allows unused Express params
        "forceConsistentCasingInFileNames": true,

        // --- Module Interop & Types ---
        "skipLibCheck": true,           // Skip type checking of all .d.ts files (speeds up build)
        "esModuleInterop": true,
        "resolveJsonModule": true,
        "types": ["bun-types", "node"], // For Bun, Node.js and express global types

        // --- Necessary for @typegoose/typegoose ---
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
    },
    "include": ["src/**/*"],
    "exclude": ["node_modules", "dist"]
}