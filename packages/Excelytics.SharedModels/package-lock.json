{"name": "excelytics.sharedmodels", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "excelytics.sharedmodels", "dependencies": {"@typegoose/typegoose": "^12.10.1", "mongoose": "~8.15.1"}, "devDependencies": {"@types/bun": "latest", "@types/mongoose": "^5.11.97", "bun": "^1.2.15", "bun-types": "^1.2.15"}, "peerDependencies": {"typescript": "^5"}}, "node_modules/@mongodb-js/saslprep": {"version": "1.2.2", "license": "MIT", "dependencies": {"sparse-bitfield": "^3.0.3"}}, "node_modules/@oven/bun-darwin-aarch64": {"version": "1.2.15", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@typegoose/typegoose": {"version": "12.16.0", "license": "MIT", "dependencies": {"lodash": "^4.17.20", "loglevel": "^1.9.2", "reflect-metadata": "^0.2.2", "semver": "^7.7.2", "tslib": "^2.8.1"}, "engines": {"node": ">=16.20.1"}, "peerDependencies": {"mongoose": "~8.15.0"}}, "node_modules/@types/bun": {"version": "1.2.15", "resolved": "https://registry.npmjs.org/@types/bun/-/bun-1.2.15.tgz", "integrity": "sha512-U1ljPdBEphF0nw1MIk0hI7kPg7dFdPyM7EenHsp6W5loNHl7zqy6JQf/RKCgnUn2KDzUpkBwHPnEJEjII594bA==", "dev": true, "license": "MIT", "dependencies": {"bun-types": "1.2.15"}}, "node_modules/@types/mongoose": {"version": "5.11.97", "dev": true, "license": "MIT", "dependencies": {"mongoose": "*"}}, "node_modules/@types/node": {"version": "22.15.30", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/webidl-conversions": {"version": "7.0.3", "license": "MIT"}, "node_modules/@types/whatwg-url": {"version": "11.0.5", "license": "MIT", "dependencies": {"@types/webidl-conversions": "*"}}, "node_modules/bson": {"version": "6.10.4", "license": "Apache-2.0", "engines": {"node": ">=16.20.1"}}, "node_modules/bun": {"version": "1.2.15", "cpu": ["arm64", "x64", "aarch64"], "dev": true, "hasInstallScript": true, "license": "MIT", "os": ["darwin", "linux", "win32"], "bin": {"bun": "bin/bun.exe", "bunx": "bin/bun.exe"}, "optionalDependencies": {"@oven/bun-darwin-aarch64": "1.2.15", "@oven/bun-darwin-x64": "1.2.15", "@oven/bun-darwin-x64-baseline": "1.2.15", "@oven/bun-linux-aarch64": "1.2.15", "@oven/bun-linux-aarch64-musl": "1.2.15", "@oven/bun-linux-x64": "1.2.15", "@oven/bun-linux-x64-baseline": "1.2.15", "@oven/bun-linux-x64-musl": "1.2.15", "@oven/bun-linux-x64-musl-baseline": "1.2.15", "@oven/bun-windows-x64": "1.2.15", "@oven/bun-windows-x64-baseline": "1.2.15"}}, "node_modules/bun-types": {"version": "1.2.15", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/kareem": {"version": "2.6.3", "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/loglevel": {"version": "1.9.2", "license": "MIT", "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "node_modules/memory-pager": {"version": "1.5.0", "license": "MIT"}, "node_modules/mongodb": {"version": "6.16.0", "license": "Apache-2.0", "dependencies": {"@mongodb-js/saslprep": "^1.1.9", "bson": "^6.10.3", "mongodb-connection-string-url": "^3.0.0"}, "engines": {"node": ">=16.20.1"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.1.0 || ^2.0.0", "gcp-metadata": "^5.2.0", "kerberos": "^2.0.1", "mongodb-client-encryption": ">=6.0.0 <7", "snappy": "^7.2.2", "socks": "^2.7.1"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "gcp-metadata": {"optional": true}, "kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "snappy": {"optional": true}, "socks": {"optional": true}}}, "node_modules/mongodb-connection-string-url": {"version": "3.0.2", "license": "Apache-2.0", "dependencies": {"@types/whatwg-url": "^11.0.2", "whatwg-url": "^14.1.0 || ^13.0.0"}}, "node_modules/mongoose": {"version": "8.15.1", "license": "MIT", "dependencies": {"bson": "^6.10.3", "kareem": "2.6.3", "mongodb": "~6.16.0", "mpath": "0.9.0", "mquery": "5.0.0", "ms": "2.1.3", "sift": "17.1.3"}, "engines": {"node": ">=16.20.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}, "node_modules/mpath": {"version": "0.9.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/mquery": {"version": "5.0.0", "license": "MIT", "dependencies": {"debug": "4.x"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/reflect-metadata": {"version": "0.2.2", "license": "Apache-2.0"}, "node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/sift": {"version": "17.1.3", "license": "MIT"}, "node_modules/sparse-bitfield": {"version": "3.0.3", "license": "MIT", "dependencies": {"memory-pager": "^1.0.2"}}, "node_modules/tr46": {"version": "5.1.1", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/typescript": {"version": "5.8.3", "license": "Apache-2.0", "peer": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.21.0", "dev": true, "license": "MIT"}, "node_modules/webidl-conversions": {"version": "7.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/whatwg-url": {"version": "14.2.0", "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}}}