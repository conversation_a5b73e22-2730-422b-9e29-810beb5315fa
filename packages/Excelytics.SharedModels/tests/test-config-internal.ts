/**
 * Internal test script to verify the configuration system works within SharedModels
 */
import { ConfigManager } from '../src/config';
import { createFileStructureMap } from '../src';

async function testConfigurationSystem() {
	console.log('🧪 Testing Configuration System Internally\n');
    
	try {
		// Test 1: ConfigManager functionality
		console.log('1️⃣  Testing ConfigManager...');
		const configManager = ConfigManager.getInstance();
		const config = await configManager.getConfig();
        
		console.log('   ✅ Configuration loaded successfully');
		console.log('   📋 Final ignore list:', config.ignoresList);
		console.log('   📋 Final hide list:', config.hideContentsList);
        
		// Test 2: Generate structure with new config
		console.log('\n2️⃣  Testing createFileStructureMap...');
		await createFileStructureMap('.', {
			showAllWithHideList: true
		});
		console.log('   ✅ Structure generated with custom config');
        
		// Test 3: Clear cache and reload
		console.log('\n3️⃣  Testing cache clearing...');
		configManager.clearCache();
		const reloadedConfig = await configManager.getConfig();
		console.log('   ✅ Configuration reloaded after cache clear');
        
		console.log('\n🎉 All internal tests passed!');
        
	} catch (error) {
		console.error('❌ Internal test failed:', error);
		process.exit(1);
	}
}

// Run the test
testConfigurationSystem().catch(console.error);