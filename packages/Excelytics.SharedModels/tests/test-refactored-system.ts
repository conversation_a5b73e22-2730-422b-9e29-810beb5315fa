/**
 * Comprehensive test of the refactored configuration system
 */
import { ConfigManager } from '../src/config';
import { createFileStructureMap } from '../src';
import { resolveListConfigToSet } from '../src/helpers/deprecated-script.helper';

async function testRefactoredSystem() {
	console.log('🧪 Testing Refactored Configuration System\n');
    
	try {
		// Test 1: Configuration Loading
		console.log('1️⃣  Testing Configuration Loading...');
		const configManager = ConfigManager.getInstance();
		const config = await configManager.getConfig();
        
		console.log('   ✅ Configuration loaded successfully');
		console.log('   📋 Source:', config);
        
		// Test 2: List Resolution
		console.log('\n2️⃣  Testing List Resolution...');
		const resolvedIgnores = resolveListConfigToSet(config.ignoresList);
		const resolvedHides = resolveListConfigToSet(config.hideContentsList);
        
		console.log('   ✅ Lists resolved to Sets');
		console.log('   🚫 Resolved ignores:', Array.from(resolvedIgnores));
		console.log('   👁️  Resolved hides:', Array.from(resolvedHides));
        
		// Test 3: Verify our custom config is being used
		console.log('\n3️⃣  Testing Custom Configuration...');
		const hasCustomIgnores = resolvedIgnores.has('temp_test.ts') && resolvedIgnores.has('tsconfig.tsbuildinfo');
		const hasCustomHides = resolvedHides.has('tsconfig.json');
		const hasDefaults = resolvedIgnores.has('node_modules') && resolvedIgnores.has('.git');
        
		console.log('   ✅ Custom ignores applied:', hasCustomIgnores);
		console.log('   ✅ Custom hides applied:', hasCustomHides);
		console.log('   ✅ Default values preserved:', hasDefaults);
        
		// Test 4: Generate structure with new system
		console.log('\n4️⃣  Testing Structure Generation...');
		await createFileStructureMap('.', {
			showAllWithHideList: true
		});
		console.log('   ✅ Structure generated with ConfigManager');
        
		// Test 5: Cache functionality
		console.log('\n5️⃣  Testing Cache Functionality...');
		const config2 = await configManager.getConfig(); // Should use cache
		console.log('   ✅ Second config call (cached)');
        
		configManager.clearCache();
		const config3 = await configManager.getConfig(); // Should reload
		console.log('   ✅ Config reloaded after cache clear');
        
		console.log('\n🎉 All refactored system tests passed!');
		console.log('\n📊 Summary:');
		console.log('   • Configuration discovery: ✅');
		console.log('   • Default + custom merging: ✅');
		console.log('   • List resolution: ✅');
		console.log('   • Structure generation: ✅');
		console.log('   • Cache management: ✅');
        
	} catch (error) {
		console.error('❌ Refactored system test failed:', error);
		process.exit(1);
	}
}

// Run the comprehensive test
testRefactoredSystem().catch(console.error);