import { createFileStructureMap } from '../src';

async function runTest() {
	// Determine the root path you want to map.
	// For local testing, you can use __dirname or process.cwd()
	// Let's map the 'packages' directory for a good test.
	const rootPathToMap = '../'; // Relative to where you run the script

	// Define some CLI options, similar to how your main application would pass them
	const cliOptions = {
		showAllWithHideList: true,
		foldersOnly: false,
		showAll: false
	};

	console.log(`\n--- Starting File Structure Map Generation for: ${rootPathToMap} ---`);
	await createFileStructureMap(rootPathToMap, cliOptions);
	console.log('--- Map Generation Complete ---');
}

runTest().catch(console.error);