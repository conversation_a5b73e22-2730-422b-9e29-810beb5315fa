/**
 * Test the new CLI architecture where config is loaded once at CLI level
 */
import { ConfigManager } from '../src/config';
import {
	DeprecatedScriptHelper,
	MarkdownGenerationHelper,
	Diagnostic<PERSON>elper,
	FileStructureHelper
} from '../src';

async function testCLIArchitecture() {
	console.log('🧪 Testing New CLI Architecture\n');
    
	try {
		// Test 1: Load config once (like CLI does)
		console.log('1️⃣  Loading configuration once...');
		const configManager = ConfigManager.getInstance();
		const config = await configManager.getConfig();
		console.log('   ✅ Configuration loaded at CLI level');
        
		// Test 2: Resolve lists using ScriptHelper
		console.log('\n2️⃣  Testing ScriptHelper.resolveListConfigToSet...');
		const resolvedIgnores = DeprecatedScriptHelper.resolveListConfigToSet(config.ignoresList);
		const resolvedHides = DeprecatedScriptHelper.resolveListConfigToSet(config.hideContentsList);
		console.log('   ✅ Lists resolved using ScriptHelper');
		console.log('   🚫 Ignores:', Array.from(resolvedIgnores).slice(0, 5), '...');
		console.log('   👁️  Hides:', Array.from(resolvedHides).slice(0, 3), '...');
        
		// Test 3: Process CLI options using ScriptHelper
		console.log('\n3️⃣  Testing ScriptHelper.processUserFlagOptions...');
		const cliOptions = {
			showAllWithHideList: true,
			foldersOnly: false,
			showAll: false
		};
		const flags = DeprecatedScriptHelper.processUserFlagOptions(cliOptions);
		console.log('   ✅ CLI options processed into ScriptFlags');
		console.log('   🏁 Flags:', flags);
        
		// Test 4: Use DiagnosticHelper
		console.log('\n4️⃣  Testing DiagnosticHelper...');
		DiagnosticHelper.logResolvedConfiguration(resolvedIgnores, resolvedHides);
		console.log('   ✅ Diagnostic logging works');
        
		// Test 5: Test MarkdownGenerationHelper (small directory)
		console.log('\n5️⃣  Testing MarkdownGenerationHelper...');
		const testTree = await MarkdownGenerationHelper.generateMarkdownTree(
			'./src/config',
			flags,
			resolvedIgnores,
			resolvedHides
		);
		console.log('   ✅ Markdown tree generated');
		console.log('   📄 Sample output:');
		console.log(testTree.split('\n').slice(0, 5).map(line => `      ${line}`).join('\n'));

		// Test 6: Test FileStructureHelper
		console.log('\n6️⃣  Testing FileStructureHelper...');
		console.log('   📁 Testing simple structure generation...');
		await FileStructureHelper.generateSimpleFileStructure('./src/config', cliOptions, config);
		console.log('   ✅ Simple structure generated');

		console.log('   📊 Testing enhanced structure generation...');
		await FileStructureHelper.generateEnhancedFileStructure('./src/config', cliOptions, config);
		console.log('   ✅ Enhanced structure generated');
        
		console.log('\n🎉 New CLI Architecture Test Passed!');
		console.log('\n📊 Architecture Benefits:');
		console.log('   • ✅ Config loaded once at CLI level');
		console.log('   • ✅ Functions accept pre-resolved config');
		console.log('   • ✅ Better separation of concerns');
		console.log('   • ✅ More testable individual functions');
		console.log('   • ✅ Improved performance (no repeated config loading)');
		console.log('   • ✅ Unified markdown generation helpers');
		console.log('   • ✅ Modular file structure generation');
        
	} catch (error) {
		console.error('❌ CLI Architecture test failed:', error);
		process.exit(1);
	}
}

// Run the test
testCLIArchitecture().catch(console.error);