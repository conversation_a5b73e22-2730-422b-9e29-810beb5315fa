{
    "compilerOptions": {
        // --- Project References Configuration ---
        "declaration": true, // Generate .d.ts files for other projects to consume.
        "declarationMap": true, // Improves "Go to Definition" across projects.
        "sourceMap": true, // Generate .js.map files for debugging.

        // --- Output Configuration ---
        "outDir": "./dist", // All output goes to the 'dist' folder.
        "rootDir": "./src", // The root of the source files. Preserves folder structure in 'dist'.

        // --- Module Configuration ---
        "target": "ESNext",
        "lib": ["ESNext", "DOM"],
        "module": "ESNext",
        "moduleResolution": "bundler", // Best for modern tools like Bun/Vite.

        // --- Code Quality & Strictness ---
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "noUnusedLocals": true,
        "noUnusedParameters": false, // Set to false, to allow unused Express params

        // --- Module Interop & Types ---
        "esModuleInterop": true,
        "skipLibCheck": true,
        "types": ["bun-types", "node", "express"],
        "resolveJsonModule": true,

        // --- Path Aliasing (For cleaner internal imports) ---
        "baseUrl": "./src"
    },
    "include": ["src"],
    "exclude": ["node_modules", "dist"]
}