import { defineConfig } from 'tsup';

export default defineConfig({
	entry: ['src/index.ts'],
	format: ['esm'],
	// THE FIX #1: Target a specific, modern Node.js version.
	target: 'node18',
	// THE FIX #2: Turn OFF tsup's internal DTS generation.
	// We will handle this with a separate, more reliable script.
	dts: false,
	// THE FIX #3: Create a single bundle file for simplicity.
	splitting: false,
	bundle: true,
	clean: true,
	sourcemap: true
});