# MongoDB URIs
MONGODB_URI_UNRAID=mongodb://
MONGODB_URI_LOCAL=mongodb://localhost:27017/introspection
MONGODB_URI_STAGNG=NONE
MONGODB_URI_PROD=NONE

# --- MongoDB Credentials (safer) ---
MONGO_USER=admin
MONGO_PASS=none
MONGO_HOST=************
MONGO_PORT=27017
MONGO_DB_NAME=introspection
MONGO_AUTH_SOURCE=admin

# --- Service Identification ---
SERVICE_NAME=none
VERSION=1.1.1
API_VERSION=v1

# --- Environment & Network ---
TAILSCALE_IP=************
ENV=development
PORT=0
VPN=true


# --- Redis Configuration ---
REDIS_URI_UNRAID=************
REDIS_PASSWORD=undefined
REDIS_HOST=************
REDIS_PORT=6379

# --- Microservices URLs (Revised Ports) ---
IDP_SERVICE_URL=http://localhost:6002
CALC_SERVICE_URL=http://localhost:6003
CLIENT_SERVICE_URL=http://localhost:4200
FINANCE_SERVICE_URL=http://localhost:6001


# Log Levels: (TRACE, DEBUG, INFO, WARN, ERROR, SILENT)
LOG_LEVEL=DEBUG