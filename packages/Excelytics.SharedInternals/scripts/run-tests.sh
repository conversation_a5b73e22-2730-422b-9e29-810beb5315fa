#!/bin/bash

# Test runner script for Excelytics.SharedInternals
# Can be used locally or in CI/CD pipelines

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
TEST_ENV=${ENV:-test}
NODE_ENV=${NODE_ENV:-test}
VERBOSE=${VERBOSE:-false}
COVERAGE=${COVERAGE:-false}

echo -e "${BLUE}🧪 Excelytics.SharedInternals Test Runner${NC}"
echo "=================================="

# Set up environment
echo -e "${YELLOW}📋 Setting up test environment...${NC}"
export ENV=$TEST_ENV
export NODE_ENV=$NODE_ENV
export SERVICE_NAME=${SERVICE_NAME:-test-service}
export PORT=${PORT:-3000}
export MONGO_USER=${MONGO_USER:-test-user}
export MONGO_PASS=${MONGO_PASS:-test-pass}
export MONGO_HOST=${MONGO_HOST:-localhost}
export MONGO_DB_NAME=${MONGO_DB_NAME:-test-db}
export REDIS_HOST=${REDIS_HOST:-localhost}
export DEBUG_ERRORS=${DEBUG_ERRORS:-false}
export LOG_SENSITIVE=${LOG_SENSITIVE:-false}

echo "✅ Environment configured:"
echo "  - ENV: $ENV"
echo "  - NODE_ENV: $NODE_ENV"
echo "  - DEBUG_ERRORS: $DEBUG_ERRORS"
echo "  - LOG_SENSITIVE: $LOG_SENSITIVE"
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Please run this script from the package root.${NC}"
    exit 1
fi

# Check if test directory exists
if [ ! -d "src/__tests__" ]; then
    echo -e "${YELLOW}⚠️ Warning: No test directory found (src/__tests__)${NC}"
    exit 0
fi

# Count test files
TEST_FILE_COUNT=$(find src/__tests__ -name "*.test.ts" 2>/dev/null | wc -l || echo "0")
echo -e "${BLUE}📊 Found $TEST_FILE_COUNT test files${NC}"

if [ "$TEST_FILE_COUNT" -eq 0 ]; then
    echo -e "${YELLOW}⚠️ No test files found${NC}"
    exit 0
fi

# List test files
echo -e "${BLUE}📁 Test files:${NC}"
find src/__tests__ -name "*.test.ts" | sed 's/^/  - /'
echo ""

# Build if needed
if [ ! -d "dist" ] || [ "src" -nt "dist" ]; then
    echo -e "${YELLOW}🔨 Building package...${NC}"
    bun run build
    echo "✅ Build completed"
    echo ""
fi

# Run tests
echo -e "${BLUE}🧪 Running tests...${NC}"
echo "=================================="

if [ "$COVERAGE" = "true" ]; then
    echo -e "${YELLOW}📊 Running tests with coverage...${NC}"
    bun test --coverage src/__tests__
else
    echo -e "${YELLOW}📝 Running tests...${NC}"
    bun test src/__tests__
fi

TEST_EXIT_CODE=$?

echo ""
echo "=================================="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
else
    echo -e "${RED}❌ Tests failed with exit code $TEST_EXIT_CODE${NC}"
fi

exit $TEST_EXIT_CODE
