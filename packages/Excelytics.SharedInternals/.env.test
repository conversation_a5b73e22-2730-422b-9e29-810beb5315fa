# Test Environment Configuration
# This file is used during CI/CD testing and local test runs

# Environment Settings
ENV=test
NODE_ENV=test

# Service Configuration
SERVICE_NAME=test-service
PORT=3000

# Database Configuration (Mock/Test values)
MONGO_USER=test-user
MONGO_PASS=test-pass
MONGO_HOST=localhost
MONGO_DB_NAME=test-db

# Redis Configuration (Mock/Test values)
REDIS_HOST=localhost
REDIS_PORT=6379

# Logging Configuration
DEBUG_ERRORS=false
LOG_SENSITIVE=false

# Test-specific settings
TEST_TIMEOUT=30000
TEST_VERBOSE=false
