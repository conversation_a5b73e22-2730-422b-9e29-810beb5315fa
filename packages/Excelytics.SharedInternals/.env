#--------------------------------------------------------------------------
# Excelytics.SharedInternals - Environment Configuration
#--------------------------------------------------------------------------

# --- Core Environment ---
ENV=development
NODE_ENV=development

# --- Error Handling & Logging Configuration ---
# Enable verbose error logging (overrides environment-based defaults)
DEBUG_ERRORS=false

# Enable logging of sensitive information (development only)
# WARNING: Only enable in development - never in production
LOG_SENSITIVE=false

# --- Logging Configuration ---
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, SILENT
LOG_LEVEL=DEBUG

# --- Test Environment Overrides ---
# These are used during testing to control behavior
TEST_DISABLE_LOGGING=false
TEST_MOCK_CONSOLE=false