/**
 * Zod schemas for token validation and generation.
 * Ensures consistent token structure across all microservices.
 * @module token.schema
 */
import { z } from 'zod';
import { PermissionUtils } from '../utils';
import { EnumPermissions, EnumClientOrigin, EnumClientPath, EnumTokenType } from '../enums';

/**
 * Base schema for token payloads.
 * Contains common validation rules for all token types.
 */
export const BaseTokenPayloadSchema = z.object({
	userId: z
		.string().min(1, 'User ID cannot be empty'),
	email: z
		.string().email('Invalid email format'),
	clientId: z
		.string().min(1, 'Client ID cannot be empty'),
	clientOrigin: z
		.nativeEnum(EnumClientOrigin, {
			errorMap: () => ({ message: 'Invalid client origin' })
		}),
	clientPath: z
		.nativeEnum(EnumClientPath, {
			errorMap: () => ({ message: 'Invalid client path' })
		}),
	isActive: z.boolean().optional()
});

/**
 * Schema for validating access token payloads.
 * Extends base schema with permissions array and timestamp validation.
 */
export const AccessTokenPayloadSchema = BaseTokenPayloadSchema.extend({
	tokenType: z.literal(EnumTokenType.ACCESS),

	permissions: z
		.array(
			z.string()
				.min(1, 'Permission cannot be empty')
				.refine(
					(permission) => {
						// Validate that the permission exists in our enum
						return Object.values(EnumPermissions).includes(permission as EnumPermissions);
					},
					{ message: 'Invalid permission. Must be a valid system permission.' }
				)
		)
		.optional() // Matches the optional permissions in AccessTokenPayload
		.refine(
			(permissions) => {
				// Skip validation if permissions array is undefined
				if (!permissions) return true;

				// Check for duplicate permissions
				return new Set(permissions).size === permissions.length;
			},
			{ message: 'Duplicate permissions are not allowed' }
		)
		.refine(
			(permissions) => {
				// Skip validation if permissions array is undefined
				if (!permissions || permissions.length === 0) return true;

				// Ensure user has at least basic API access if permissions are provided
				return PermissionUtils.hasAnyPermission(permissions, [
					EnumPermissions.ACCESS_API,
					EnumPermissions.ADMIN_SYSTEM
				]);
			},
			{ message: 'Token must include basic API access permission when permissions are specified' }
		),

	// Standard 'iat' claim is a NumericDate (seconds since epoch)
	issuedAt: z
		.number({
			required_error: 'issuedAt (iat) is required in access token payload',
			invalid_type_error: 'issuedAt (iat) must be a number',
		})
		.int()
		.positive()
		.transform((val) => new Date(val * 1000)), // Convert seconds to Date object

	expiresAt: z
		.number()
		.int()
		.positive()
		.transform((val) => new Date(val * 1000))
		.optional(), // Convert seconds to Date object
});

/**
 * Schema for validating refresh token payloads.
 * Extends base schema with timestamp validation.
 */
export const RefreshTokenPayloadSchema = BaseTokenPayloadSchema.extend({
	tokenType: z.literal(EnumTokenType.REFRESH),

	// Standard 'iat' claim is a NumericDate (seconds since epoch)
	issuedAt: z
		.number({
			required_error: 'issuedAt (iat) is required in refresh token payload',
			invalid_type_error: 'issuedAt (iat) must be a number',
		})
		.int()
		.positive()
		.transform((val) => new Date(val * 1000)), // Convert seconds to Date object

	expiresAt: z
		.number()
		.int()
		.positive()
		.transform((val) => new Date(val * 1000))
		.optional() // Convert seconds to Date object
});

/**
 * Union schema for any token payload.
 * Validates against either access or refresh token schemas.
 */
export const TokenPayloadSchema = z.union([
	AccessTokenPayloadSchema,
	RefreshTokenPayloadSchema
]);

/**
 * Schema for token generation input.
 * Used when creating new tokens.
 */
export const TokenGenerationInputSchema = BaseTokenPayloadSchema.extend({
	permissions: z
		.array(
			z.string()
				.min(1, 'Permission cannot be empty')
				.refine(
					(permission) => {
						return Object.values(EnumPermissions).includes(permission as EnumPermissions);
					},
					{ message: 'Invalid permission. Must be a valid system permission.' }
				)
		)
		.optional()
});