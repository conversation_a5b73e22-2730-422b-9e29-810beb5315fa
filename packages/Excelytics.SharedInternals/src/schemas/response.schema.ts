/**
 * Zod schemas for API response validation.
 * Provides runtime validation for response structures across microservices.
 * @module response.schema
 */
import { z } from 'zod';

/**
 * Schema for API error object structure.
 * Used within error responses to provide detailed error information.
 */
export const ApiErrorObjectSchema = z.object({
	code: z.string(),
	message: z.string(),
	details: z.any().optional()
});

/**
 * Base API response schema that all responses follow.
 * Provides consistent structure for client-side handling.
 */
export const ApiResponseSchema = z.object({
	success: z.boolean(),
	message: z.string(),
	data: z.any().optional(),
	error: ApiErrorObjectSchema.optional(),
	pagination: z.any().optional() // PaginationMetadata schema can be added later
});

/**
 * Success response schema with generic data type.
 * Used for all successful API operations.
 */
export const SuccessResponseSchema = ApiResponseSchema.extend({
	success: z.literal(true),
	data: z.any()
});

/**
 * Error response schema.
 * Provides standardized error information for failed operations.
 */
export const ErrorResponseSchema = ApiResponseSchema.extend({
	success: z.literal(false),
	error: ApiErrorObjectSchema
});

/**
 * Authentication token response schema.
 * Contains tokens and payload information for authenticated sessions.
 */
export const TokenResponseSchema = z.object({
	token: z.string(),
	refreshToken: z.string().optional(),
	tokenPayload: z.any() // AccessTokenPayload | RefreshTokenPayload schema can be added later
});

// Inferred types from schemas
export type ApiErrorObjectZod = z.infer<typeof ApiErrorObjectSchema>;
export type ApiResponseZod = z.infer<typeof ApiResponseSchema>;
export type SuccessResponseZod = z.infer<typeof SuccessResponseSchema>;
export type ErrorResponseZod = z.infer<typeof ErrorResponseSchema>;
export type TokenResponseZod = z.infer<typeof TokenResponseSchema>;
