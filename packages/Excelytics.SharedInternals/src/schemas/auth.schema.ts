/**
 * Authentication-related Zod schemas.
 * Used for validating authentication requests across microservices.
 * @module auth.schema
 */
import { z } from 'zod';

/**
 * Schema for token verification requests.
 * Used when verifying email confirmation, password reset tokens, etc.
 */
export const VerifyTokenSchema = z.object({
	token: z.string().min(1, 'Token cannot be empty')
});

/**
 * Schema for token refresh requests.
 * Used when requesting a new access token using a refresh token.
 */
export const RefreshTokenSchema = z.object({
	refreshToken: z.string().min(1, 'Refresh token cannot be empty')
});

/**
 * Schema for password validation.
 * Enforces password complexity requirements.
 */
export const PasswordSchema = z.string()
	.min(8, 'Password must be at least 8 characters long')
	.regex(/[A-Z]/, "Password must contain at least one uppercase letter")
	.regex(/[a-z]/, "Password must contain at least one lowercase letter")
	.regex(/[0-9]/, "Password must contain at least one number")
	.regex(/[^A-Za-z0-9]/, "Password must contain at least one special character");