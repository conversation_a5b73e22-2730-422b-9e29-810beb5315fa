/**
 * @module api.constants
 */

/**
 * Predefined API versions for routing and version management.
 * @readonly
 */
export const API_VERSION = {
	V0: 'v0',
	V1: 'v1',
	V2: 'v2',
	V3: 'v3',
	V4: 'v4'
} as const;

/**
 * A centralized collection of common HTTP methods.
 * @readonly
 */
export const HttpMethods = {
	GET: 'GET',
	PUT: 'PUT',
	POST: 'POST',
	HEAD: 'HEAD',
	PATCH: 'PATCH',
	DELETE: 'DELETE',
	OPTIONS: 'OPTIONS'
} as const;

/**
 * A centralized collection of common MIME types for the Content-Type header.
 * @readonly
 */
export const ContentTypes = {
	/** Standard JSON format. Used for most API request/response bodies. */
	APPLICATION_JSON: 'application/json',
	APPLICATION_JSON_CHARSET_UTF8: 'application/json; charset=utf-8',
	/** Standard for HTML form submissions. */
	FORM_URLENCODED: 'application/x-www-form-urlencoded',
	/** Used for submitting forms that contain files, non-ASCII data, and binary data. CRITICAL for your file upload feature. */
	MULTIPART_FORM_DATA: 'multipart/form-data',
	/** The specific MIME type for modern Microsoft Excel files (.xlsx). Essential for your Calc service. */
	EXCEL_XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
	/** Plain text format. */
	TEXT_PLAIN: 'text/plain',
	/** Standard for HTML pages. */
	TEXT_HTML: 'text/html',
	/** A generic binary data type for downloads or unknown file types. */
	OCTET_STREAM: 'application/octet-stream'
} as const;

/**
 * A centralized collection of HTTP header names used across the Introspection services.
 * Using constants for header names prevents typos and ensures consistency.
 * @readonly
 */
export const HttpHeaders = {
	// --- Standard Request Headers ---
	/** The media type of the resource in the request body (e.g., 'application/json'). */
	CONTENT_TYPE: 'Content-Type',
	/** Contains credentials to authenticate a user agent (e.g., 'Bearer <token>'). */
	AUTHORIZATION: 'Authorization',
	/** Indicates the origin of a cross-site access request. */
	ORIGIN: 'Origin',
	/** The address of the previous web page from which a link was followed. */
	REFERER: 'Referer',
	/** A string identifying the client software. */
	USER_AGENT: 'User-Agent',

	// --- Standard CORS Response Headers ---
	/** Specifies a URI that may access the resource. */
	ACCESS_CONTROL_ALLOW_ORIGIN: 'Access-Control-Allow-Origin',
	/** Used in response to a preflight request to indicate which headers can be used. */
	ACCESS_CONTROL_ALLOW_HEADERS: 'Access-Control-Allow-Headers',
	/** Used in response to a preflight request to indicate which methods are allowed. */
	ACCESS_CONTROL_ALLOW_METHODS: 'Access-Control-Allow-Methods',
	/** Indicates whether the response can be shared when the request's credentials mode is 'include'. */
	ACCESS_CONTROL_ALLOW_CREDENTIALS: 'Access-Control-Allow-Credentials',

	// --- Custom Introspection Headers ---
	/** A unique ID for tracing a request through multiple microservices. */
	X_REQUEST_ID: 'X-Request-ID',
	/** Identifies the calling service in service-to-service communication. */
	X_SERVICE_NAME: 'X-Service-Name',
	/** The version of the frontend client making the request. */
	X_CLIENT_VERSION: 'X-Client-Version',
	/** Passes the authenticated user's ID from a gateway to a downstream service. */
	X_USER_ID: 'X-User-ID',
	/** A custom header to request that the server's response not be compressed. */
	X_NO_COMPRESSION: 'X-No-Compression',

	// --- Custom Informational Response Headers ---
	/** Identifies which microservice is sending the response. */
	X_SERVICE: 'X-Service',
	/** The version of the API that served the request. */
	X_API_VERSION: 'X-API-Version',
	/** The environment the service is running in (e.g., 'development'). */
	X_ENVIRONMENT: 'X-Environment',

	// --- Standard Rate Limiting Headers ---
	/** The maximum number of requests permitted in the current window. */
	X_RATE_LIMIT_LIMIT: 'X-RateLimit-Limit',
	/** The number of requests remaining in the current window. */
	X_RATE_LIMIT_REMAINING: 'X-RateLimit-Remaining',
	/** The time at which the current rate limit window resets (UTC epoch seconds). */
	X_RATE_LIMIT_RESET: 'X-RateLimit-Reset'
} as const;

/**
 * A comprehensive collection of standard HTTP status codes.
 * @readonly
 */
export const HttpStatus = {
	// 1xx Informational
	CONTINUE: 100,
	SWITCHING_PROTOCOLS: 101,
	PROCESSING: 102,

	// 2xx Success
	OK: 200,
	CREATED: 201,
	ACCEPTED: 202, // Useful for async operations
	NO_CONTENT: 204,
	RESET_CONTENT: 205,

	// 3xx Redirection (Less common for API constants, but can be useful)
	MOVED_PERMANENTLY: 301,
	FOUND: 302,
	SEE_OTHER: 303,
	NOT_MODIFIED: 304,
	TEMPORARY_REDIRECT: 307,
	PERMANENT_REDIRECT: 308,

	// 4xx Client Errors
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401, // Authentication is required and has failed or has not yet been provided.
	PAYMENT_REQUIRED: 402,
	FORBIDDEN: 403, // Authenticated, but not authorized to access the resource.
	NOT_FOUND: 404,
	METHOD_NOT_ALLOWED: 405,
	NOT_ACCEPTABLE: 406,
	PROXY_AUTHENTICATION_REQUIRED: 407,
	REQUEST_TIMEOUT: 408,
	CONFLICT: 409, // Indicates a request conflict with current state of the server.
	GONE: 410,
	LENGTH_REQUIRED: 411,
	PRECONDITION_FAILED: 412,
	PAYLOAD_TOO_LARGE: 413,
	URI_TOO_LONG: 414,
	UNSUPPORTED_MEDIA_TYPE: 415,
	REQUESTED_RANGE_NOT_SATISFIABLE: 416,
	EXPECTATION_FAILED: 417,
	I_AM_A_TEAPOT: 418, // :)
	UNPROCESSABLE_ENTITY: 422, // Often for validation errors (semantic)
	LOCKED: 423,
	FAILED_DEPENDENCY: 424,
	TOO_EARLY: 425,
	UPGRADE_REQUIRED: 426,
	PRECONDITION_REQUIRED: 428,
	TOO_MANY_REQUESTS: 429, // Rate limiting
	REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
	UNAVAILABLE_FOR_LEGAL_REASONS: 451,

	// 5xx Server Errors
	INTERNAL_SERVER_ERROR: 500,
	NOT_IMPLEMENTED: 501,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
	HTTP_VERSION_NOT_SUPPORTED: 505,
	VARIANT_ALSO_NEGOTIATES: 506,
	INSUFFICIENT_STORAGE: 507,
	LOOP_DETECTED: 508,
	NOT_EXTENDED: 510,
	NETWORK_AUTHENTICATION_REQUIRED: 511
} as const;

/**
 * Common values for the Cache-Control HTTP header.
 * @readonly
 */
export const CacheControl = {
	/** The response may be cached by any cache. */
	PUBLIC: 'public',
	/** The response is intended for a single user and must not be stored by a shared cache. */
	PRIVATE: 'private',
	/** Forces caches to submit the request to the origin server for validation before releasing a cached copy. */
	NO_CACHE: 'no-cache',
	/** The cache should not store anything about the client request or server response. */
	NO_STORE: 'no-store',
	/** The response is considered fresh for the specified number of seconds. */
	MAX_AGE: (seconds: number) => `max-age=${seconds}`,
	/** Indicates the client is willing to accept a stale response. */
	MAX_STALE: (seconds: number) => `max-stale=${seconds}`,
	/** Once a resource is stale, caches must not use their stale copy without successful validation on the origin server. */
	MUST_REVALIDATE: 'must-revalidate'
} as const;