/**
 * Permission group definitions for role-based access control.
 * @module permission-group.constants
 */
import { EnumPermissions } from '../enums';

/**
 * Predefined permission groups for common role assignments.
 * Each group contains a set of permissions that define what actions users with that role can perform.
 */
export const PermissionGroups = {
	/**
     * Super admin permissions - full system access
     * Includes all administrative capabilities across all services
     */
	SUPER_ADMIN: [
		EnumPermissions.READ_LOGS,
		EnumPermissions.CALC_ADMIN,
		EnumPermissions.ADMIN_SYSTEM,
		EnumPermissions.MANAGE_USERS,
		EnumPermissions.CLIENT_ADMIN,
		EnumPermissions.FINANCE_ADMIN,
		EnumPermissions.IDENTITY_ADMIN,
		EnumPermissions.MANAGE_SESSIONS,
		EnumPermissions.MANAGE_SETTINGS
	],

	/**
     * User manager permissions
     * Focused on user management and session control
     */
	USER_MANAGER: [
		EnumPermissions.READ_USERS,
		EnumPermissions.CREATE_USERS,
		EnumPermissions.UPDATE_USERS,
		EnumPermissions.MANAGE_SESSIONS,
		EnumPermissions.IDENTITY_MANAGE_USERS
	],

	/**
     * Analyst permissions
     * Full access to data operations, reporting, and analytics
     */
	ANALYST: [
		// File operations
		EnumPermissions.READ_FILES,
		EnumPermissions.UPLOAD_FILES,
		EnumPermissions.PROCESS_FILES,
		EnumPermissions.DOWNLOAD_FILES,

		// Financial data
		EnumPermissions.READ_FINANCIAL_DATA,
		EnumPermissions.CREATE_FINANCIAL_DATA,
		EnumPermissions.UPDATE_FINANCIAL_DATA,

		// Reports and dashboards
		EnumPermissions.READ_REPORTS,
		EnumPermissions.CREATE_REPORTS,
		EnumPermissions.UPDATE_REPORTS,
		EnumPermissions.EXPORT_REPORTS,
		EnumPermissions.READ_DASHBOARDS,

		// Calculations
		EnumPermissions.READ_CALCULATIONS,
		EnumPermissions.CREATE_CALCULATIONS,
		EnumPermissions.PROCESS_CALCULATIONS,

		// Charts
		EnumPermissions.READ_CHARTS,
		EnumPermissions.CREATE_CHARTS,
		EnumPermissions.EXPORT_CHARTS,
		EnumPermissions.READ_ANALYTICS,

		// API access
		EnumPermissions.ACCESS_CALC_API,
		EnumPermissions.ACCESS_CLIENT_API,
		EnumPermissions.ACCESS_FINANCE_API
	],

	/**
     * Basic user permissions
     * Limited to self-operations and basic read access
     */
	USER: [
		// Self operations
		EnumPermissions.READ_SELF_FILES,
		EnumPermissions.READ_SELF_PROFILE,
		EnumPermissions.UPLOAD_SELF_FILES,
		EnumPermissions.UPDATE_SELF_PROFILE,
		EnumPermissions.DOWNLOAD_SELF_FILES,
		EnumPermissions.READ_SELF_CALCULATIONS,
		EnumPermissions.READ_SELF_FINANCIAL_DATA,
		EnumPermissions.CREATE_SELF_CALCULATIONS,
		EnumPermissions.CREATE_SELF_FINANCIAL_DATA,

		// Basic read operations
		EnumPermissions.READ_CHARTS,
		EnumPermissions.READ_TEMPLATES,

		// Basic API access
		EnumPermissions.ACCESS_API,
		EnumPermissions.ACCESS_CLIENT_API
	],

	/**
     * Guest permissions
     * Minimal access for unauthenticated or newly registered users
     */
	GUEST: [
		EnumPermissions.ACCESS_API,
		EnumPermissions.LOGIN_SYSTEM,
		EnumPermissions.READ_TEMPLATES
	]
} as const;