/**
 * Cache key definitions for Redis caching across Excelytics microservices.
 * @module cache.constants
 */

/**
 * Standardized cache key generators for various entity types.
 * Ensures consistent cache key formats across all services.
 */
export const CacheKeys = {
	/** Cache key for user profile data */
	USER_PROFILE: (userId: string) => `user:profile:${userId}`,

	/** Cache key for file processing status */
	FILE_PROCESSING: (fileId: string) => `file:processing:${fileId}`,

	/** Cache key for calculation results */
	CALCULATION_RESULT: (fileId: string) => `calc:result:${fileId}`,

	/** Cache key for model entities */
	MODEL_ENTITY: (modelName: string) => `models/${modelName}`
} as const;