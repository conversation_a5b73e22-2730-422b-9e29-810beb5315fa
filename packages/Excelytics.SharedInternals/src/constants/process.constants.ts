/**
 * Constants related to Node.js process handling, server operations, and database states.
 * @module process.constants
 */

/**
 * Common Node.js server error codes.
 * Used for handling server startup and listening errors.
 */
export const NodeServerErrors = {
	/**
     * Error code for "Permission Denied".
     * Occurs when a server attempts to listen on a port without sufficient permissions.
     */
	ACCESS_DENIED: 'EACCES',

	/**
     * Error code for "Address Already in Use".
     * Occurs when a server attempts to listen on a port that's already in use.
     */
	ADDRESS_IN_USE: 'EADDRINUSE',

	/**
     * The system call name associated with server listening errors.
     * Used for checking if an error originated from the `listen` operation.
     */
	LISTEN_SYSCALL: 'listen'
} as const;

/**
 * Process signal constants for handling graceful shutdown.
 * Used in signal handlers for proper application termination.
 */
export const ProcessSignals = {
	/**
     * Signal Interrupt (Ctrl+C in terminal).
     * Used to request process termination.
     */
	SIGNAL_INTERRUPT: 'SIGINT',

	/**
     * Signal Terminate.
     * Generic signal for process termination, often sent by process managers.
     */
	SIGNAL_TERMINATE: 'SIGTERM',

	/**
     * Custom identifier for unhandled promise rejections.
     * Used for consistent shutdown function calls.
     */
	UNHANDLED_REJECTION: 'UNHANDLED_REJECTION',

	/**
     * Custom identifier for uncaught exceptions.
     * Used for consistent shutdown function calls.
     */
	UNCAUGHT_EXCEPTION: 'UNCAUGHT_EXCEPTION'
} as const;

/**
 * Mongoose connection state codes.
 * Used for database connection status monitoring.
 */
export const MongooseStates = {
	/** Database disconnected */
	DISCONNECTED: 0,
	/** Database connected */
	CONNECTED: 1,
	/** Database connection in progress */
	CONNECTING: 2,
	/** Database disconnection in progress */
	DISCONNECTING: 3
} as const;

/**
 * Server-related constants for error handling and event management.
 * Used in server lifecycle management.
 */
export const ServerConstants = {
	/** Error code when attempting to interact with a server that's not running */
	SERVER_NOT_RUNNING: 'ERR_SERVER_NOT_RUNNING',
	/** Event name for unhandled promise rejections */
	UNHANDLED_REJECTION: 'unhandledRejection',
	/** Event name for uncaught exceptions */
	UNCAUGHT_EXCEPTION: 'uncaughtException',
	/** Event name for stream completion */
	FINISH: 'finish',
	/** Event name for errors */
	ERROR: 'error',
	/** Event name for server close */
	CLOSE: 'close',
	/** Event name for server connection */
	CONNECT: 'connect'
} as const;