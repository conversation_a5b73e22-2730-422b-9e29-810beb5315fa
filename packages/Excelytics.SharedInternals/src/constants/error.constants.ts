/**
 * Standardized error codes used across all Excelytics microservices.
 * Ensures consistent error handling and client responses.
 * @module error.constants
 */

/**
 * Comprehensive collection of error codes organized by category.
 * Each code maps to a specific error condition and typically corresponds to an HTTP status code.
 */
export const ErrorCodes = {
	// General Errors
	/** Unknown or unclassified error */
	UNKNOWN_ERROR: 'UNKNOWN_ERROR',
	/** Service temporarily unavailable (503) */
	SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
	/** Internal server error (500) */
	INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',

	// Input & Request Errors
	/** Resource not found (404) */
	NOT_FOUND: 'NOT_FOUND',
	/** Malformed request (400) */
	BAD_REQUEST: 'BAD_REQUEST',
	/** Unexpected error during processing */
	UNEXPECTED_ERROR: 'UNEXPECTED_ERROR',
	/** Request payload too large (413) */
	PAYLOAD_TOO_LARGE: 'PAYLOAD_TOO_LARGE',
	/** Required parameter missing */
	MISSING_PARAMETER: 'MISSING_PARAMETER',
	/** Parameter has invalid value or format */
	INVALID_PARAMETER: 'INVALID_PARAMETER',
	/** HTTP method not allowed for resource (405) */
	METHOD_NOT_ALLOWED: 'METHOD_NOT_ALLOWED',
	/** Request semantically incorrect (422) */
	UNPROCESSABLE_ENTITY: 'UNPROCESSABLE_ENTITY',
	/** Request body invalid or malformed */
	INVALID_REQUEST_BODY: 'INVALID_REQUEST_BODY',
	/** Media type not supported (415) */
	UNSUPPORTED_MEDIA_TYPE: 'UNSUPPORTED_MEDIA_TYPE',

	// CRUD Errors
	/** Data retrieval operation failed */
	FETCH_FAILED: 'FETCH_FAILED',
	/** Data update operation failed */
	UPDATE_FAILED: 'UPDATE_FAILED',
	/** Data deletion operation failed */
	DELETE_FAILED: 'DELETE_FAILED',

	// Authentication & Authorization Errors
	/** Authenticated but not authorized (403) */
	FORBIDDEN: 'FORBIDDEN',
	/** Authentication required (401) */
	UNAUTHORIZED: 'UNAUTHORIZED',
	/** Authentication token expired */
	TOKEN_EXPIRED: 'TOKEN_EXPIRED',
	/** Authentication token invalid */
	INVALID_TOKEN: 'INVALID_TOKEN',
	/** User account locked due to security policy */
	ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
	/** User not authenticated */
	UNAUTHENTICATED: 'UNAUTHENTICATED',
	/** User account disabled */
	ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
	/** Cross-origin request not allowed */
	CORS_NOT_ALLOWED: 'CORS_NOT_ALLOWED',
	/** CORS error */
	CORS_ERROR: 'CORS_ERROR',
	/** Permission denied for requested operation */
	PERMISSION_DENIED: 'PERMISSION_DENIED',
	/** Required permission missing */
	MISSING_PERMISSION: 'MISSING_PERMISSION',
	/** Login credentials invalid */
	INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
	/** User registration failed - user exists */
	USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
	/** User not found */
	USER_NOT_FOUND: 'USER_NOT_FOUND',
	/** User registration failed */
	REGISTRATION_FAILED: 'REGISTRATION_FAILED',
	/** Authentication process failed */
	AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
	/** Insufficient permissions for operation */
	INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
	/** Unknown error during registration */
	UNKNOWN_REGISTRATION_ERROR: 'UNKNOWN_REGISTRATION_ERROR',
	/** Session regeneration failed */
	SESSION_REGENERATION_FAILED: 'SESSION_REGENERATION_FAILED',

	// Validation Errors
	/** Input validation failed (400 or 422) */
	VALIDATION_ERROR: 'VALIDATION_ERROR',
	/** General validation failed */
	VALIDATION_FAILED: 'VALIDATION_FAILED',
	/** Invalid input provided */
	INVALID_INPUT: 'INVALID_INPUT',
	/** Invalid format provided */
	INVALID_FORMAT: 'INVALID_FORMAT',
	/** Invalid email format */
	INVALID_EMAIL_FORMAT: 'INVALID_EMAIL_FORMAT',
	/** Password too weak */
	WEAK_PASSWORD: 'WEAK_PASSWORD',
	/** Login form validation failed */
	LOGIN_VALIDATION_ERROR: 'LOGIN_VALIDATION_ERROR',
	/** Aggregate validation failed */
	AGGREGATE_VALIDATION_ERROR: 'AGGREGATE_VALIDATION_ERROR',
	/** Registration form validation failed */
	REGISTRATION_VALIDATION_ERROR: 'REGISTRATION_VALIDATION_ERROR',

	// Token Verification Errors
	/** Token introspection failed */
	INTROSPECTION_FAILED: 'INTROSPECTION_FAILED',
	/** Token refresh operation failed */
	TOKEN_REFRESH_FAILED: 'TOKEN_REFRESH_FAILED',
	/** Refresh token invalid */
	INVALID_REFRESH_TOKEN: 'INVALID_REFRESH_TOKEN',
	/** Error processing token */
	TOKEN_PROCESSING_ERROR: 'TOKEN_PROCESSING_ERROR',
	/** Error generating token */
	TOKEN_GENERATION_ERROR: 'TOKEN_GENERATION_ERROR',
	/** Token verification failed */
	TOKEN_VERIFICATION_FAILED: 'TOKEN_VERIFICATION_FAILED',

	// Resource & State Errors
	/** Resource state conflict (409) */
	CONFLICT: 'CONFLICT',
	/** Precondition for operation failed (412) */
	PRECONDITION_FAILED: 'PRECONDITION_FAILED',
	/** Operation not allowed by business rules */
	OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
	/** Resource already exists */
	RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',

	// Rate Limiting
	/** Too many requests (429) */
	TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',
	/** Rate limit exceeded */
	RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
	/** Specific action rate limited */
	ACTION_RATE_LIMITED: 'ACTION_RATE_LIMITED',
	/** Authentication rate limit exceeded */
	AUTH_RATE_LIMIT_EXCEEDED: 'AUTH_RATE_LIMIT_EXCEEDED',
	/** Calculation service rate limit exceeded */
	CALC_RATE_LIMIT_EXCEEDED: 'CALC_RATE_LIMIT_EXCEEDED',
	/** File upload rate limit exceeded */
	UPLOAD_RATE_LIMIT_EXCEEDED: 'UPLOAD_RATE_LIMIT_EXCEEDED',
	/** Calculation requests rate limit exceeded */
	CALCULATION_RATE_LIMIT_EXCEEDED: 'CALCULATION_RATE_LIMIT_EXCEEDED',
	/** Password reset rate limit exceeded */
	PASSWORD_RESET_RATE_LIMIT_EXCEEDED: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
	/** Heavy processing rate limit exceeded */
	HEAVY_PROCESSING_RATE_LIMIT_EXCEEDED: 'HEAVY_PROCESSING_RATE_LIMIT_EXCEEDED',

	// External Service Errors
	/** Bad gateway error (502) */
	BAD_GATEWAY: 'BAD_GATEWAY',
	/** External service error */
	EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
	/** External service timeout */
	EXTERNAL_SERVICE_TIMEOUT: 'EXTERNAL_SERVICE_TIMEOUT',

	// Database Errors
	/** General database error */
	DATABASE_ERROR: 'DATABASE_ERROR',
	/** Duplicate key error (MongoDB 11000) */
	DUPLICATE_KEY_ERROR: 'DUPLICATE_KEY_ERROR',

	// Custom Business Logic Errors
	/** Health check failed */
	HEALTH_CHECK_FAILED: 'HEALTH_CHECK_FAILED',
	/** Insufficient funds for operation */
	INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
	/** Item out of stock */
	ITEM_OUT_OF_STOCK: 'ITEM_OUT_OF_STOCK',
	/** User not verified */
	USER_NOT_VERIFIED: 'USER_NOT_VERIFIED'
} as const;