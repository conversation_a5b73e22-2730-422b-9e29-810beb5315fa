/**
 * Core application constants used across Excelytics microservices.
 * @module app.constants
 */

/**
 * Service name identifiers for all microservices in the Excelytics platform.
 * Used for service discovery, logging, and inter-service communication.
 */
export const ServiceNames = {
	/** Finance backend service */
	FINANCE: 'excelytics.finance',
	/** Frontend/web service */
	CLIENT: 'excelytics.client',
	/** Identity provider service (IdP) */
	IDENTITY: 'excelytics.identity',
	/** Calculation engine service */
	CALC: 'excelytics.calc',
	/** API Gateway service */
	GATEWAY: 'excelytics.gateway',
	/** Fallback: unknown or unspecified service */
	OTHER: 'excelytics.other'
} as const;

/**
 * List of domains that are allowed to access the API (Revised Ports).
 * Used for CORS configuration across all microservices.
 */
export const AllowedOrigins = [
	// local development environments
	'http://localhost:6002',    // IdP
	'http://localhost:6003',    // Calc
	'http://localhost:4200',    // Client  (FE)
	'http://localhost:6001',    // Finance (BE)

	// Development with Tailscale IP (private network)
	'http://************:6002', // IdP
	'http://************:4200', // Calc
	'http://************:4200', // Client  (FE)
	'http://************:6001', // Finance (BE)

	// These are crucial for OAuth/OIDC flows to know where to redirect users
	'https://auth.excelytics.co.za', // Auth0
	'https://app.excelytics.co.za',  // App (FE)

	// Production domains
	'https://excelytics.co.za',     // Main production domain
	'https://www.excelytics.co.za', // WWW variant
	'https://api.excelytics.co.za', // API subdomain if used
	'https://app.excelytics.co.za'  // App subdomain if used
] as const;