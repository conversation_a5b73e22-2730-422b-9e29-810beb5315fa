/**
 * Hierarchical message constants organized by category and type
 * Static string constants only - dynamic functions moved to message.helper.ts
 * Provides consistent messaging across all Excelytics microservices
 *
 * ===== MIGRATION GUIDE =====
 *
 * NEW STRUCTURE (Recommended - Use these):
 * - SCREAMING_SNAKE_CASE constants from SharedInternals
 * - Standardized across all Excelytics microservices
 * - Consistent naming convention
 *
 * LEGACY CONSTANTS (Still supported):
 * - PascalCase constants from team member's repo (old-messages.ts)
 * - Kept for backward compatibility
 * - Teams can migrate at their own pace
 *
 * DYNAMIC FUNCTIONS:
 * - Moved to MessageHelpers in message.helper.ts
 * - Import { MessageHelpers } from '../helpers/message.helper'
 *
 * USAGE EXAMPLES:
 *
 * // NEW (Recommended - Use these)
 * Messages.Generic.Errors.UNKNOWN                // NEW SharedInternals format
 * Messages.Auth.Errors.INVALID_CREDENTIALS       // NEW SharedInternals format
 * Messages.User.Errors.USER_NOT_FOUND            // NEW SharedInternals format
 *
 * // LEGACY (Still works - migrate when convenient)
 * Messages.Generic.Errors.NoDataFound            // OLD from team member's repo
 * Messages.Repository.Errors.FailedCreate        // OLD from team member's repo
 *
 * // DYNAMIC FUNCTIONS (Use MessageHelpers)
 * MessageHelpers.Errors.DUPLICATE_FIELD_EXISTS('email')  // NEW
 * MessageHelpers.Success.ENTITY_CREATED('User')          // NEW
 *
 * @module messages.constants
 */

/**
 * Static message constants organized by category and type
 * Dynamic functions are in MessageHelpers (message.helper.ts)
 */
export const Messages = {
	/**
	 * Generic system messages
	 */
	Generic: {
		Errors: {
			// ===== LEGACY CONSTANTS (Still supported - from team member's repo) =====
			/** Legacy: No data found */
			NoDataFound: 'No data found',
			/** Legacy: Request body is required */
			RequestBodyRequired: 'Request body is required',
			/** Legacy: Missing request body */
			MissingRequestBody: 'Missing request body',
			/** Legacy: Id parameter is required */
			IdParameterRequired: 'Id parameter is required',
			/** Legacy: Resource not found */
			ResourceNotFound: 'Resource not found',
			/** Legacy: An unexpected error occurred */
			UnexpectedError: 'An unexpected error occurred',

			// ===== NEW STRUCTURE (Recommended - SharedInternals standard) =====
			/** New: No data found - use instead of NoDataFound */
			NO_DATA_FOUND: 'No data found',
			/** New: Request body is required - use instead of RequestBodyRequired */
			REQUEST_BODY_REQUIRED: 'Request body is required',
			/** New: Missing request body - use instead of MissingRequestBody */
			MISSING_REQUEST_BODY: 'Missing request body',
			/** New: Id parameter is required - use instead of IdParameterRequired */
			ID_PARAMETER_REQUIRED: 'Id parameter is required',
			/** New: Resource not found - use instead of ResourceNotFound */
			RESOURCE_NOT_FOUND: 'Resource not found',
			/** New: An unexpected error occurred - use instead of UnexpectedError */
			UNEXPECTED_ERROR: 'An unexpected error occurred',
			/** New: An unknown error occurred */
			UNKNOWN: 'An unknown error occurred',
			/** New: Internal server error occurred */
			INTERNAL_SERVER: 'An internal server error occurred. Please try again later.',
			/** New: Service is temporarily unavailable */
			SERVICE_UNAVAILABLE: 'Service is temporarily unavailable. Please try again later.',
			/** New: Network error occurred */
			NETWORK_ERROR: 'Network error occurred. Please check your connection.',
			/** New: Request timed out */
			TIMEOUT: 'Request timed out. Please try again.',
			/** New: System is under maintenance */
			MAINTENANCE: 'System is under maintenance. Please try again later.'
		},
		Success: {
			// ===== LEGACY CONSTANTS (Still supported - from team member's repo) =====
			/** Legacy: Resource created successfully */
			ResourceCreated: 'Resource created successfully',
			/** Legacy: Resource updated successfully */
			ResourceUpdated: 'Resource updated successfully',
			/** Legacy: Resource deleted successfully */
			ResourceDeleted: 'Resource deleted successfully',

			// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====
			/** Legacy: Dynamic message for entity creation */
			EntityCreated: (entityName: string) => `${entityName} created successfully.`,
			/** Legacy: Dynamic message for entity retrieval */
			EntityRetrieved: (entityName: string) => `${entityName} retrieved successfully.`,
			/** Legacy: Dynamic message for entity update */
			EntityUpdated: (entityName: string) => `${entityName} updated successfully.`,
			/** Legacy: Dynamic message for entity deletion */
			EntityDeleted: (entityName: string, id: string) => `${entityName} with ${id} deleted successfully.`,

			// ===== NEW STRUCTURE (Recommended - SharedInternals standard) =====
			/** New: Resource created successfully - use instead of ResourceCreated */
			RESOURCE_CREATED: 'Resource created successfully',
			/** New: Resource updated successfully - use instead of ResourceUpdated */
			RESOURCE_UPDATED: 'Resource updated successfully',
			/** New: Resource deleted successfully - use instead of ResourceDeleted */
			RESOURCE_DELETED: 'Resource deleted successfully',
			/** New: Operation completed successfully */
			OPERATION_COMPLETED: 'Operation completed successfully',
			/** New: Data saved successfully */
			DATA_SAVED: 'Data saved successfully',
			/** New: Data updated successfully */
			DATA_UPDATED: 'Data updated successfully',
			/** New: Data deleted successfully */
			DATA_DELETED: 'Data deleted successfully',
			/** New: Request processed successfully */
			REQUEST_PROCESSED: 'Request processed successfully',

			// ===== NEW DYNAMIC FUNCTIONS (Recommended - SharedInternals standard) =====
			/** New: Dynamic message for entity creation - use instead of EntityCreated */
			ENTITY_CREATED: (entityName: string) => `${entityName} created successfully.`,
			/** New: Dynamic message for entity retrieval - use instead of EntityRetrieved */
			ENTITY_RETRIEVED: (entityName: string) => `${entityName} retrieved successfully.`,
			/** New: Dynamic message for entity update - use instead of EntityUpdated */
			ENTITY_UPDATED: (entityName: string) => `${entityName} updated successfully.`,
			/** New: Dynamic message for entity deletion - use instead of EntityDeleted */
			ENTITY_DELETED: (entityName: string, id: string) => `${entityName} with ID ${id} deleted successfully.`,
			/** New: Dynamic message for operation completion */
			OPERATION_COMPLETED_WITH_COUNT: (operation: string, count: number) => `${operation} completed successfully. ${count} items processed.`,
			/** New: Dynamic message for batch operation */
			BATCH_OPERATION_SUCCESS: (operation: string, successCount: number, totalCount: number) => `${operation} completed: ${successCount}/${totalCount} items processed successfully.`,
		},
		Info: {
			LOADING: 'Loading...',
			PROCESSING: 'Processing your request...',
			PLEASE_WAIT: 'Please wait while we process your request',
			NO_DATA: 'No data available',
			COMING_SOON: 'This feature is coming soon'
		},
		Warning: {
			UNSAVED_CHANGES: 'You have unsaved changes',
			SESSION_EXPIRING: 'Your session is about to expire',
			SLOW_CONNECTION: 'Slow connection detected',
			BETA_FEATURE: 'This is a beta feature'
		}
	},

	/**
	 * Authentication and authorization messages
	 * Dynamic functions moved to MessageHelpers.Auth
	 */
	Auth: {
		// ===== LEGACY CONSTANTS (Still supported - migrate when convenient) =====
		Error: {
			INVALID_CREDENTIALS: 'Invalid email or password',
			INVALID_TOKEN: 'Token is invalid or expired',
			TOKEN_EXPIRED: 'Your session has expired. Please log in again.',
			UNAUTHORIZED: 'You are not authorized to access this resource',
			FORBIDDEN: 'Access denied. Insufficient permissions.',
			ACCOUNT_LOCKED: 'Your account has been locked due to security policy',
			ACCOUNT_DISABLED: 'Your account has been disabled',
			SESSION_EXPIRED: 'Your session has expired. Please log in again.',
			LOGIN_FAILED: 'Login failed. Please check your credentials.',
			REGISTRATION_FAILED: 'Registration failed. Please try again.',
			PASSWORD_RESET_FAILED: 'Password reset failed. Please try again.',
			INSUFFICIENT_PERMISSIONS: 'You do not have sufficient permissions for this action'
		},

		// ===== NEW STRUCTURE (Recommended - Use these) =====
		Errors: {
			InvalidCredentials: 'Invalid email or password',
			InvalidToken: 'Token is invalid or expired',
			TokenExpired: 'Your session has expired. Please log in again.',
			Unauthorized: 'You are not authorized to access this resource',
			Forbidden: 'Access denied. Insufficient permissions.',
			AccountLocked: 'Your account has been locked due to security policy',
			AccountDisabled: 'Your account has been disabled',
			SessionExpired: 'Your session has expired. Please log in again.',
			LoginFailed: 'Login failed. Please check your credentials.',
			RegistrationFailed: 'Registration failed. Please try again.',
			PasswordResetFailed: 'Password reset failed. Please try again.',
			InsufficientPermissions: 'You do not have sufficient permissions for this action'
		},

		// ===== LEGACY SUCCESS CONSTANTS (Still supported - migrate when convenient) =====
		Success: {
			LOGIN_SUCCESS: 'Login successful',
			LOGOUT_SUCCESS: 'Logout successful',
			REGISTRATION_SUCCESS: 'Registration completed successfully',
			PASSWORD_RESET_SUCCESS: 'Password reset successfully',
			PASSWORD_CHANGED: 'Password changed successfully',
			TOKEN_REFRESHED: 'Session refreshed successfully',
			ACCOUNT_VERIFIED: 'Account verified successfully',
			PROFILE_UPDATED: 'Profile updated successfully'
		},

		// ===== NEW STRUCTURE (Recommended - Use these) =====
		Messages: {
			LoginSuccess: 'Login successful',
			LogoutSuccess: 'Logout successful',
			RegistrationSuccess: 'Registration completed successfully',
			PasswordResetSuccess: 'Password reset successfully',
			PasswordChanged: 'Password changed successfully',
			TokenRefreshed: 'Session refreshed successfully',
			AccountVerified: 'Account verified successfully',
			ProfileUpdated: 'Profile updated successfully'
		},

		Info: {
			LOGGING_IN: 'Logging you in...',
			REGISTERING: 'Creating your account...',
			VERIFYING: 'Verifying your credentials...',
			RESETTING_PASSWORD: 'Resetting your password...',
			CHECK_EMAIL: 'Please check your email for further instructions'
		},

		Warning: {
			WEAK_PASSWORD: 'Password is too weak. Please choose a stronger password.',
			PASSWORD_EXPIRING: 'Your password will expire soon',
			MULTIPLE_LOGIN_ATTEMPTS: 'Multiple failed login attempts detected'
		}
	},

	/**
	 * User management messages
	 */
	User: {
		// ===== LEGACY CONSTANTS (Still supported - migrate when convenient) =====
		Error: {
			USER_NOT_FOUND: 'User not found',
			USER_ALREADY_EXISTS: 'A user with this email already exists',
			EMAIL_ALREADY_REGISTERED: 'This email is already registered',
			INVALID_EMAIL: 'Please enter a valid email address',
			INVALID_USER_DATA: 'Invalid user data provided',
			USER_CREATION_FAILED: 'Failed to create user account',
			USER_UPDATE_FAILED: 'Failed to update user information',
			USER_DELETION_FAILED: 'Failed to delete user account',
			PROFILE_INCOMPLETE: 'Please complete your profile'
		},

		Success: {
			USER_CREATED: 'User account created successfully',
			USER_UPDATED: 'User information updated successfully',
			USER_DELETED: 'User account deleted successfully',
			PROFILE_COMPLETED: 'Profile completed successfully',
			EMAIL_VERIFIED: 'Email address verified successfully',
			PREFERENCES_SAVED: 'Preferences saved successfully'
		},

		// ===== NEW STRUCTURE (Recommended - Use these) =====
		Errors: {
			UserNotFound: 'User not found',
			UserAlreadyExists: 'A user with this email already exists',
			EmailAlreadyRegistered: 'This email is already registered',
			InvalidEmail: 'Please enter a valid email address',
			InvalidUserData: 'Invalid user data provided',
			UserCreationFailed: 'Failed to create user account',
			UserUpdateFailed: 'Failed to update user information',
			UserDeletionFailed: 'Failed to delete user account',
			ProfileIncomplete: 'Please complete your profile'
		},

		Messages: {
			UserCreated: 'User account created successfully',
			UserUpdated: 'User information updated successfully',
			UserDeleted: 'User account deleted successfully',
			ProfileCompleted: 'Profile completed successfully',
			EmailVerified: 'Email address verified successfully',
			PreferencesSaved: 'Preferences saved successfully'
		},

		Info: {
			CREATING_USER: 'Creating user account...',
			UPDATING_USER: 'Updating user information...',
			LOADING_PROFILE: 'Loading user profile...',
			VERIFYING_EMAIL: 'Verifying email address...'
		}
	},

	/**
	 * Repository layer messages (from team member's repo)
	 * Dynamic functions moved to MessageHelpers.Repository
	 */
	Repository: {
		Errors: {
			// ===== LEGACY CONSTANTS (Still supported - from team member's repo) =====
			ModelRequired: 'A Mongoose model is required for the RepositoryService.',
			FailedCreate: 'Failed to create document.',
			FailedGet: 'Failed to get documents.',
			InvalidDocumentId: 'Invalid document Id',
			FailedFetchDocumentId: 'Failed to fetch document by Id',
			FailedFetch: 'Failed to fetch documents',
			FailedFind: 'Failed to find documents',
			InvalidUpdateId: 'Invalid update Id',
			FailedUpdate: 'Failed to update document in database.',
			InvalidDeleteId: 'Invalid delete Id',
			FailedDelete: 'Failed to delete document from database.',
			FailedCount: 'Failed to retrieve count.'
		}
	},

	/**
	 * Mutator layer messages (from team member's repo)
	 * Dynamic functions moved to MessageHelpers.Mutator
	 */
	Mutator: {
		Errors: {
			// Static constants only - dynamic functions in MessageHelpers.Mutator
		}
	},

	/**
	 * Validation messages
	 * Dynamic functions moved to MessageHelpers.Validation
	 */
	Validation: {
		Basic: {
			// ===== LEGACY CONSTANTS (Still supported - from team member's repo) =====
			MultipleErrors: 'Multiple validation errors occurred.',

			// ===== NEW STRUCTURE (Recommended - SharedInternals standard) =====
			VALIDATION_FAILED: 'Validation failed. Please check your input.',
			REQUIRED_FIELD: 'This field is required',
			INVALID_FORMAT: 'Invalid format provided',
			INVALID_EMAIL_FORMAT: 'Please enter a valid email address',
			INVALID_PASSWORD_FORMAT: 'Password must meet the required criteria',
			INVALID_PHONE_FORMAT: 'Please enter a valid phone number',
			INVALID_DATE_FORMAT: 'Please enter a valid date',
			INVALID_NUMBER_FORMAT: 'Please enter a valid number',
			VALUE_TOO_SHORT: 'Value is too short',
			VALUE_TOO_LONG: 'Value is too long',
			INVALID_RANGE: 'Value is outside the allowed range',
			INVALID_ENUM_VALUE: 'Invalid option selected'
		},
		CompanyDirectory: {
			// ===== LEGACY CONSTANTS (Still supported - from team member's repo) =====
			RegistrationNumberExists: 'A company directory with this registration number already exists.',
			RegistrationDuplicate: 'Duplicate registration number'
		},
		Success: {
			VALIDATION_PASSED: 'All fields are valid',
			FORMAT_CORRECT: 'Format is correct'
		},
		Info: {
			VALIDATING: 'Validating input...',
			CHECK_FORMAT: 'Please check the format of your input'
		}
	},

	/**
	 * Excel processing messages
	 * Dynamic functions moved to MessageHelpers.Excel
	 */
	Excel: {
		Errors: {
			INVALID_FILE_FORMAT: 'Invalid Excel file format',
			PARSING_FAILED: 'Failed to parse Excel file',
			EMPTY_FILE: 'Excel file is empty',
			CORRUPTED_FILE: 'Excel file appears to be corrupted',
			UNSUPPORTED_VERSION: 'Unsupported Excel file version'
		},
		Success: {
			FILE_PARSED: 'Excel file parsed successfully',
			DATA_EXTRACTED: 'Data extracted from Excel file successfully',
			CALCULATIONS_COMPLETE: 'Excel calculations completed successfully'
		},
		Info: {
			PARSING_FILE: 'Parsing Excel file...',
			EXTRACTING_DATA: 'Extracting data from Excel file...',
			PROCESSING_FORMULAS: 'Processing Excel formulas...'
		}
	},

	/**
	 * File and upload messages
	 */
	File: {
		Error: {
			INVALID_FILE_TYPE: 'Invalid file type. Please upload a supported file.',
			FILE_TOO_LARGE: 'File is too large. Maximum size allowed is {maxSize}.',
			FILE_TOO_SMALL: 'File is too small. Minimum size required is {minSize}.',
			UPLOAD_FAILED: 'File upload failed. Please try again.',
			FILE_NOT_FOUND: 'File not found',
			FILE_CORRUPTED: 'File appears to be corrupted',
			PROCESSING_FAILED: 'File processing failed',
			INVALID_FILE_FORMAT: 'Invalid file format. Supported formats: {formats}'
		},
		Success: {
			UPLOAD_SUCCESS: 'File uploaded successfully',
			PROCESSING_COMPLETE: 'File processing completed',
			FILE_SAVED: 'File saved successfully',
			FILE_DELETED: 'File deleted successfully'
		},
		Info: {
			UPLOADING: 'Uploading file...',
			PROCESSING: 'Processing file...',
			ANALYZING: 'Analyzing file content...',
			SUPPORTED_FORMATS: 'Supported formats: {formats}'
		},
		Warning: {
			LARGE_FILE: 'Large file detected. Processing may take longer.',
			OVERWRITE_WARNING: 'This will overwrite the existing file'
		}
	},

	/**
	 * Database and data messages
	 */
	Database: {
		Error: {
			CONNECTION_FAILED: 'Database connection failed',
			QUERY_FAILED: 'Database query failed',
			DUPLICATE_ENTRY: 'Duplicate entry detected',
			CONSTRAINT_VIOLATION: 'Database constraint violation',
			TRANSACTION_FAILED: 'Database transaction failed',
			DATA_CORRUPTION: 'Data corruption detected'
		},
		Success: {
			CONNECTION_ESTABLISHED: 'Database connection established',
			QUERY_EXECUTED: 'Database query executed successfully',
			TRANSACTION_COMPLETED: 'Database transaction completed'
		},
		Info: {
			CONNECTING: 'Connecting to database...',
			EXECUTING_QUERY: 'Executing database query...',
			MIGRATING: 'Running database migrations...'
		}
	},

	/**
	 * API and network messages
	 */
	Api: {
		Error: {
			REQUEST_FAILED: 'API request failed',
			INVALID_RESPONSE: 'Invalid API response received',
			RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
			CORS_ERROR: 'Cross-origin request blocked',
			NETWORK_UNAVAILABLE: 'Network is unavailable',
			EXTERNAL_SERVICE_ERROR: 'External service error occurred'
		},
		Success: {
			REQUEST_SUCCESS: 'API request completed successfully',
			DATA_RETRIEVED: 'Data retrieved successfully',
			DATA_SYNCHRONIZED: 'Data synchronized successfully'
		},
		Info: {
			MAKING_REQUEST: 'Making API request...',
			SYNCHRONIZING: 'Synchronizing data...',
			RETRYING: 'Retrying request...'
		}
	},

	/**
	 * Test environment messages
	 */
	Test: {
		Error: {
			TEST_FAILED: 'Test failed',
			ASSERTION_FAILED: 'Assertion failed',
			MOCK_ERROR: 'Mock function error',
			SETUP_FAILED: 'Test setup failed',
			TEARDOWN_FAILED: 'Test teardown failed'
		},
		Success: {
			TEST_PASSED: 'Test passed successfully',
			ALL_TESTS_PASSED: 'All tests passed',
			SETUP_COMPLETE: 'Test setup completed',
			TEARDOWN_COMPLETE: 'Test teardown completed'
		},
		Info: {
			RUNNING_TESTS: 'Running tests...',
			SETTING_UP: 'Setting up test environment...',
			CLEANING_UP: 'Cleaning up test environment...',
			MOCK_DATA_CREATED: 'Mock data created for testing'
		}
	},

	/**
	 * Business logic specific messages for Excelytics
	 */
	Business: {
		Finance: {
			Error: {
				CALCULATION_FAILED: 'Financial calculation failed',
				INVALID_FORMULA: 'Invalid formula provided',
				INSUFFICIENT_DATA: 'Insufficient data for calculation',
				EXCEL_PARSE_ERROR: 'Failed to parse Excel file',
				CHART_GENERATION_FAILED: 'Chart generation failed'
			},
			Success: {
				CALCULATION_COMPLETE: 'Financial calculation completed',
				EXCEL_PROCESSED: 'Excel file processed successfully',
				CHART_GENERATED: 'Chart generated successfully',
				REPORT_CREATED: 'Financial report created successfully'
			},
			Info: {
				CALCULATING: 'Performing financial calculations...',
				PARSING_EXCEL: 'Parsing Excel file...',
				GENERATING_CHART: 'Generating chart...',
				CREATING_REPORT: 'Creating financial report...'
			}
		},
		Analytics: {
			Error: {
				DATA_ANALYSIS_FAILED: 'Data analysis failed',
				INSUFFICIENT_SAMPLE_SIZE: 'Insufficient sample size for analysis',
				INVALID_DATA_RANGE: 'Invalid data range selected'
			},
			Success: {
				ANALYSIS_COMPLETE: 'Data analysis completed',
				INSIGHTS_GENERATED: 'Insights generated successfully',
				DASHBOARD_UPDATED: 'Dashboard updated successfully'
			},
			Info: {
				ANALYZING_DATA: 'Analyzing data...',
				GENERATING_INSIGHTS: 'Generating insights...',
				UPDATING_DASHBOARD: 'Updating dashboard...'
			}
		}
	}
} as const;

/**
 * Type for accessing message paths
 */
export type MessagePath = keyof typeof Messages;

// getMessage function and MessageHelpers moved to message.helper.ts to avoid conflicts
// Import { getMessage, MessageHelpers } from '../helpers/message.helper' to use dynamic functions

// Export default for compatibility with existing usage
export default Messages;
