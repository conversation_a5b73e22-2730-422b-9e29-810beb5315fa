/**
 * Tests for error helper functions
 * Ensures all helper functions work correctly and maintain backward compatibility
 */
import './setup'; // Import test environment setup
import { test, expect, describe, beforeEach, mock } from 'bun:test';
import {
	logBasicErrorInfo,
	logBaseErrorDetails,
	logGenericErrorDetails
} from '../helpers/error-logging.helper';
import {
	processZodErrorResponse,
	processCorsErrorResponse,
	processGenericErrorResponse,
	addStackTraceToResponse
} from '../helpers/error-response.helper';
import { BaseError, UnauthorizedError } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';
import type { ErrorResponse } from '../types/api/response.types';
import type { Request } from 'express';

// Mock console methods
const mockConsoleError = mock(() => {});
const mockConsoleLog = mock(() => {});

beforeEach(() => {
	mockConsoleError.mockClear();
	mockConsoleLog.mockClear();
	console.error = mockConsoleError;
	console.log = mockConsoleLog;
});

describe('Error Logging Helper Functions', () => {
	const mockRequest = {
		method: 'POST',
		originalUrl: '/api/v1/test'
	} as Request;

	describe('logBasicErrorInfo', () => {
		test('should log basic error information', () => {
			logBasicErrorInfo(mockRequest);

			expect(mockConsoleError).toHaveBeenCalledWith('--- Global Error Handler Triggered ---');
			expect(mockConsoleError).toHaveBeenCalledWith('[Request Path]:', 'POST /api/v1/test');
			expect(mockConsoleError).toHaveBeenCalledWith('[Timestamp]:', expect.any(String));
		});
	});

	describe('logBaseErrorDetails', () => {
		test('should log BaseError details in verbose mode', () => {
			const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: true, isBaseError: true, isZodError: false, isCorsError: false };

			logBaseErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Error Type]: BaseError');
			expect(mockConsoleError).toHaveBeenCalledWith('[Error Code]:', 'INVALID_TOKEN');
			expect(mockConsoleError).toHaveBeenCalledWith('[Status Code]:', 401);
			expect(mockConsoleError).toHaveBeenCalledWith('[Message]:', 'Test error');
		});

		test('should not log expected errors in non-verbose mode', () => {
			const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
			const config = { isVerbose: false, isDebugMode: false, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: true, isBaseError: true, isZodError: false, isCorsError: false };
			
			logBaseErrorDetails(error, config, classification);
			
			expect(mockConsoleError).not.toHaveBeenCalled();
		});

		test('should log error details with sensitive information when enabled', () => {
			const error = new BaseError(
				'Test error',
				HttpStatus.BAD_REQUEST,
				ErrorCodes.VALIDATION_FAILED,
				{ password: 'secret123', email: '<EMAIL>' }
			);
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: true };
			const classification = { isExpectedError: true, isBaseError: true, isZodError: false, isCorsError: false };

			logBaseErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Debug Details]:', expect.stringContaining('password'));
		});

		test('should redact sensitive information when disabled', () => {
			const error = new BaseError(
				'Test error',
				HttpStatus.BAD_REQUEST,
				ErrorCodes.VALIDATION_FAILED,
				{ password: 'secret123', email: '<EMAIL>' }
			);
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: true, isBaseError: true, isZodError: false, isCorsError: false };

			logBaseErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Details REDACTED] - Set LOG_SENSITIVE=true to view');
		});
	});

	describe('logGenericErrorDetails', () => {
		test('should log generic error details for unexpected errors', () => {
			const error = new Error('Unexpected error');
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			logGenericErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Error Type]:', 'Error');
			expect(mockConsoleError).toHaveBeenCalledWith('[Message]:', 'Unexpected error');
		});

		test('should not log expected generic errors', () => {
			const error = new Error('Expected error');
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: true, isBaseError: false, isZodError: false, isCorsError: false };
			
			logGenericErrorDetails(error, config, classification);
			
			expect(mockConsoleError).not.toHaveBeenCalled();
		});

		test('should log stack trace when enabled', () => {
			const error = new Error('Test error');
			error.stack = 'Error: Test error\n    at test.js:1:1';
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: true };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			logGenericErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Stack]:', expect.stringContaining('Error: Test error'));
		});

		test('should redact stack trace when sensitive logging disabled', () => {
			const error = new Error('Test error');
			error.stack = 'Error: Test error\n    at test.js:1:1';
			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			logGenericErrorDetails(error, config, classification);

			expect(mockConsoleError).toHaveBeenCalledWith('[Stack REDACTED] - Set LOG_SENSITIVE=true to view');
		});
	});
});

describe('Error Response Helper Functions', () => {
	describe('processZodErrorResponse', () => {
		test('should process Zod errors correctly', () => {
			const zodError = {
				name: 'ZodError',
				format: mock(() => ({
					email: { _errors: ['Invalid email format'] },
					password: { _errors: ['Password too short'] }
				}))
			};
			
			const result = processZodErrorResponse(zodError);
			
			expect(result.statusCode).toBe(400);
			expect(result.responseBody.success).toBe(false);
			expect(result.responseBody.error.code).toBe('VALIDATION_ERROR');
		});
	});

	describe('processCorsErrorResponse', () => {
		test('should process CORS errors correctly', () => {
			const corsError = {
				message: 'Not allowed by CORS',
				statusCode: 403
			};
			
			const result = processCorsErrorResponse(corsError);
			
			expect(result.statusCode).toBe(403);
			expect(result.responseBody.success).toBe(false);
			expect(result.responseBody.error.code).toBe('CORS_NOT_ALLOWED');
		});
	});

	describe('processGenericErrorResponse', () => {
		test('should process generic errors correctly', () => {
			const error = new Error('Unexpected error');
			
			const result = processGenericErrorResponse(error);
			
			expect(result.statusCode).toBe(500);
			expect(result.responseBody.success).toBe(false);
			expect(result.responseBody.error.code).toBe('UNKNOWN_ERROR');
		});

		test('should preserve status code from error object', () => {
			const error = { statusCode: 503, message: 'Service unavailable' };
			
			const result = processGenericErrorResponse(error);
			
			expect(result.statusCode).toBe(503);
		});
	});

	describe('addStackTraceToResponse', () => {
		test('should add stack trace in debug mode with sensitive logging', () => {
			const error = new Error('Test error');
			error.stack = 'Error: Test error\n    at test.js:1:1';

			const responseBody: ErrorResponse = {
				success: false,
				message: 'Test error',
				error: { code: 'TEST_ERROR', message: 'Test error' }
			};

			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: true };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			addStackTraceToResponse(error, responseBody, config, classification);

			expect(responseBody.error.details).toBeDefined();
			expect(responseBody.error.details?.stack).toContain('Error: Test error');
		});

		test('should redact stack trace when sensitive logging disabled', () => {
			const error = new Error('Test error');
			error.stack = 'Error: Test error\n    at test.js:1:1';

			const responseBody: ErrorResponse = {
				success: false,
				message: 'Test error',
				error: { code: 'TEST_ERROR', message: 'Test error' }
			};

			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			addStackTraceToResponse(error, responseBody, config, classification);

			expect(responseBody.error.details?.stack).toBe('[REDACTED] - Set LOG_SENSITIVE=true to view stack trace');
		});

		test('should not add stack trace for BaseError', () => {
			const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
			error.stack = 'Error: Test error\n    at test.js:1:1';

			const responseBody: ErrorResponse = {
				success: false,
				message: 'Test error',
				error: { code: 'INVALID_TOKEN', message: 'Test error' }
			};

			const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: true };
			const classification = { isExpectedError: true, isBaseError: true, isZodError: false, isCorsError: false };

			addStackTraceToResponse(error, responseBody, config, classification);

			expect(responseBody.error.details).toBeUndefined();
		});

		test('should not add stack trace when not in debug mode', () => {
			const error = new Error('Test error');
			error.stack = 'Error: Test error\n    at test.js:1:1';

			const responseBody: ErrorResponse = {
				success: false,
				message: 'Test error',
				error: { code: 'TEST_ERROR', message: 'Test error' }
			};

			const config = { isVerbose: true, isDebugMode: false, shouldLogSensitiveInfo: true };
			const classification = { isExpectedError: false, isBaseError: false, isZodError: false, isCorsError: false };

			addStackTraceToResponse(error, responseBody, config, classification);

			expect(responseBody.error.details).toBeUndefined();
		});
	});
});
