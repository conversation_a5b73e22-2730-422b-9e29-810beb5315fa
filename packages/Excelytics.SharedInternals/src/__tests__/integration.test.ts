/**
 * Integration tests for GlobalErrorHandler with real Express app
 * Tests actual HTTP requests and responses to ensure consumer compatibility
 */
import './setup'; // Import test environment setup
import { test, expect, describe, beforeAll, afterAll } from 'bun:test';
import express, { type Request, type Response, type NextFunction } from 'express';
import request from 'supertest';
import { GlobalErrorHandler } from '../middleware/error-handler.middleware';
import { BaseError, UnauthorizedError, ValidationError, NotFoundError, ConflictError } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';

// Create test Express app
const createTestApp = () => {
	const app = express();
	app.use(express.json());

	// Test routes that throw different types of errors
	app.get('/test/unauthorized', (req: Request, res: Response, next: NextFunction) => {
		const error = new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);
		next(error);
	});

	app.get('/test/validation', (req: Request, res: Response, next: NextFunction) => {
		const error = new ValidationError('Invalid input data', {
			field: 'email',
			reason: 'Invalid format'
		});
		next(error);
	});

	app.get('/test/not-found', (req: Request, res: Response, next: NextFunction) => {
		const error = new NotFoundError('User not found');
		next(error);
	});

	app.get('/test/conflict', (req: Request, res: Response, next: NextFunction) => {
		const error = new ConflictError('User already exists', ErrorCodes.USER_ALREADY_EXISTS);
		next(error);
	});

	app.get('/test/generic-error', (req: Request, res: Response, next: NextFunction) => {
		const error = new Error('Unexpected error occurred');
		next(error);
	});

	app.get('/test/custom-status', (req: Request, res: Response, next: NextFunction) => {
		const error = { statusCode: 503, message: 'Service temporarily unavailable' };
		next(error);
	});

	app.get('/test/zod-error', (req: Request, res: Response, next: NextFunction) => {
		const zodError = {
			name: 'ZodError',
			format: () => ({
				email: { _errors: ['Invalid email format'] },
				password: { _errors: ['Password too short'] }
			})
		};
		next(zodError);
	});

	app.get('/test/cors-error', (req: Request, res: Response, next: NextFunction) => {
		const corsError = new Error('Not allowed by CORS');
		next(corsError);
	});

	app.get('/test/internal-server-error', (req: Request, res: Response, next: NextFunction) => {
		const error = new BaseError(
			'Database connection failed',
			HttpStatus.INTERNAL_SERVER_ERROR,
			ErrorCodes.INTERNAL_SERVER_ERROR
		);
		next(error);
	});

	app.get('/test/sensitive-data', (req: Request, res: Response, next: NextFunction) => {
		const error = new ValidationError(
			'Authentication failed',
			{
				password: 'secret123',
				token: 'jwt.token.here',
				email: '<EMAIL>',
				username: 'testuser'
			}
		);
		next(error);
	});

	// Apply GlobalErrorHandler as the last middleware
	app.use(GlobalErrorHandler);

	return app;
};

describe('GlobalErrorHandler Integration Tests', () => {
	let app: express.Application;

	beforeAll(() => {
		app = createTestApp();
		// Set test environment
		process.env.ENV = 'test';
	});

	afterAll(() => {
		// Clean up environment
		delete process.env.ENV;
	});

	describe('BaseError Handling', () => {
		test('should handle UnauthorizedError correctly', async () => {
			const response = await request(app)
				.get('/test/unauthorized')
				.expect(401);

			expect(response.body).toEqual({
				success: false,
				message: 'Invalid credentials',
				error: {
					code: 'INVALID_CREDENTIALS',
					message: 'Invalid credentials'
				}
			});
		});

		test('should handle ValidationError with details', async () => {
			const response = await request(app)
				.get('/test/validation')
				.expect(400);

			expect(response.body).toEqual({
				success: false,
				message: 'Invalid input data',
				error: {
					code: 'VALIDATION_ERROR',
					message: 'Invalid input data',
					details: {
						field: 'email',
						reason: 'Invalid format'
					}
				}
			});
		});

		test('should handle NotFoundError correctly', async () => {
			const response = await request(app)
				.get('/test/not-found')
				.expect(404);

			expect(response.body).toEqual({
				success: false,
				message: 'User not found',
				error: {
					code: 'NOT_FOUND',
					message: 'User not found'
				}
			});
		});

		test('should handle ConflictError correctly', async () => {
			const response = await request(app)
				.get('/test/conflict')
				.expect(409);

			expect(response.body).toEqual({
				success: false,
				message: 'User already exists',
				error: {
					code: 'USER_ALREADY_EXISTS',
					message: 'User already exists'
				}
			});
		});

		test('should handle InternalServerError correctly', async () => {
			const response = await request(app)
				.get('/test/internal-server-error')
				.expect(500);

			expect(response.body).toEqual({
				success: false,
				message: 'An internal server error occurred. Please try again later.',
				error: {
					code: 'INTERNAL_SERVER_ERROR',
					message: 'An internal server error occurred.',
					details: '[REDACTED]'
				}
			});
		});
	});

	describe('Generic Error Handling', () => {
		test('should handle generic JavaScript errors', async () => {
			const response = await request(app)
				.get('/test/generic-error')
				.expect(500);

			expect(response.body.success).toBe(false);
			expect(response.body.message).toBe('An unexpected error occurred on the server.');
			expect(response.body.error.code).toBe('UNKNOWN_ERROR');
		});

		test('should handle errors with custom status codes', async () => {
			const response = await request(app)
				.get('/test/custom-status')
				.expect(503);

			expect(response.body.success).toBe(false);
			expect(response.body.message).toBe('An unexpected error occurred on the server.');
		});
	});

	describe('Special Error Types', () => {
		test('should handle Zod validation errors', async () => {
			const response = await request(app)
				.get('/test/zod-error')
				.expect(400);

			expect(response.body.success).toBe(false);
			expect(response.body.error.code).toBe('VALIDATION_ERROR');
		});

		test('should handle CORS errors', async () => {
			const response = await request(app)
				.get('/test/cors-error')
				.expect(403);

			expect(response.body.success).toBe(false);
			expect(response.body.error.code).toBe('CORS_NOT_ALLOWED');
		});
	});

	describe('Sensitive Data Redaction', () => {
		test('should redact sensitive information in test environment', async () => {
			// Set environment to production to test redaction
			process.env.ENV = 'production';
			process.env.LOG_SENSITIVE = 'false';

			const response = await request(app)
				.get('/test/sensitive-data')
				.expect(400);

			expect(response.body.success).toBe(false);
			expect(response.body.error.details.password).toBe('[REDACTED]');
			expect(response.body.error.details.token).toBe('[REDACTED]');
			expect(response.body.error.details.email).toBe('<EMAIL>'); // Not sensitive
			expect(response.body.error.details.username).toBe('testuser'); // Not sensitive

			// Reset environment
			process.env.ENV = 'test';
			delete process.env.LOG_SENSITIVE;
		});
	});

	describe('Response Format Consistency', () => {
		test('all error responses should have consistent structure', async () => {
			const endpoints = [
				'/test/unauthorized',
				'/test/validation',
				'/test/not-found',
				'/test/conflict',
				'/test/generic-error'
			];

			for (const endpoint of endpoints) {
				const response = await request(app).get(endpoint);
				
				// All responses should have this structure
				expect(response.body).toHaveProperty('success', false);
				expect(response.body).toHaveProperty('message');
				expect(response.body).toHaveProperty('error');
				expect(response.body.error).toHaveProperty('code');
				expect(response.body.error).toHaveProperty('message');
				
				// Ensure proper types
				expect(typeof response.body.success).toBe('boolean');
				expect(typeof response.body.message).toBe('string');
				expect(typeof response.body.error.code).toBe('string');
				expect(typeof response.body.error.message).toBe('string');
			}
		});
	});
});
