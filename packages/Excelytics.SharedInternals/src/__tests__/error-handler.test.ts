/**
 * Tests for the refactored GlobalErrorHandler and helper functions
 * Ensures backward compatibility and proper functionality
 */
import './setup'; // Import test environment setup
import { test, expect, describe, beforeEach, mock } from 'bun:test';
import type { Request, Response, NextFunction } from 'express';
import { GlobalErrorHandler } from '../middleware/error-handler.middleware';
import {
	getErrorLoggingConfig,
	classifyError,
	logError,
	redactSensitiveInformation
} from '../helpers/error-logging.helper';
import {
	processBaseErrorResponse
} from '../helpers/error-response.helper';
import { BaseError, UnauthorizedError, ValidationError, NotFoundError } from '../errors';
import { ErrorCodes, HttpStatus, Messages } from '../constants';

// Mock console methods to capture logs
const mockConsoleError = mock(() => {});
const mockConsoleLog = mock(() => {});
const mockConsoleWarn = mock(() => {});

beforeEach(() => {
	mockConsoleError.mockClear();
	mockConsoleLog.mockClear();
	mockConsoleWarn.mockClear();
	console.error = mockConsoleError;
	console.log = mockConsoleLog;
	console.warn = mockConsoleWarn;
});

describe('Error Logging Configuration', () => {
	beforeEach(() => {
		// Reset environment variables
		delete Bun.env.DEBUG_ERRORS;
		delete Bun.env.LOG_SENSITIVE;
	});

	test('should return correct config for development environment', () => {
		Bun.env.ENV = 'development';
		const config = getErrorLoggingConfig();

		expect(config.isVerbose).toBe(true);
		expect(config.isDebugMode).toBe(true);
		expect(config.shouldLogSensitiveInfo).toBe(false);
	});

	test('should return correct config for test environment', () => {
		Bun.env.ENV = 'test';
		const config = getErrorLoggingConfig();

		expect(config.isVerbose).toBe(true);
		expect(config.isDebugMode).toBe(true);
		expect(config.shouldLogSensitiveInfo).toBe(false);
	});

	test('should return correct config for production environment', () => {
		Bun.env.ENV = 'production';
		const config = getErrorLoggingConfig();

		expect(config.isVerbose).toBe(false);
		expect(config.isDebugMode).toBe(false);
		expect(config.shouldLogSensitiveInfo).toBe(false);
	});

	test('should enable sensitive logging when LOG_SENSITIVE=true in development', () => {
		Bun.env.ENV = 'development';
		Bun.env.LOG_SENSITIVE = 'true';
		const config = getErrorLoggingConfig();

		expect(config.shouldLogSensitiveInfo).toBe(true);
	});

	test('should enable verbose logging when DEBUG_ERRORS=true', () => {
		Bun.env.ENV = 'production';
		Bun.env.DEBUG_ERRORS = 'true';
		const config = getErrorLoggingConfig();

		expect(config.isVerbose).toBe(true);
	});
});

describe('Error Classification', () => {
	test('should correctly classify BaseError instances', () => {
		const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
		const classification = classifyError(error);
		
		expect(classification.isBaseError).toBe(true);
		expect(classification.isExpectedError).toBe(true);
		expect(classification.isZodError).toBe(false);
		expect(classification.isCorsError).toBe(false);
	});

	test('should correctly classify Zod errors', () => {
		const zodError = {
			name: 'ZodError',
			format: mock(() => {}),
			statusCode: 400
		};
		const classification = classifyError(zodError);
		
		expect(classification.isZodError).toBe(true);
		expect(classification.isExpectedError).toBe(true);
		expect(classification.isBaseError).toBe(false);
	});

	test('should correctly classify CORS errors', () => {
		const corsError = {
			message: 'Not allowed by CORS',
			statusCode: 403
		};
		const classification = classifyError(corsError);
		
		expect(classification.isCorsError).toBe(true);
		expect(classification.isExpectedError).toBe(true);
	});

	test('should correctly classify unexpected errors', () => {
		const error = new Error('Unexpected error');
		const classification = classifyError(error);
		
		expect(classification.isBaseError).toBe(false);
		expect(classification.isExpectedError).toBe(false);
		expect(classification.isZodError).toBe(false);
		expect(classification.isCorsError).toBe(false);
	});

	test('should handle 5xx errors as unexpected', () => {
		const error = { statusCode: 500, message: 'Internal error' };
		const classification = classifyError(error);
		
		expect(classification.isExpectedError).toBe(false);
	});
});

describe('Sensitive Information Redaction', () => {
	test('should redact password fields', () => {
		const details = {
			password: 'secret123',
			email: '<EMAIL>',
			userPassword: 'anothersecret'
		};
		
		const redacted = redactSensitiveInformation(details);
		
		expect(redacted.password).toBe('[REDACTED]');
		expect(redacted.userPassword).toBe('[REDACTED]');
		expect(redacted.email).toBe('<EMAIL>');
	});

	test('should redact token fields', () => {
		const details = {
			accessToken: 'jwt.token.here',
			refreshToken: 'refresh.token.here',
			bearerToken: 'bearer.token.here',
			username: 'testuser'
		};
		
		const redacted = redactSensitiveInformation(details);
		
		expect(redacted.accessToken).toBe('[REDACTED]');
		expect(redacted.refreshToken).toBe('[REDACTED]');
		expect(redacted.bearerToken).toBe('[REDACTED]');
		expect(redacted.username).toBe('testuser');
	});

	test('should handle non-object inputs safely', () => {
		expect(redactSensitiveInformation(null)).toBe(null);
		expect(redactSensitiveInformation(undefined)).toBe(undefined);
		expect(redactSensitiveInformation('string')).toBe('string');
		expect(redactSensitiveInformation(123)).toBe(123);
	});
});

describe('Error Response Processing', () => {
	test('should process BaseError correctly', () => {
		const error = new UnauthorizedError('Invalid credentials', ErrorCodes.INVALID_CREDENTIALS);
		const config = { isVerbose: true, isDebugMode: true, shouldLogSensitiveInfo: false };
		
		const result = processBaseErrorResponse(error, config);
		
		expect(result.statusCode).toBe(401);
		expect(result.responseBody.success).toBe(false);
		expect(result.responseBody.message).toBe('Invalid credentials');
		expect(result.responseBody.error.code).toBe('INVALID_CREDENTIALS');
	});

	test('should sanitize 500 errors in production', () => {
		Bun.env.ENV = 'production';
		const error = new BaseError(
			'Database connection failed',
			HttpStatus.INTERNAL_SERVER_ERROR,
			ErrorCodes.INTERNAL_SERVER_ERROR
		);
		const config = { isVerbose: false, isDebugMode: false, shouldLogSensitiveInfo: false };

		const result = processBaseErrorResponse(error, config);

		expect(result.responseBody.message).toBe(Messages.Generic.Errors.INTERNAL_SERVER);
		expect(result.responseBody.error.message).toBe('An internal server error occurred.');
	});
});

describe('Backward Compatibility', () => {
	let mockRequest: Partial<Request>;
	let mockResponse: Partial<Response>;
	let mockNext: NextFunction;
	let mockJson: ReturnType<typeof mock>;
	let mockStatus: ReturnType<typeof mock>;

	beforeEach(() => {
		mockJson = mock(() => {});
		mockStatus = mock(() => ({ json: mockJson }));
		
		mockRequest = {
			method: 'POST',
			originalUrl: '/api/v1/test'
		};
		
		mockResponse = {
			status: mockStatus,
			json: mockJson
		};
		
		mockNext = mock(() => {});
	});

	test('should handle UnauthorizedError like before', () => {
		const error = new UnauthorizedError('Token is invalid or expired', ErrorCodes.INVALID_TOKEN);
		
		GlobalErrorHandler(
			error,
			mockRequest as Request,
			mockResponse as Response,
			mockNext
		);
		
		expect(mockStatus).toHaveBeenCalledWith(401);
		expect(mockJson).toHaveBeenCalledWith({
			success: false,
			message: 'Token is invalid or expired',
			error: {
				code: 'INVALID_TOKEN',
				message: 'Token is invalid or expired'
			}
		});
	});

	test('should handle ValidationError like before', () => {
		const error = new ValidationError(Messages.Validation.Basic.VALIDATION_FAILED, ErrorCodes.VALIDATION_FAILED);

		GlobalErrorHandler(
			error,
			mockRequest as Request,
			mockResponse as Response,
			mockNext
		);

		// Verify the mock was called
		expect(mockStatus).toHaveBeenCalled();
		expect(mockJson).toHaveBeenCalled();

		// Check the status code
		expect(mockStatus).toHaveBeenCalledWith(400);

		// Check the response structure
		const jsonCall = mockJson.mock.calls[0][0];
		expect(jsonCall).toHaveProperty('success', false);
		expect(jsonCall).toHaveProperty('error');
		expect(jsonCall.error).toHaveProperty('code', 'VALIDATION_ERROR');
	});

	test('should handle NotFoundError like before', () => {
		const error = new NotFoundError(Messages.User.Errors.UserNotFound);

		GlobalErrorHandler(
			error,
			mockRequest as Request,
			mockResponse as Response,
			mockNext
		);

		expect(mockStatus).toHaveBeenCalledWith(404);
		expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
			success: false,
			message: expect.any(String),
			error: expect.objectContaining({
				code: 'NOT_FOUND',
				message: expect.any(String)
			})
		}));
	});

	test('should handle generic errors like before', () => {
		const error = new Error('Unexpected error');
		
		GlobalErrorHandler(
			error,
			mockRequest as Request,
			mockResponse as Response,
			mockNext
		);
		
		expect(mockStatus).toHaveBeenCalledWith(500);
		expect(mockJson).toHaveBeenCalledWith(
			expect.objectContaining({
				success: false,
				message: expect.any(String),
				error: expect.objectContaining({
					code: expect.any(String),
					message: expect.any(String)
				})
			})
		);
	});
});

describe('Logging Behavior', () => {
	let mockRequest: Partial<Request>;

	beforeEach(() => {
		mockRequest = {
			method: 'POST',
			originalUrl: '/api/v1/test'
		};
	});

	test('should log verbose info in development for expected errors', () => {
		process.env.ENV = 'development';
		const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
		const config = getErrorLoggingConfig();
		const classification = classifyError(error);

		logError(error, mockRequest as Request, config, classification);

		expect(mockConsoleError).toHaveBeenCalledWith('--- Global Error Handler Triggered ---');
		expect(mockConsoleError).toHaveBeenCalledWith('[Request Path]:', 'POST /api/v1/test');
		expect(mockConsoleError).toHaveBeenCalledWith('[Error Type]: BaseError');
		expect(mockConsoleError).toHaveBeenCalledWith('[Error Code]:', 'INVALID_TOKEN');
	});

	test('should not log expected errors in production when not verbose', () => {
		Bun.env.ENV = 'production';
		Bun.env.DEBUG_ERRORS = 'false'; // Ensure verbose is false
		const error = new UnauthorizedError('Test error', ErrorCodes.INVALID_TOKEN);
		const config = getErrorLoggingConfig();
		const classification = classifyError(error);

		// Clear previous calls
		mockConsoleError.mockClear();

		logError(error, mockRequest as Request, config, classification);

		// In production with verbose=false, expected errors should not be logged
		expect(mockConsoleError).not.toHaveBeenCalled();
	});

	test('should always log unexpected errors', () => {
		process.env.ENV = 'production';
		const error = new Error('Unexpected error');
		const config = getErrorLoggingConfig();
		const classification = classifyError(error);

		logError(error, mockRequest as Request, config, classification);

		expect(mockConsoleError).toHaveBeenCalledWith('--- Global Error Handler Triggered ---');
		expect(mockConsoleError).toHaveBeenCalledWith('[Error Type]:', 'Error');
	});
});

describe('Consumer Integration Tests', () => {
	let mockRequest: Partial<Request>;
	let mockResponse: Partial<Response>;
	let mockNext: NextFunction;
	let mockJson: ReturnType<typeof mock>;
	let mockStatus: ReturnType<typeof mock>;

	beforeEach(() => {
		mockJson = mock(() => {});
		mockStatus = mock(() => ({ json: mockJson }));

		mockRequest = {
			method: 'POST',
			originalUrl: '/api/v1/auth/login',
			headers: { 'content-type': 'application/json' }
		};

		mockResponse = {
			status: mockStatus,
			json: mockJson
		};

		mockNext = mock(() => {});
	});

	describe('Identity Service Compatibility', () => {
		test('should handle authentication errors correctly', () => {
			const error = new UnauthorizedError(Messages.Auth.Errors.InvalidCredentials, ErrorCodes.INVALID_CREDENTIALS);

			GlobalErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

			expect(mockStatus).toHaveBeenCalledWith(401);
			expect(mockJson).toHaveBeenCalledWith({
				success: false,
				message: Messages.Auth.Errors.InvalidCredentials,
				error: {
					code: 'INVALID_CREDENTIALS',
					message: Messages.Auth.Errors.InvalidCredentials
				}
			});
		});

		test('should handle token validation errors correctly', () => {
			const error = new UnauthorizedError(Messages.Auth.Errors.InvalidToken, ErrorCodes.INVALID_TOKEN);

			GlobalErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

			expect(mockStatus).toHaveBeenCalledWith(401);
			expect(mockJson).toHaveBeenCalledWith({
				success: false,
				message: Messages.Auth.Errors.InvalidToken,
				error: {
					code: 'INVALID_TOKEN',
					message: Messages.Auth.Errors.InvalidToken
				}
			});
		});

		test('should handle user not found errors correctly', () => {
			const error = new NotFoundError(Messages.User.Errors.UserNotFound);

			GlobalErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

			expect(mockStatus).toHaveBeenCalledWith(404);
			expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
				success: false,
				message: expect.any(String),
				error: expect.objectContaining({
					code: 'NOT_FOUND',
					message: expect.any(String)
				})
			}));
		});
	});

	describe('Calc Service Compatibility', () => {
		test('should handle calculation errors correctly', () => {
			const error = new BaseError(
				'Calculation failed',
				HttpStatus.BAD_REQUEST,
				ErrorCodes.INVALID_INPUT,
				{ field: 'formula', reason: 'Invalid syntax' }
			);

			GlobalErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

			expect(mockStatus).toHaveBeenCalledWith(400);
			expect(mockJson).toHaveBeenCalledWith({
				success: false,
				message: 'Calculation failed',
				error: {
					code: 'INVALID_INPUT',
					message: 'Calculation failed',
					details: { field: 'formula', reason: 'Invalid syntax' }
				}
			});
		});
	});

	describe('Finance Service Compatibility', () => {
		test('should handle file upload errors correctly', () => {
			const error = new ValidationError(
				Messages.Excel.Errors.INVALID_FILE_FORMAT,
				{ allowedFormats: ['.xlsx', '.csv'] }
			);

			GlobalErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

			expect(mockStatus).toHaveBeenCalledWith(400);
			expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
				success: false,
				message: expect.any(String),
				error: expect.objectContaining({
					code: 'VALIDATION_ERROR',
					message: expect.any(String),
					details: expect.any(Object)
				})
			}));
		});
	});
});
