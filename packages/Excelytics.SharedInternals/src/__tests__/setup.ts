/**
 * Test environment setup
 * Sets up environment variables and mocks for testing
 */

// Try to load test environment file if it exists
try {
  const fs = require('fs');
  const path = require('path');
  const envTestPath = path.join(__dirname, '../../.env.test');

  if (fs.existsSync(envTestPath)) {
    const envContent = fs.readFileSync(envTestPath, 'utf8');
    envContent.split('\n').forEach((line: string) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, value] = trimmedLine.split('=');
        if (key && value) {
          process.env[key.trim()] = value.trim();
        }
      }
    });
  }
} catch (error) {
  // Fallback to manual setup if .env.test loading fails
  console.warn('Could not load .env.test file, using fallback configuration');
}

// Set up test environment variables (fallback or override)
process.env.ENV = process.env.ENV || 'test';
process.env.NODE_ENV = process.env.NODE_ENV || 'test';
process.env.SERVICE_NAME = process.env.SERVICE_NAME || 'test-service';
process.env.PORT = process.env.PORT || '3000';
process.env.MONGO_USER = process.env.MONGO_USER || 'test-user';
process.env.MONGO_PASS = process.env.MONGO_PASS || 'test-pass';
process.env.MONGO_HOST = process.env.MONGO_HOST || 'localhost';
process.env.MONGO_DB_NAME = process.env.MONGO_DB_NAME || 'test-db';
process.env.REDIS_HOST = process.env.REDIS_HOST || 'localhost';

// Ensure Bun.env is also set for compatibility
if (typeof Bun !== 'undefined' && Bun.env) {
  Object.keys(process.env).forEach((key: string) => {
    if (process.env[key] !== undefined) {
      Bun.env[key] = process.env[key] as string;
    }
  });
}

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;

// Export for test cleanup
export const restoreConsole = () => {
	console.error = originalConsoleError;
	console.log = originalConsoleLog;
	console.warn = originalConsoleWarn;
};
