/**
 * Health check utility functions.
 * Provides methods for checking the health of various dependencies.
 * @module health-check.utils
 */
import { HealthStatus } from '../types';
import { type Mongoose } from 'mongoose';
import { MongooseStates } from '../constants';
import type { DependencyHealth, HealthCheckFunction } from '../types';

/**
 * @function createMongooseHealthCheck
 * @description Creates a health check function specifically for a Mongoose database connection
 * This function can be passed into the HealthHelper's `checks` array
 * @param {string} [dependencyName='MongoDB'] - An optional name for the dependency
 * @param {Mongoose} mongooseInstance - The Mongoose instance to check against
 * @returns { HealthCheckFunction } A function that performs the health check when called
 */
export const createMongooseHealthCheck = (
	mongooseInstance: Mongoose,
	dependencyName: string = 'MongoDB'
): HealthCheckFunction => {

	/** Return the actual check function that HealthHelper will execute. */
	return async (): Promise<DependencyHealth> => {
		const dbHealth: DependencyHealth = {
			name: dependencyName,
			status: HealthStatus.UNKNOWN
		};

		const startTime = process.hrtime();

		try {
			// Check the passed‐in instance’s connection state
			if (mongooseInstance.connection.readyState !== MongooseStates.CONNECTED) {
				console.error(`Mongoose connection is not established. Current state: ${mongooseInstance.connection.readyState}`);

				dbHealth.status = HealthStatus.UNHEALTHY
				dbHealth.error = "Mongoose connection is not established."
				return dbHealth
			}

			// The 'ping' command is a lightweight way to check if the DB is responsive.
			// Does a ping against the underlying native driver.
			if (mongooseInstance.connection.db) {
				await mongooseInstance.connection.db.admin().ping();
				dbHealth.status = HealthStatus.HEALTHY;
			}

		} catch (error: any) {
			dbHealth.status = HealthStatus.UNHEALTHY;
			dbHealth.error = error.message;
			console.error(`[HealthCheck] ${dependencyName} health check failed:`, error.message);
		}

		const diff = process.hrtime(startTime);
		dbHealth.responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return dbHealth;
	};
};

/**
 * @function createRedisHealthCheck
 * @description Example of another dependency check for Redis
 * @param {any} redisClient - An instance of a Redis client (e.g., from ioredis)
 * @param {string} [dependencyName='Redis'] - An optional name for the dependency
 * @returns { HealthCheckFunction }
 */
export const createRedisHealthCheck = (
	redisClient: any,
	dependencyName: string = 'Redis'
): HealthCheckFunction => {
	return async (): Promise<DependencyHealth> => {
		const redisHealth: DependencyHealth = { name: dependencyName, status: HealthStatus.UNKNOWN };
		const startTime = process.hrtime();

		try {
			const reply = await redisClient.ping();
			if (reply !== 'PONG') {
				console.error('Received unexpected reply from Redis PING command.');

				redisHealth.status = HealthStatus.UNHEALTHY;
				redisHealth.error = 'Received unexpected reply from Redis PING command.';
				return redisHealth;
			}

			redisHealth.status = HealthStatus.HEALTHY;
		} catch (error: any) {
			redisHealth.status = HealthStatus.UNHEALTHY;
			redisHealth.error = error.message;
		}

		const diff = process.hrtime(startTime);
		redisHealth.responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return redisHealth;
	};
};