/**
 * Iteration 1: with Claude 4 Sonnet (01.06.2025)
 * Iteration 2: Refactored via Gemini 2.5 Pro (06.06.2025)
 * @module service-client
 */
import { env_ } from './env_internal';
import axios, { type AxiosInstance } from 'axios';
import { type HttpStatusCode, ServiceError } from '../errors';
import type { ApiResponse, ServiceClientOptions } from '../types';
import { ErrorCodes, HttpStatus, ServiceNames } from '../constants';

/**
 * @class ServiceClient
 * @description A standardized HTTP client for service-to-service communication.
 */
export class ServiceClient {
	private readonly serviceName: string;
	private axiosInstance: AxiosInstance;

	constructor(baseURL: string | undefined, serviceName: string, defaultTimeout: number = 10000) {
		if (!baseURL) {
			// Log a warning but don't throw, so services that don't need this client can still start.
			console.warn(`Base URL for service "${serviceName}" is not configured. Client will be non-functional.`);
		}

		this.serviceName = serviceName;
		this.axiosInstance = axios.create({
			baseURL: baseURL,
			timeout: defaultTimeout,
			headers: {
				'Content-Type': 'application/json',
				'User-Agent': `Introspection-Internal-Client/1.0 (${env_.SERVICE_NAME})`
			}
		});
	}

	/**
     * Extracts and prepares headers for forwarding to another service.
     * @param originalRequest The original Express request.
     * @returns A record of headers to be forwarded.
     */
	private getForwardingHeaders(originalRequest?: Request): Record<string, string> {
		const headers: Record<string, string> = {};
		if (originalRequest) {
			const requestId = originalRequest.headers.get('x-request-id');
			if (requestId) {
				// Propagate request ID for tracing
				headers['x-request-id'] = requestId;
			}

			// You could also propagate the original user's Authorization header if needed, but this requires careful security consideration.
			// const authHeader = originalRequest.headers.authorization;
			// if (authHeader) {
			//   headers['Authorization'] = authHeader;
			// }
		}

		return headers;
	}

	/**
     * Handles errors from Axios requests and throws a ServiceError.
     * @param error The error caught from an Axios request.
     * @throws {ServiceError} A standardized error for service-to-service communication failures.
     */
	private handleError(error: any): never {
		if (axios.isAxiosError(error)) {
			const message = `Error calling ${this.serviceName} service: ${error.response?.data?.message || error.message}`;
			const statusCode = (error.response?.status as HttpStatusCode) || HttpStatus.SERVICE_UNAVAILABLE;
			const errorCode = error.response?.data?.error?.code || ErrorCodes.EXTERNAL_SERVICE_ERROR;
			const details = error.response?.data;

			throw new ServiceError(message, statusCode, errorCode, details, error);
		}

		// For non-axios errors (e.g., programming errors in the client itself)
		throw new ServiceError(
			`Unexpected error in ServiceClient for ${this.serviceName}: ${error.message}`,
			HttpStatus.INTERNAL_SERVER_ERROR,
			ErrorCodes.UNKNOWN_ERROR,
			undefined,
			error
		);
	}

	/**
     * Performs a GET request to the service.
     * @param endpoint The endpoint to call.
     * @param options Optional request configuration.
     * @returns The response data from the service.
     * @throws {ServiceError} If the request fails.
     */
	public async get<T>(endpoint: string, options?: ServiceClientOptions): Promise<ApiResponse<T>> {
		try {
			const forwardingHeaders = this.getForwardingHeaders(options?.originalRequest);
			const response = await this.axiosInstance.get<ApiResponse<T>>(endpoint, {
				headers: { ...forwardingHeaders, ...options?.headers },
				timeout: options?.timeout
			});

			return response.data;
		} catch (error) {
			this.handleError(error);
		}
	}

	/**
     * Performs a POST request to the service.
     * @param endpoint The endpoint to call.
     * @param data The data to send in the request body.
     * @param options Optional request configuration.
     * @returns The response data from the service.
     * @throws {ServiceError} If the request fails.
     */
	public async post<T>(endpoint: string, data: any, options?: ServiceClientOptions): Promise<ApiResponse<T>> {
		try {
			const forwardingHeaders = this.getForwardingHeaders(options?.originalRequest);
			const response = await this.axiosInstance.post<ApiResponse<T>>(endpoint, data, {
				headers: { ...forwardingHeaders, ...options?.headers },
				timeout: options?.timeout
			});

			return response.data;
		} catch (error) {
			this.handleError(error);
		}
	}

	/**
     * Performs a PUT request to the service.
     * @param endpoint The endpoint to call.
     * @param data The data to send in the request body.
     * @param options Optional request configuration.
     * @returns The response data from the service.
     * @throws {ServiceError} If the request fails.
     */
	public async put<T>(endpoint: string, data: any, options?: ServiceClientOptions): Promise<ApiResponse<T>> {
		try {
			const forwardingHeaders = this.getForwardingHeaders(options?.originalRequest);
			const response = await this.axiosInstance.put<ApiResponse<T>>(endpoint, data, {
				headers: { ...forwardingHeaders, ...options?.headers },
				timeout: options?.timeout
			});

			return response.data;
		} catch (error) {
			this.handleError(error);
		}
	}

	/**
     * Performs a DELETE request to the service.
     * @param endpoint The endpoint to call.
     * @param options Optional request configuration.
     * @returns The response data from the service.
     * @throws {ServiceError} If the request fails.
     */
	public async delete<T>(endpoint: string, options?: ServiceClientOptions): Promise<ApiResponse<T>> {
		try {
			const forwardingHeaders = this.getForwardingHeaders(options?.originalRequest);
			const response = await this.axiosInstance.delete<ApiResponse<T>>(endpoint, {
				headers: { ...forwardingHeaders, ...options?.headers },
				timeout: options?.timeout
			});

			return response.data;
		} catch (error) {
			this.handleError(error);
		}
	}
}

// --- Pre-instantiated Clients for Easy Use ---
// These clients can be imported and used directly in any service.
// The service will only work if the corresponding environment variable is set.

/** Client for the Calculation service */
export const calcClient = new ServiceClient(env_.CALC_SERVICE_URL, ServiceNames.CALC);
/** Client for the Identity Provider service */
export const idpClient = new ServiceClient(env_.IDP_SERVICE_URL, ServiceNames.IDENTITY);
/** Client for the Finance service */
export const financeClient = new ServiceClient(env_.FINANCE_SERVICE_URL, ServiceNames.FINANCE);