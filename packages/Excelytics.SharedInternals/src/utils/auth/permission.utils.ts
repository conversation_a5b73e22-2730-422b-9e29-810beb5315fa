/**
 * Permission utility functions.
 * Provides methods for working with the permission system.
 * @module permission.utils
 */
import { PermissionGroups } from '../../constants';

/**
 * Utility class for permission-related operations.
 * Handles permission checking, extraction, and role management.
 */
export class PermissionUtils {
	/**
     * Checks if a permission array contains a specific permission.
     *
     * @param userPermissions - Array of permission strings the user has
     * @param requiredPermission - The permission string to check for
     * @returns True if the user has the required permission
     */
	static hasPermission(userPermissions: string[], requiredPermission: string): boolean {
		return userPermissions.includes(requiredPermission);
	}

	/**
     * Checks if a permission array contains any of the specified permissions.
     *
     * @param userPermissions - Array of permission strings the user has
     * @param requiredPermissions - Array of permission strings to check for
     * @returns True if the user has at least one of the required permissions
     */
	static hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
		return requiredPermissions.some(permission => userPermissions.includes(permission));
	}

	/**
     * Checks if a permission array contains all the specified permissions.
     *
     * @param userPermissions - Array of permission strings the user has
     * @param requiredPermissions - Array of permission strings to check for
     * @returns True if the user has all of the required permissions
     */
	static hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
		return requiredPermissions.every(permission => userPermissions.includes(permission));
	}

	/**
     * Gets all permissions for a specific role.
     *
     * @param role - The role to get permissions for
     * @returns Array of permission strings for the specified role
     */
	static getPermissionsForRole(role: keyof typeof PermissionGroups): string[] {
		return [...PermissionGroups[role]];
	}

	/**
     * Checks if a permission is a self-scoped permission.
     * Self-scoped permissions apply only to the user's own resources.
     *
     * @param permission - The permission string to check
     * @returns True if the permission is self-scoped
     */
	static isSelfPermission(permission: string): boolean {
		return permission.startsWith('self:');
	}

	/**
     * Checks if a permission is scoped to a specific service.
     *
     * @param permission - The permission string to check
     * @param service - The service name to check for
     * @returns True if the permission is scoped to the specified service
     */
	static isServicePermission(permission: string, service: string): boolean {
		return permission.startsWith(`${service}:`);
	}

	/**
     * Extracts the action part from a permission string.
     * For format "scope:action:resource", returns "action".
     * For format "action:resource", returns "action".
     *
     * @param permission - The permission string to parse
     * @returns The action part of the permission
     */
	static extractAction(permission: string): string {
		const parts = permission.split(':');
		return parts.length === 3 ? parts[1] : parts[0];
	}

	/**
     * Extracts the resource part from a permission string.
     * For any format, returns the last part after the colon.
     *
     * @param permission - The permission string to parse
     * @returns The resource part of the permission
     */
	static extractResource(permission: string): string {
		const parts = permission.split(':');
		return parts[parts.length - 1];
	}

	/**
     * Extracts the scope part from a permission string.
     * For format "scope:action:resource", returns "scope".
     * For format "action:resource", returns "global".
     *
     * @param permission - The permission string to parse
     * @returns The scope part of the permission or "global" if not specified
     */
	static extractScope(permission: string): string {
		const parts = permission.split(':');
		return parts.length === 3 ? parts[0] : 'global';
	}
}