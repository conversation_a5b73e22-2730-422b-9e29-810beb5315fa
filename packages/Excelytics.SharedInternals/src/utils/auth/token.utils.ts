/**
 * Token utility functions.
 * Provides methods for working with tokens, such as checking expiration and permissions.
 * @module token.utils
 */
import { EnumTokenType } from '../../enums';
import type {
	UserRole,
	TokenPayload,
	AccessTokenPayload,
	RefreshTokenPayload
} from '../../types';

/**
 * @function isAccessToken
 * @description Type guard to check if a token payload is an access token.
 */
export const isAccessToken = (token: TokenPayload): token is AccessTokenPayload => {
	return token.tokenType === EnumTokenType.ACCESS;
};

/**
 * @function isRefreshToken
 * @description Type guard to check if a token payload is a refresh token.
 * @token {TokenPayload} is a union type of AccessTokenPayload and RefreshTokenPayload.
 */
export const isRefreshToken = (token: TokenPayload): token is RefreshTokenPayload => {
	return token.tokenType === EnumTokenType.REFRESH;
};

/**
 * @function hasPermission
 * @description Checks if a token payload contains a specific permission/role.
 * This assumes permissions are stored in the 'permissions' array of an AccessTokenPayload.
 */
export const hasPermission = (
	token: TokenPayload | undefined,
	requiredPermission: UserRole | string
): boolean => {
	if (!token || !isAccessToken(token) || !token.permissions) {
		return false;
	}

	return token.permissions.includes(requiredPermission);
};

/**
 * @function isTokenExpired
 * @description Checks if a token payload is expired based on its 'expiresAt' claim.
 */
export const isTokenExpired = (token: TokenPayload): boolean => {
	if (!token.expiresAt) {
		// If there's no expiration, treat it as non-expiring
		console.warn('Token has no expiration date. Treating as non-expiring.');
		return false;
	}

	// Compare expiration date with the current time
	return new Date() > token.expiresAt;
};

/**
 * @function getSecondsUntilExpiry
 * @description Calculates the number of seconds remaining until the token expires.
 * @returns {number} The number of seconds until expiry. Returns 0 if expired or no expiry is set.
 */
export const getSecondsUntilExpiry = (token: TokenPayload): number => {
	if (!token.expiresAt) {
		return 0;
	}

	const now = new Date();
	const expiry = token.expiresAt;
	if (now >= expiry) {
		return 0;
	}

	return Math.floor((expiry.getTime() - now.getTime()) / 1000);
};