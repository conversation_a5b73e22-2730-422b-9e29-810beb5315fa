/**
 * @module environments
 * @description
 * This module provides Zod schemas for validating environment variables across all Introspection Consulting microservices.
 *
 * The architecture uses a `BaseEnvironmentSchema` for common configuration and service-specific schemas (e.g., `IdpEnvironmentSchema`) that extend the base.
 * USAGE: In each microservice, import the specific schema you need and parse the environment at the start of your application.
 */
import { z } from 'zod';
import { EnumLogLevel, EnumApiVersion, EnumEnv } from '../enums';

// --- 1. Base Schema ---
/**
 * Zod schema for validating environment variables.
 * Provides default values and type coercion for configuration settings.
 */
export const BaseFieldsSchema = z.object({
	// --- Service Identity ---
	/** The current application environment (e.g., 'development', 'production'). */
	ENV: z
		.preprocess(
			() => Bun.env.NODE_ENV || Bun.env.ENV,
			z.nativeEnum(EnumEnv),
		)
		.default(EnumEnv.Development),
	/** The unique name of the current service (e.g., 'excelytics.finance'). */
	SERVICE_NAME: z.string(),
	/** The semantic version of the service. */
	VERSION: z.string().default('1.0.0'),
	/** The API version the service exposes. */
	API_VERSION: z.nativeEnum(EnumApiVersion).default(EnumApiVersion.V1),

	// --- Network & Logging ---
	/** The port number for the service to listen on. No default to prevent conflicts. */
	PORT: z.coerce.number().int().positive(),
	/** The level of detail for application logs. */
	LOG_LEVEL: z.nativeEnum(EnumLogLevel).default(EnumLogLevel.Info),
	/** Flag to indicate if the service is running in a VPN-enabled environment. */
	VPN: z.preprocess(
		(val) => String(val).toLowerCase() === 'true',
		z.boolean(),
	).default(false),

	// --- Database & Redis (Raw Components) ---
	// We define components and build the final URI in the transform step.
	/** The username for the MongoDB connection. */
	MONGO_USER: z.string(),
	/** The password for the MongoDB connection. */
	MONGO_PASS: z.string(),
	/** The host for the MongoDB connection. */
	MONGO_HOST: z.string(),
	/** The port for the MongoDB connection. */
	MONGO_PORT: z.coerce.number().default(27017),
	/** The database name for the MongoDB connection. */
	MONGO_DB_NAME: z.string(),
	/** The authentication source for the MongoDB connection. */
	MONGO_AUTH_SOURCE: z.string().optional(),

	/** Redis server IP address on Local */
	REDIS_URI: z.string().ip({ version: 'v4' }).default('********').optional(),
	/** The Redis logical DB index (0–15 by default). */
	REDIS_DB: z.coerce.number().int().min(0).default(0),
	/** The host for the Redis connection. */
	REDIS_HOST: z.string(),
	/** The port for the Redis connection. */
	REDIS_PORT: z.coerce.number().default(6379),
	/** The password for the Redis connection. */
	REDIS_PASSWORD: z.string().optional(),

	// --- Unraid & Tailscale Network, IP Addresses & DB URIs ---
	/** Tailscale IP address for private network communication */
	TAILSCALE_IP: z.string().ip({ version: 'v4' }).default('********').optional(),
	/** Redis server IP address on Unraid */
	REDIS_URI_UNRAID: z.string().ip({ version: 'v4' }).default('********').optional(),
	/** MongoDB URI on Unraid */
	MONGODB_URI_UNRAID: z.string().optional(),
	/** MongoDB URI on Local */
	MONGODB_URI_LOCAL: z.string().optional(),

	// MICROSERVICE URLs
	/** URL for the Identity Provider service */
	IDP_SERVICE_URL: z.string().url().default('http://localhost:6002'),
	/** URL for the Calculation service */
	CALC_SERVICE_URL: z.string().url().default('http://localhost:6003'),
	/** URL for the Client service */
	CLIENT_SERVICE_URL: z.string().url().default('http://localhost:4200'),
	/** URL for the Finance service */
	FINANCE_SERVICE_URL: z.string().url().default('http://localhost:6001'),

	// Gateway Configuration
	/** Interval for health checks in milliseconds */
	HEALTH_CHECK_INTERVAL: z.coerce.number().int().positive().default(30000).optional(),
	/** Timeout for requests in milliseconds */
	REQUEST_TIMEOUT: z.coerce.number().int().positive().default(5000).optional(),
	/** Maximum number of retries for failed requests */
	MAX_RETRIES: z.coerce.number().int().positive().default(3).optional(),

	/** Flag to skip authentication for testing purposes */
	SKIP_AUTHENTICATION: z
		.preprocess(val => String(val).toLowerCase() === "true", z.boolean())
		.default(false)
});

// --- 2. Reusable Transformation ---
/**
 * This function will be applied to each final schema.
 * Removed '.infer<…BaseFieldsSchema>' so TS can still view properties added to extended schemas that use this transform.
 */
const InternalMongoUriTransform = <
	T extends Record<string, unknown> & z.infer<typeof BaseFieldsSchema>>
	(
		data: T
	): T & { MONGODB_URI: string } => {
	const user = encodeURIComponent(data.MONGO_USER);
	const pass = encodeURIComponent(data.MONGO_PASS);
	const authSource = data.MONGO_AUTH_SOURCE
		? `?authSource=${data.MONGO_AUTH_SOURCE}`
		: "";
	return {
		...data,
		MONGODB_URI: `mongodb://${user}:${pass}@${data.MONGO_HOST}:${data.MONGO_PORT}/${data.MONGO_DB_NAME}${authSource}`,
	};
};

// --- 3. Service-Specific Schemas ---
/**
 * Environment schema for the Identity Provider (IdP) service.
 * Extends the base schema and then applies the transformation.
 * Parse this when consumed.
 */
export const IdpEnvironmentSchema = BaseFieldsSchema.extend({
	// --- IdP Secrets (NO DEFAULTS - app will crash if missing) ---
	JWT_SECRET: z
		.string()
		.min(32, 'JWT_SECRET must be at least 32 characters'),
	COOKIE_SECRET: z
		.string()
		.min(32, 'COOKIE_SECRET must be at least 32 characters'),
	SESSION_SECRET: z
		.string()
		.min(32, 'SESSION_SECRET must be at least 32 characters'),
	REFRESH_TOKEN_SECRET: z
		.string()
		.min(32, 'REFRESH_TOKEN_SECRET must be at least 32 characters'),

	/** Secret for encrypting session data in the database. Highly recommended. */
	SESSION_STORE_CRYPTO_SECRET: z
		.string()
		.min(32, 'SESSION_STORE_CRYPTO_SECRET must be at least 32 characters')
		.optional(),

	// --- IdP URLs & Domains ---
	IDP_BASE_URL: z.string().url().optional(),
	CLIENT_APP_LOGOUT_SUCCESS_REDIRECT_URI: z.string().url().optional(),
	CLIENT_APP_LOGIN_SUCCESS_REDIRECT_URI: z.string().url().optional(),

	/** The domain for session cookies. e.g., ".excelytics.co.za" */
	COOKIE_DOMAIN: z.string().startsWith(".").optional(),

	// --- Expiration & Thresholds ---
	/** Note: We use _MS suffix for clarity that these are numbers (milliseconds) */
	SESSION_MAX_AGE_MS: z.coerce.number().default(1000 * 60 * 60), // 3600000 = 1 hour
	SLOW_REQUEST_THRESHOLD_MS: z.coerce.number().default(5000),    // 5000    = 5 seconds
	ACCESS_TOKEN_EXPIRY: z.string().default("30m"),
	REFRESH_TOKEN_EXPIRY: z.string().default("7d"),

	// --- Rate Limiting ---
	RATE_LIMIT_WINDOW_MS: z.coerce.number().default(1000 * 60 * 15), // 900000 = 15 mins
	RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(100),

	// --- Session Store (MongoStore) ---
	SESSION_STORE_COLLECTION_NAME: z.string().default("sessions_idp"),
	SESSION_STORE_TTL_SECONDS: z.coerce.number().default(14 * 24 * 60 * 60), // 1209600 = 14 days
	SESSION_STORE_TOUCH_AFTER_SECONDS: z.coerce.number().default(24 * 3600)  // 86400   = 24 hours
}).transform(InternalMongoUriTransform);

/** Environment schema for the Finance service, parse this when consumed */
export const FinanceEnvironmentSchema = BaseFieldsSchema.extend({
	// --- File Upload Configuration ---
	MAX_FILE_SIZE: z.string().default("50MB"),
	ALLOWED_FILE_TYPES: z.string().default("xlsx,xls,csv"),

	// --- Expiration & Thresholds ---
	/** Note: We use _MS suffix for clarity that these are numbers (milliseconds) */
	SESSION_MAX_AGE_MS: z.coerce.number().default(1000 * 60 * 60),	// 3600000 = 1 hour
	SLOW_REQUEST_THRESHOLD_MS: z.coerce.number().default(5000) 	// 5000    = 5 seconds
}).transform(InternalMongoUriTransform);

/** Environment schema for the Calculation engine, only extends base with transformed MongoDB URI */
export const CalcEnvironmentSchema = BaseFieldsSchema.transform(InternalMongoUriTransform);