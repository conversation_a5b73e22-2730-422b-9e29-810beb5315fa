/**
 * Date utility functions.
 * Provides methods for date manipulation, formatting, and comparison.
 * @module date.utils
 */

/**
 * Utility class for date-related operations.
 * Handles date formatting, parsing, and manipulation.
 */
export class DateUtility {
	/**
     * Gets today's date as a Date object set to midnight.
     * Default Date Object format: YYYY-MM-DD
     *
     * @returns A Date object representing today at 00:00:00
     */
	public getTodayAsDateObject = (): Date => {
		const today = new Date();
		// Ensure date format is set to midnight (00:00:00)
		return new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
	};

	/**
     * Gets today's date as a formatted string.
     * Format: DD/MM/YYYY
     *
     * @returns Today's date as a string in DD/MM/YYYY format
     */
	public getTodayAsString = (): string => {
		const today = new Date();
		const dd = String(today.getDate()).padStart(2, '0');
		const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is month 0!
		const yyyy = today.getFullYear();
		return `${dd}/${mm}/${yyyy}`;
	};

	/**
     * Converts a date string to a Date object.
     * Default format expected: DD/MM/YYYY
     *
     * @param dateString - The date string to convert
     * @param delimiter - The character separating date parts (default: '/')
     * @returns A Date object representing the input string at midnight
     */
	public getDateFromString = (dateString: string, delimiter: string = '/'): Date => {
		const [dd, mm, yyyy] = dateString.split(delimiter);
		return new Date(
			Number(yyyy),
			Number(mm) - 1, // January is month 0!
			Number(dd),
			0, 0, 0, 0 // Set time to midnight
		);
	};

	/**
     * Formats a Date object to a string according to the specified format.
     *
     * @param date - The Date object to format
     * @param format - The format to use (e.g., 'readable' for human-friendly format)
     * @returns The formatted date string
     */
	public formatDate = (date: Date, format: string): string => {
		if (format === 'readable') {
			return new Intl.DateTimeFormat('en-US', {
				year: 'numeric',
				month: 'long',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit'
			}).format(date);
		}
		return date.toISOString();
	};

	/**
     * Checks if a date is in the past (expired).
     *
     * @param expiryDate - The date to check against the current time
     * @returns True if the date is in the past
     */
	public isExpired = (expiryDate: Date): boolean => {
		return new Date() > expiryDate;
	};

	/**
     * Adds a specified number of days to a date.
     *
     * @param date - The base Date object
     * @param days - The number of days to add
     * @returns A new Date object with days added
     */
	public addDays = (date: Date, days: number): Date => {
		return this.addHours(date, days * 24);
	};

	/**
     * Adds a specified number of hours to a date.
     *
     * @param date - The base Date object
     * @param hours - The number of hours to add
     * @returns A new Date object with hours added
     */
	public addHours = (date: Date, hours: number): Date => {
		return this.addMinutes(date, hours * 60);
	};

	/**
     * Adds a specified number of minutes to a date.
     *
     * @param date - The base Date object
     * @param minutes - The number of minutes to add
     * @returns A new Date object with minutes added
     */
	public addMinutes = (date: Date, minutes: number): Date => {
		return new Date(date.getTime() + minutes * 60000);
	};
}

/**
 * Format date and time for South African timezone (SAST - UTC+2)
 * Returns format: (Mon) dd-mm-yyyy 10:22pm SAST
 */
function formatSouthAfricanDateTime(
	includeDay: boolean = true,
	date: Date = new Date()
): string {
	const options: Intl.DateTimeFormatOptions = {
		timeZone: 'Africa/Johannesburg',
		weekday: 'short',
		day: '2-digit',
		month: '2-digit',
		year: 'numeric',
		hour: '2-digit',
		minute: '2-digit',
		hour12: true
	};

	const formatter = new Intl.DateTimeFormat('en-ZA', options);
	const parts = formatter.formatToParts(date);

	const weekday = parts.find(part => part.type === 'weekday')?.value;
	const day = parts.find(part => part.type === 'day')?.value;
	const month = parts.find(part => part.type === 'month')?.value;
	const year = parts.find(part => part.type === 'year')?.value;
	const hour = parts.find(part => part.type === 'hour')?.value;
	const minute = parts.find(part => part.type === 'minute')?.value;
	const dayPeriod = parts.find(part => part.type === 'dayPeriod')?.value?.toLowerCase();

	return includeDay
		? `(${weekday}) ${day}-${month}-${year} ${hour}:${minute}${dayPeriod} SAST`
		: `${day}-${month}-${year} ${hour}:${minute}${dayPeriod} SAST`;
}

/**
 * Format date for South African timezone (SAST - UTC+2)
 * Returns format: dd-mm-yyyy
 */
function formatSouthAfricanDate(date: Date = new Date()): string {
	const options: Intl.DateTimeFormatOptions = {
		timeZone: 'Africa/Johannesburg',
		day: '2-digit',
		month: '2-digit',
		year: 'numeric'
	};

	const formatter = new Intl.DateTimeFormat('en-ZA', options);
	const parts = formatter.formatToParts(date);

	const day = parts.find(part => part.type === 'day')?.value;
	const month = parts.find(part => part.type === 'month')?.value;
	const year = parts.find(part => part.type === 'year')?.value;

	return `${day}-${month}-${year}`;
}

export const ZADateUtils = {
	formatSouthAfricanDateTime,
	formatSouthAfricanDate
};