/**
 * String utility functions.
 * Provides methods for string manipulation and validation.
 * @module string.utils
 */

/**
 * Validates an email address using a simple regex.
 * @param email - The email address to validate
 * @returns True if the email is valid
 */
export const isValidEmail = (email: string): boolean => {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
};

/**
 * Validates an ObjectId using a simple regex.
 * @param id - The ObjectId to validate
 * @returns True if the ObjectId is valid
 */
export const isValidObjectId = (id: string): boolean => {
	return /^[0-9a-fA-F]{24}$/.test(id);
};

/**
 * Sanitizes a string by removing leading/trailing whitespace and stripping HTML tags.
 * @param input - The string to sanitize
 * @returns The sanitized string
 */
export const sanitizeString = (input: string): string => {
	return input.trim().replace(/[<>]/g, '');
};