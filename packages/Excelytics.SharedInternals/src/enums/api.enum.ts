/**
 * @module api.enum
 */

/**
 * Enum representing different API versions.
 * Used for versioning API endpoints and ensuring backward compatibility.
 */
export enum EnumApiVersion {
    V0 = 'v0',
    V1 = 'v1',
    V2 = 'v2',
    V3 = 'v3'
}

/**
 * Enum representing different token types.
 * Used for token validation and management.
 */
export enum EnumTokenType {
    /** Refresh tokens are used to obtain new access tokens. */
    REFRESH = 'refresh',
    /** Access tokens are used for API authentication and authorization. */
    ACCESS = 'access'
}