/**
 * @module app.enum
 */

/**
 * Enum representing different application environments.
 * Used for configuring application behavior based on the environment.
 */
export enum EnumEnv {
    Development = 'development',
    Production = 'production',
    Staging = 'staging',
    UAT = 'uat',
    Local = 'local',
    Test = 'test',
    Base = 'base'
}

/**
 * Enum representing different log levels from typegoose log-level enum.
 * Used for configuring the verbosity of application logging.
 */
export enum EnumLogLevel {
    Silent = 'TRACE', // 0
    Debug = 'DEBUG',  // 1
    Info = 'INFO',    // 2
    Warn = 'WARN',    // 3
    Error = 'ERROR',  // 4
    Fatal = 'SILENT'  // 5
}