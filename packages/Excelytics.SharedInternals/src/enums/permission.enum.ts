/**
 * Permission system enums.
 * Defines the core permission model for the Excelytics platform.
 * @module permission.enum
 */

/**
 * Core permission actions that can be performed across the platform.
 * Represents the verbs in the permission system.
 */
export enum EnumPermissionAction {
    // Basic CRUD operations
    CREATE = 'create',
    READ = 'read',
    UPDATE = 'update',
    DELETE = 'delete',

    // File operations
    UPLOAD = 'upload',
    DOWNLOAD = 'download',
    PROCESS = 'process',

    // Calculation operations
    CALCULATE = 'calculate',
    EXPORT = 'export',

    // Administrative operations
    MANAGE = 'manage',
    ADMIN = 'admin',

    // Authentication operations
    LOGIN = 'login',
    LOGOUT = 'logout',
    REFRESH = 'refresh'
}

/**
 * Platform resources that permissions can be applied to.
 * Represents the nouns in the permission system.
 */
export enum EnumPermissionResource {
    // User management
    USERS = 'users',
    PROFILES = 'profiles',
    SESSIONS = 'sessions',

    // File management
    FILES = 'files',
    UPLOADS = 'uploads',
    TEMPLATES = 'templates',

    // Financial data
    REPORTS = 'reports',
    DASHBOARDS = 'dashboards',
    FINANCIAL_DATA = 'financial_data',

    // Calculations
    CHARTS = 'charts',
    ANALYTICS = 'analytics',
    CALCULATIONS = 'calculations',

    // System resources
    LOGS = 'logs',
    SYSTEM = 'system',
    SETTINGS = 'settings',

    // API access
    API = 'api'
}

/**
 * Service-specific permission scopes for microservices.
 * Defines the context in which permissions are applied.
 */
export enum EnumPermissionScope {
    // Global scope
    GLOBAL = 'global',

    // Service-specific scopes
    IDENTITY = 'identity',
    FINANCE = 'finance',
    CLIENT = 'client',
    CALC = 'calc',

    // User-specific scope
    SELF = 'self'
}

/**
 * Comprehensive permissions enum with formatted permission strings.
 * Format: {scope}:{action}:{resource} or {action}:{resource} for global permissions.
 * Provides a complete set of predefined permissions used throughout the system.
 */
export enum EnumPermissions {
    // === GLOBAL PERMISSIONS ===
    // System administration
    MANAGE_SETTINGS = 'manage:settings',
    ADMIN_SYSTEM = 'admin:system',
    READ_LOGS = 'read:logs',

    // === USER & IDENTITY PERMISSIONS ===
    // User management (admin level)
    CREATE_USERS = 'create:users',
    UPDATE_USERS = 'update:users',
    DELETE_USERS = 'delete:users',
    MANAGE_USERS = 'manage:users',
    READ_USERS = 'read:users',

    // Self-user operations
    READ_SELF_PROFILE = 'self:read:profiles',
    UPDATE_SELF_PROFILE = 'self:update:profiles',
    DELETE_SELF_PROFILE = 'self:delete:profiles',

    // Session management
    MANAGE_SESSIONS = 'manage:sessions',
    READ_SESSIONS = 'read:sessions',

    // Authentication
    LOGIN_SYSTEM = 'login:system',
    REFRESH_TOKENS = 'refresh:sessions',

    // === FILE MANAGEMENT PERMISSIONS ===
    // File operations
    READ_FILES = 'read:files',
    UPLOAD_FILES = 'upload:files',
    UPDATE_FILES = 'update:files',
    DELETE_FILES = 'delete:files',
    PROCESS_FILES = 'process:files',
    DOWNLOAD_FILES = 'download:files',

    // Self-file operations (user's own files)
    READ_SELF_FILES = 'self:read:files',
    UPLOAD_SELF_FILES = 'self:upload:files',
    UPDATE_SELF_FILES = 'self:update:files',
    DELETE_SELF_FILES = 'self:delete:files',
    DOWNLOAD_SELF_FILES = 'self:download:files',

    // Template management
    READ_TEMPLATES = 'read:templates',
    CREATE_TEMPLATES = 'create:templates',
    UPDATE_TEMPLATES = 'update:templates',
    DELETE_TEMPLATES = 'delete:templates',

    // === FINANCIAL DATA PERMISSIONS ===
    // Financial data access
    READ_FINANCIAL_DATA = 'read:financial_data',
    CREATE_FINANCIAL_DATA = 'create:financial_data',
    UPDATE_FINANCIAL_DATA = 'update:financial_data',
    DELETE_FINANCIAL_DATA = 'delete:financial_data',

    // Self-financial data (user's own data)
    READ_SELF_FINANCIAL_DATA = 'self:read:financial_data',
    CREATE_SELF_FINANCIAL_DATA = 'self:create:financial_data',
    UPDATE_SELF_FINANCIAL_DATA = 'self:update:financial_data',
    DELETE_SELF_FINANCIAL_DATA = 'self:delete:financial_data',

    // Reports and dashboards
    READ_REPORTS = 'read:reports',
    CREATE_REPORTS = 'create:reports',
    UPDATE_REPORTS = 'update:reports',
    DELETE_REPORTS = 'delete:reports',
    EXPORT_REPORTS = 'export:reports',

    READ_DASHBOARDS = 'read:dashboards',
    CREATE_DASHBOARDS = 'create:dashboards',
    UPDATE_DASHBOARDS = 'update:dashboards',
    DELETE_DASHBOARDS = 'delete:dashboards',

    // === CALCULATION ENGINE PERMISSIONS ===
    // Calculation operations
    READ_CALCULATIONS = 'read:calculations',
    CREATE_CALCULATIONS = 'create:calculations',
    UPDATE_CALCULATIONS = 'update:calculations',
    DELETE_CALCULATIONS = 'delete:calculations',
    PROCESS_CALCULATIONS = 'process:calculations',

    // Self-calculations (user's own calculations)
    READ_SELF_CALCULATIONS = 'self:read:calculations',
    CREATE_SELF_CALCULATIONS = 'self:create:calculations',
    UPDATE_SELF_CALCULATIONS = 'self:update:calculations',
    DELETE_SELF_CALCULATIONS = 'self:delete:calculations',

    // Charts and analytics
    READ_CHARTS = 'read:charts',
    CREATE_CHARTS = 'create:charts',
    UPDATE_CHARTS = 'update:charts',
    DELETE_CHARTS = 'delete:charts',
    EXPORT_CHARTS = 'export:charts',

    READ_ANALYTICS = 'read:analytics',
    CREATE_ANALYTICS = 'create:analytics',

    // === SERVICE-SPECIFIC PERMISSIONS ===
    // Identity service
    IDENTITY_ADMIN = 'identity:admin:system',
    IDENTITY_MANAGE_USERS = 'identity:manage:users',
    IDENTITY_READ_SESSIONS = 'identity:read:sessions',

    // Finance service
    FINANCE_ADMIN = 'finance:admin:system',
    FINANCE_PROCESS_FILES = 'finance:process:files',
    FINANCE_MANAGE_DATA = 'finance:manage:financial_data',

    // Calc service
    CALC_ADMIN = 'calc:admin:system',
    CALC_MANAGE_CHARTS = 'calc:manage:charts',
    CALC_PROCESS_HEAVY = 'calc:process:calculations',

    // Client service
    CLIENT_ADMIN = 'client:admin:system',
    CLIENT_MANAGE_UI = 'client:manage:dashboards',

    // === API ACCESS PERMISSIONS ===
    // General API access
    ACCESS_API = 'read:api',

    // Service-specific API access
    ACCESS_IDENTITY_API = 'identity:read:api',
    ACCESS_FINANCE_API = 'finance:read:api',
    ACCESS_CLIENT_API = 'client:read:api',
    ACCESS_CALC_API = 'calc:read:api'
}