/**
 * Redis cache helper functions.
 * Provides methods for caching and retrieving data from Redis.
 * @module redis-cache.helper
 */
import type { ReturnModelType } from '@typegoose/typegoose';
import type { IRedisService } from '../interfaces';
import type { GetQueryOptions } from '../types';
import { CacheKeys } from '../constants';

/**
 * A helper class to encapsulate caching strategy for a repository using typegoose models.
 * It determines if a query is cacheable and manages cache keys and invalidation.
 */
export class RedisCacheHelper<T> {
	private readonly redisService: IRedisService;
	private readonly dbModel: ReturnModelType<new () => T>;

	/**
     * Initializes a new instance of the CacheHelper.
     * @param redisService The Redis service instance to interact with Redis.
     * @param dbModel The Typegoose model to derive cache keys from.
     */
	constructor(
		redisService: IRedisService,
		dbModel: ReturnModelType<new () => T>
	) {
		this.redisService = redisService;
		this.dbModel = dbModel;
	}

	/**
     * Determines if a given query is eligible for caching.
     * A query is cacheable if it has no filter conditions.
     * Caching is typically only used for general, unfiltered "get all" queries.
     * @param options The query options.
     * @returns True if the query should be cached, otherwise false.
     */
	public shouldUseCache(options: GetQueryOptions<T>): boolean {
		return !options.query || Object.keys(options.query).length === 0;
	}

	/**
     * Generates the primary cache key for the entire collection.
     * @returns A standardized cache key string.
     */
	public getCacheKey(): string {
		return CacheKeys.MODEL_ENTITY(this.dbModel.modelName);
	}

	/**
     * Invalidates (deletes) the primary cache for the collection.
     * This should be called after any create, update, or delete operation.
     * @returns A Promise that resolves when the cache is invalidated.
     */
	public async removeCacheKey(): Promise<void> {
		const cacheKey = this.getCacheKey();
		const cacheExists = await this.redisService.exists(cacheKey);
		if (cacheExists) {
			await this.redisService.deleteCache(cacheKey);
			console.log(`[Cache] Invalidated for ${this.dbModel.modelName}`);
		}
	}
}