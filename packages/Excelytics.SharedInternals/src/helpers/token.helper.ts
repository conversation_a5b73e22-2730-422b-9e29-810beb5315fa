/**
 * Token helper functions.
 * Provides methods for working with tokens, such as extracting from headers.
 * @module token.helper
 */

/**
 * Extract Bearer token from Authorization header
 */
function ExtractBearerToken(authHeader: string): string | null {
	const parts = authHeader.split(' ');
	if (parts.length !== 2 || parts[0] !== 'Bearer') {
		return null;
	}

	return parts[1];
}

/**
 * Create a Unix timestamp from a Date object.
 * @param date The date to convert to a Unix timestamp.
 * @returns The Unix timestamp.
 */
export function createUnixTimestamp(date: number = Date.now()): number {
	return Math.floor(date / 1000);
}

export const TokenHelper = {
	ExtractBearerToken
};