/**
 * Error handling helper functions.
 * Provides standardized error response formatting across microservices.
 * @module error.helper
 */
import { ZodError } from 'zod';
import { EnumEnv } from '../enums';
import { env_ } from '../utils/env_internal';
import type { ErrorResponse } from '../types';
import { ErrorResponseSchema } from '../schemas';
import { ErrorCodes, HttpStatus } from '../constants';

/**
 * Handles CORS-related errors with standardized formatting.
 * Used when a request is blocked by CORS policy.
 *
 * @param error - The CORS error object
 * @returns Formatted error response with status code and response body
 * Return type: { statusCode: number, responseBody: ErrorResponse }
 */
function handleCorsErrors(error: any): { statusCode: number, responseBody: ErrorResponse } {
	console.warn('[Error Type]: CORS Error String Match');
	console.warn('Message:', error.message);

	const responseBody: ErrorResponse = {
		success: false,
		message: 'Cross-Origin Request Blocked by CORS policy.',
		error: {
			code: ErrorCodes.CORS_NOT_ALLOWED,
			message: 'This request was blocked due to CORS restrictions.'
		}
	};

	// Validate response structure with Zod
	const validatedResponse = ErrorResponseSchema.parse(responseBody);

	return {
		statusCode: HttpStatus.FORBIDDEN,
		responseBody: validatedResponse
	};
}

/**
 * Creates a standardized error response from a ZodError.
 * Used for handling validation failures in request data.
 *
 * @param error - The Zod validation error
 * @returns Formatted error response with status code and response body
 * Return type: { statusCode: number, responseBody: ErrorResponse }
 */
function handleZodErrors(error: ZodError): { statusCode: number, responseBody: ErrorResponse } {
	console.error('[Error Type]: ZodError');
	console.error('Message:', 'Input validation failed');
	console.error('Details:', JSON.stringify(error.format(), null, 2));

	const responseBody: ErrorResponse = {
		success: false,
		message: 'Input validation failed. Please check your data.',
		error: {
			code: ErrorCodes.VALIDATION_ERROR,
			message: 'One or more fields failed validation.',
			details: error.format()
		}
	};

	// Validate response structure with Zod
	const validatedResponse = ErrorResponseSchema.parse(responseBody);

	return {
		// Or HttpStatus.UNPROCESSABLE_ENTITY (422)
		statusCode: HttpStatus.BAD_REQUEST,
		responseBody: validatedResponse
	};
}

/**
 * Handles unexpected or generic errors with standardized formatting.
 * Provides different responses for client vs. server errors.
 *
 * @param error - The error object to handle
 * @returns Formatted error response with status code and response body
 * Return type: { statusCode: number, responseBody: ErrorResponse }
 */
function handleUnexpectedErrors(error: any): { statusCode: number, responseBody: ErrorResponse } {
	console.error('[Error Type]: Unknown/Standard Error');
	console.error('Message:', error.message || 'An unknown error occurred.');
	if (env_.ENV === EnumEnv.Development || error.showStack) {
		console.error('Stack:', error.stack);
	}

	const checkStatusCode = error.status || error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR;
	const isClientError = checkStatusCode >= 400 && checkStatusCode < 500;

	const responseBody: ErrorResponse = {
		success: false,
		message: (isClientError && error.message) ? error.message : 'An unexpected error occurred on the server.',
		error: {
			code: error.code || ErrorCodes.UNKNOWN_ERROR,
			message: (isClientError && error.message) ? error.message : 'An unexpected error occurred.'
		}
	};

	// Validate response structure with Zod
	const validatedResponse = ErrorResponseSchema.parse(responseBody);

	return {
		statusCode: error.status || error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
		responseBody: validatedResponse
	};
}

/**
 * @constant ErrorHelper
 * @description An object that groups together error handling helper functions.
 */
export const ErrorHelper = {
	handleZodErrors,
	handleCorsErrors,
	handleUnexpectedErrors
};