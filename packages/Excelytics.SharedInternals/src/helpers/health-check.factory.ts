/**
 * Factory function for creating a HealthCheckHelper instance.
 * Automatically configures the helper with the correct external services to ping.
 * @module health-check.factory
 */
import type { HealthCheckFunction, ServiceNameType } from '../types';
import { HealthCheckHelper } from './health-check.helper';
import { ALL_MICROSERVICES } from '../config';

/** Configuration options for creating a HealthHelper instance via the factory. */
export interface CreateHealthHelperOptions {
    /** The unique name of the current service (e.g., ServiceNames.IDENTITY). */
    serviceName: ServiceNameType;
    /** The version of the current service, typically from environment variables. */
    version: string;
    /** An array of local health check functions for this service's direct dependencies. */
    checks: HealthCheckFunction[];
}

/**
 * @factory createHealthHelper
 * @description Creates and configures a HealthCheckHelper for a specific microservice.
 * It automatically determines the list of external services to ping by taking all known microservices and filtering out the current service itself.
 * @param options The service-specific configuration.
 * @returns A fully initialized instance of HealthCheckHelper.
 */
export const createHealthHelper = (
	options: CreateHealthHelperOptions,
): HealthCheckHelper => {
	const { serviceName, version, checks } = options;

	// This is the key logic: automatically determine which services are "external".
	const externalServices = ALL_MICROSERVICES.filter(
		(service) => service.name !== serviceName,
	);

	// Create and return the helper instance with the correct configuration.
	return new HealthCheckHelper({
		serviceName: serviceName,
		version: version,
		checks: checks,
		externalServices: externalServices
	});
};