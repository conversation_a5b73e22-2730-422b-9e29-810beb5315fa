/**
 * Helper functions for error logging configuration and behavior
 * @module error-logging.helper
 */
import { EnumEnv } from '../enums';
import { BaseError } from '../errors';
import { ZADateUtils } from '../utils';
import type { Request } from '../config';
import type { ErrorLoggingConfig, ErrorClassification } from '../types';

/**
 * Main error logging orchestrator, executed after error classification
 * @param error - The error object
 * @param request - Express request object
 * @param config - Logging configuration
 * @param classification - Error classification
 */
export function logError(
	error: any,
	request: Request,
	config: ErrorLoggingConfig,
	classification: ErrorClassification
): void {
	// Always log basic info for verbose mode or unexpected errors (1xx to 4xx)
	if (config.isVerbose || !classification.isExpectedError) {
		logBasicErrorInfo(request);
	}

	// Log specific error details based on type
	if (classification.isBaseError) {
		logBaseErrorDetails(error, config, classification);
	} else {
		logGenericErrorDetails(error, config, classification);
	}
}

/**
 * Logs basic error information
 * @param request - Express request object
 */
export function logBasicErrorInfo(request: Request): void {
	console.error('--- Global Error Handler Triggered ---');
	console.error('[Request Path]:', `${request.method} ${request.originalUrl}`);
	console.error('[Timestamp]:', ZADateUtils.formatSouthAfricanDateTime(false));
}

/**
 * Logs BaseError specific information
 * @param error - The BaseError instance
 * @param config - Logging configuration
 * @param classification - Error classification
 */
export function logBaseErrorDetails(
	error: BaseError,
	config: ErrorLoggingConfig,
	classification: ErrorClassification
): void {
	if (config.isVerbose || !classification.isExpectedError) {
		console.error('[Error Type]: BaseError');
		console.error('[Error Code]:', error.errorCode);
		console.error('[Status Code]:', error.statusCode);
		console.error('[Message]:', error.message);
	}

	// Log details only in verbose mode or for unexpected errors (5xx)
	if (error.details && (config.isVerbose || !classification.isExpectedError)) {
		if (config.shouldLogSensitiveInfo) {
			console.error('[Debug Details]:', JSON.stringify(error.details, null, 2));
		} else {
			console.error('[Details REDACTED] - Set LOG_SENSITIVE=true to view');
		}
	}

	// Log original error only in debug mode
	if (error.originalError && config.isDebugMode) {
		if (config.shouldLogSensitiveInfo) {
			console.error('[Original Error]:', error.originalError);
		} else {
			console.error('[Original Error REDACTED] - Set LOG_SENSITIVE=true to view');
		}
	}
}

/**
 * Logs non-BaseError information
 * @param error - The error object
 * @param config - Logging configuration
 * @param classification - Error classification
 */
export function logGenericErrorDetails(
	error: any,
	config: ErrorLoggingConfig,
	classification: ErrorClassification
): void {
	// For non-BaseError, always log basic info for unexpected errors
	if (!classification.isExpectedError) {
		console.error('[Error Type]:', error?.constructor?.name || 'Unknown');
		console.error('[Message]:', error?.message);

		if (error?.stack && config.isDebugMode) {
			if (config.shouldLogSensitiveInfo) {
				console.error('[Stack]:', error.stack);
			} else {
				console.error('[Stack REDACTED] - Set LOG_SENSITIVE=true to view');
			}
		}
	}
}

// --- Error Classification & Logging Configuration Helpers ---
/**
 * Classifies error type for appropriate logging behavior
 * @returns { ErrorClassification } Classification object
 */
export function classifyError(error: any): ErrorClassification {
	const isBaseError = error instanceof BaseError;
	const isZodError = error?.name === 'ZodError' && typeof error?.format === 'function';
	const isCorsError = error?.message && error.message.includes('Not allowed by CORS');

	// Expected errors are client errors (1xx to 4xx) or validation/CORS errors
	const isExpectedError = (error?.statusCode && error.statusCode < 500) || isZodError || isCorsError;

	return {
		isExpectedError,
		isBaseError,
		isZodError,
		isCorsError
	} as ErrorClassification;
}

/**
 * Determines logging configuration based on environment and debug flags
 * @returns { ErrorLoggingConfig } Configuration object
 */
export function getErrorLoggingConfig(): ErrorLoggingConfig {
	// Determine the current environment variables set for logging
	const currentEnv = Bun.env.ENV || process.env.NODE_ENV || 'development';
	const isDebugMode = currentEnv === EnumEnv.Development || currentEnv === EnumEnv.Test;
	const isVerbose = isDebugMode || Bun.env.DEBUG_ERRORS === 'true';
	const shouldLogSensitiveInfo = currentEnv === EnumEnv.Development && Bun.env.LOG_SENSITIVE === 'true';

	return {
		isVerbose,
		isDebugMode,
		shouldLogSensitiveInfo
	} as ErrorLoggingConfig;
}

// --- Sensitive Information Redaction ---
/**
 * Redacts sensitive information from error details
 * @param details - Error details object
 * Complexity: Medium (< 20 lines)
 */
export function redactSensitiveInformation(details: any): any {
	if (!details || typeof details !== 'object') {
		return details;
	}
	
	const redactedDetails = { ...details };
	const sensitivePatterns = ['password', 'token', 'secret', 'key', 'auth', 'credential', 'session', 'jwt', 'bearer'];
	
	Object.keys(redactedDetails).forEach(key => {
		const lowerKey = key.toLowerCase();
		const isSensitive = sensitivePatterns.some(pattern => lowerKey.includes(pattern));
		
		if (isSensitive) {
			redactedDetails[key] = '[REDACTED]';
		}
	});
	
	return redactedDetails;
}