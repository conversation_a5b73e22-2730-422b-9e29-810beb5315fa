/**
 * Environment helper functions.
 * Provides methods for parsing and validating environment variables.
 * @module environment.helper
 */
import { BaseFieldsSchema } from '../utils';
import { ZodSchema, ZodObject } from 'zod';
import fs from 'fs';

/**
 * Parses environment variables with <PERSON>od and provides a human-readable error message on failure
 * @param schema The Zod schema to parse the environment with
 * @param env The environment object to parse (i.e., `process.env` or `Bun.env`)
 * @returns The parsed environment variables
 */
export function parseEnvWithZodErrorFormatter<T>(
	schema: ZodSchema<T>,
	env: any
): T {
	const result = schema.safeParse(env)
	if (!result.success) {
		console.error("\n❌ Invalid environment configuration:")
		for (const issue of result.error.issues) {
			// path will be ['MONGO_USER'] or ['REDIS_PORT']
			console.error(`  • ${issue.path.join(".")}: ${issue.message}`)
		}
		console.error("\nPlease make sure your .env contains all required variables.")
		process.exit(1)
	}
	return result.data
}

/**
 * Generates an example .env file based on the provided Zod schema
 * @param schemaObject The Zod schema to generate the example from (defaults to `BaseFieldsSchema`)
 * @returns The generated example .env file
 */
export function generateExampleEnvFile(
	schemaObject: ZodObject<any> = BaseFieldsSchema
) {
	const keys = Object.keys((schemaObject as any)._def.shape());
	const example = keys.map(k => `${k}=`).join("\n");

	fs.writeFileSync(".env.example", example);
}