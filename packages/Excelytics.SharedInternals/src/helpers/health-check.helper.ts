/**
 * @module health-check.helper
 * @description Provides health check functionality for Excelytics microservices.
 * It can perform both shallow (local dependencies only) and deep (including external services) checks.
 */
import axios from 'axios';
import {
	HealthStatus,
	type ApiResponse,
	type ServiceNameType,
	type DependencyHealth,
	type HealthCheckResult,
	type HealthCheckFunction,
	type HealthCheckHelperOptions
} from '../types';
import { env_ } from '../utils/env_internal';

/**
 * @class HealthCheckHelper
 * @description A utility class for performing health checks on a microservice.
 * It can run both shallow (local dependencies only) and deep (including external services) checks.
 */
export class HealthCheckHelper {
	private readonly options: HealthCheckHelperOptions;

	constructor(options: HealthCheckHelperOptions) {
		this.options = options;
	}

	/**
     * @method getHealth
     * @description Performs a "shallow" health check, running only the local dependency checks provided in the constructor
     * @returns { Promise<HealthCheckResult> } A structured health check result
     */
	public async getHealth(): Promise<HealthCheckResult> {
		return this.runChecks(this.options.checks);
	}

	/**
     * @method getDeepHealth
     * @description Performs a "deep" health check, running local checks AND checking external services
     * @returns { Promise<HealthCheckResult> } A comprehensive, structured health check result
     */
	public async getDeepHealth(): Promise<HealthCheckResult> {
		const externalChecks: HealthCheckFunction[] = (
			this.options.externalServices || []
		).map(
			(service) => () => this.checkExternalService(service.name, service.url)
		);

		const allChecks = [...this.options.checks, ...externalChecks];
		return this.runChecks(allChecks);
	}

	/**
     * @private
     * @method runChecks
     * @description The core orchestrator that executes an array of check functions concurrently
     */
	private async runChecks(checks: HealthCheckFunction[]): Promise<HealthCheckResult> {
		const startTime = process.hrtime();

		const dependencies = await Promise.all(checks.map((check) => check()));
		const overallStatus = dependencies.every((dep) => dep.status === HealthStatus.HEALTHY)
			? HealthStatus.HEALTHY
			: HealthStatus.UNHEALTHY;

		const diff = process.hrtime(startTime);
		const responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return {
			service: this.options.serviceName,
			version: this.options.version,
			timestamp: new Date(),
			status: overallStatus,
			responseTime,
			dependencies
		} as HealthCheckResult;
	}

	/**
     * @private
     * @method checkExternalService
     * @description Makes an HTTP GET request to an external service's health endpoint
     */
	private async checkExternalService(
		name: ServiceNameType,
		url?: string | undefined
	): Promise<DependencyHealth> {
		if (!url) {
			return { name, status: HealthStatus.UNAVAILABLE, error: 'Health check URL not configured.' };
		}

		const health: DependencyHealth = { name, status: HealthStatus.UNKNOWN };
		const startTime = process.hrtime();

		try {
			// Construct the full health endpoint URL by appending the health path to the base service URL
			const healthEndpointUrl = `${url.replace(/\/$/, '')}/api/${env_.API_VERSION}/health`;
			const response = await axios.get<ApiResponse<HealthCheckResult>>(healthEndpointUrl, { timeout: 5000 });

			if (response.status !== 200 || !response.data.success || !response.data.data) {
				health.status = HealthStatus.UNHEALTHY;
				health.error = 'Received non-successful response';
				return health;
			}

			// Trust the status reported by the service itself
			health.status = response.data.data.status;
		} catch (error: any) {
			health.status = HealthStatus.UNHEALTHY;
			health.error = error.message;
		}

		const diff = process.hrtime(startTime);
		health.responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return health;
	}
}