/**
 * Dynamic message helper functions
 * Provides parameterized messages for consistent formatting across all Excelytics microservices
 * @module message.helper
 *
 * @example
 * // Use legacy functions (from team member's repo)
 * MessageHelpers.Errors.DuplicateResourceFieldExists('email')
 * // Output: "A resource with this email already exists."
 *
 * // Use new functions (SharedInternals standard)
 * MessageHelpers.Errors.DUPLICATE_FIELD_EXISTS('email')
 * // Output: "A resource with this email already exists."
 */

/**
 * Dynamic message functions organized by category
 */
export const MessageHelpers = {
	/**
	 * Generic error message functions
	 */
	Errors: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====

		/**
		 * Legacy: Dynamic message for duplicate resource field
		 * @param field - The field name that already exists
		 * @returns Formatted error message
		 * @example DuplicateResourceFieldExists('email') → "A resource with this email already exists."
		 */
		DuplicateResourceFieldExists: (field: string) => `A resource with this ${field} already exists.`,

		/**
		 * Legacy: Dynamic message for invalid ID format
		 * @param id - The invalid ID value
		 * @returns Formatted error message
		 * @example InvalidIdFormat('abc123') → "Invalid Id format provided: abc123"
		 */
		InvalidIdFormat: (id: any) => `Invalid Id format provided: ${id}`,

		/**
		 * Legacy: Dynamic message for duplicate resource ID
		 * @param id - The duplicate ID value
		 * @returns Formatted error message
		 * @example DuplicateResourceIdExists('123') → "Invalid Id format provided: 123"
		 */
		DuplicateResourceIdExists: (id: any) => `Invalid Id format provided: ${id}`,

		// ===== NEW DYNAMIC FUNCTIONS (Recommended - SharedInternals standard) =====

		/**
		 * New: Dynamic message for duplicate field - use instead of DuplicateResourceFieldExists
		 * @param field - The field name that already exists
		 * @returns Formatted error message
		 * @example DUPLICATE_FIELD_EXISTS('email') → "A resource with this email already exists."
		 */
		DUPLICATE_FIELD_EXISTS: (field: string) => `A resource with this ${field} already exists.`,

		/**
		 * New: Dynamic message for invalid ID format - use instead of InvalidIdFormat
		 * @param id - The invalid ID value
		 * @returns Formatted error message
		 * @example INVALID_ID_FORMAT('abc123') → "Invalid ID format provided: abc123"
		 */
		INVALID_ID_FORMAT: (id: any) => `Invalid ID format provided: ${id}`,

		/**
		 * New: Dynamic message for duplicate resource ID - use instead of DuplicateResourceIdExists
		 * @param id - The duplicate ID value
		 * @returns Formatted error message
		 * @example DUPLICATE_RESOURCE_ID('123') → "Resource with ID 123 already exists."
		 */
		DUPLICATE_RESOURCE_ID: (id: any) => `Resource with ID ${id} already exists.`,

		/**
		 * New: Dynamic message for missing required field
		 * @param field - The name of the missing required field
		 * @returns Formatted error message
		 * @example MISSING_REQUIRED_FIELD('username') → "Required field 'username' is missing."
		 */
		MISSING_REQUIRED_FIELD: (field: string) => `Required field '${field}' is missing.`,

		/**
		 * New: Dynamic message for invalid field value
		 * @param field - The field name with invalid value
		 * @param value - The invalid value provided
		 * @returns Formatted error message
		 * @example INVALID_FIELD_VALUE('age', -5) → "Invalid value '-5' for field 'age'."
		 */
		INVALID_FIELD_VALUE: (field: string, value: any) => `Invalid value '${value}' for field '${field}'.`,

		/**
		 * New: Dynamic message for operation failed
		 * @param operation - The name of the operation that failed
		 * @returns Formatted error message
		 * @example OPERATION_FAILED('User creation') → "User creation operation failed."
		 */
		OPERATION_FAILED: (operation: string) => `${operation} operation failed.`,
	},

	/**
	 * Generic success message functions
	 */
	Success: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====

		/**
		 * Legacy: Dynamic message for entity creation
		 * @param entityName - The name of the entity that was created
		 * @returns Formatted success message
		 * @example EntityCreated('User') → "User created successfully."
		 */
		EntityCreated: (entityName: string) => `${entityName} created successfully.`,

		/**
		 * Legacy: Dynamic message for entity retrieval
		 * @param entityName - The name of the entity that was retrieved
		 * @returns Formatted success message
		 * @example EntityRetrieved('User') → "User retrieved successfully."
		 */
		EntityRetrieved: (entityName: string) => `${entityName} retrieved successfully.`,

		/**
		 * Legacy: Dynamic message for entity update
		 * @param entityName - The name of the entity that was updated
		 * @returns Formatted success message
		 * @example EntityUpdated('User') → "User updated successfully."
		 */
		EntityUpdated: (entityName: string) => `${entityName} updated successfully.`,

		/**
		 * Legacy: Dynamic message for entity deletion
		 * @param entityName - The name of the entity that was deleted
		 * @param id - The ID of the deleted entity
		 * @returns Formatted success message
		 * @example EntityDeleted('User', '123') → "User with 123 deleted successfully."
		 */
		EntityDeleted: (entityName: string, id: string) => `${entityName} with ${id} deleted successfully.`,

		// ===== NEW DYNAMIC FUNCTIONS (Recommended - SharedInternals standard) =====

		/**
		 * New: Dynamic message for entity creation - use instead of EntityCreated
		 * @param entityName - The name of the entity that was created
		 * @returns Formatted success message
		 * @example ENTITY_CREATED('User') → "User created successfully."
		 */
		ENTITY_CREATED: (entityName: string) => `${entityName} created successfully.`,

		/**
		 * New: Dynamic message for entity retrieval - use instead of EntityRetrieved
		 * @param entityName - The name of the entity that was retrieved
		 * @returns Formatted success message
		 * @example ENTITY_RETRIEVED('User') → "User retrieved successfully."
		 */
		ENTITY_RETRIEVED: (entityName: string) => `${entityName} retrieved successfully.`,

		/**
		 * New: Dynamic message for entity update - use instead of EntityUpdated
		 * @param entityName - The name of the entity that was updated
		 * @returns Formatted success message
		 * @example ENTITY_UPDATED('User') → "User updated successfully."
		 */
		ENTITY_UPDATED: (entityName: string) => `${entityName} updated successfully.`,

		/**
		 * New: Dynamic message for entity deletion - use instead of EntityDeleted
		 * @param entityName - The name of the entity that was deleted
		 * @param id - The ID of the deleted entity
		 * @returns Formatted success message
		 * @example ENTITY_DELETED('User', '123') → "User with ID 123 deleted successfully."
		 */
		ENTITY_DELETED: (entityName: string, id: string) => `${entityName} with ID ${id} deleted successfully.`,

		/**
		 * New: Dynamic message for operation completion with count
		 * @param operation - The name of the operation that completed
		 * @param count - The number of items processed
		 * @returns Formatted success message
		 * @example OPERATION_COMPLETED_WITH_COUNT('Data import', 150) → "Data import completed successfully. 150 items processed."
		 */
		OPERATION_COMPLETED_WITH_COUNT: (operation: string, count: number) => `${operation} completed successfully. ${count} items processed.`,

		/**
		 * New: Dynamic message for batch operation with success/total counts
		 * @param operation - The name of the batch operation
		 * @param successCount - The number of items processed successfully
		 * @param totalCount - The total number of items attempted
		 * @returns Formatted success message
		 * @example BATCH_OPERATION_SUCCESS('User import', 95, 100) → "User import completed: 95/100 items processed successfully."
		 */
		BATCH_OPERATION_SUCCESS: (operation: string, successCount: number, totalCount: number) => `${operation} completed: ${successCount}/${totalCount} items processed successfully.`,
	},

	/**
	 * Authentication dynamic message functions
	 */
	Auth: {
		// ===== NEW DYNAMIC FUNCTIONS (Recommended - SharedInternals standard) =====

		/**
		 * New: Dynamic message for failed login attempts with lockout
		 * @param attempts - The number of failed login attempts
		 * @param lockoutTime - The lockout duration in minutes
		 * @returns Formatted error message
		 * @example LOGIN_ATTEMPTS_EXCEEDED(5, 15) → "Too many failed login attempts (5). Account locked for 15 minutes."
		 */
		LOGIN_ATTEMPTS_EXCEEDED: (attempts: number, lockoutTime: number) => `Too many failed login attempts (${attempts}). Account locked for ${lockoutTime} minutes.`,

		/**
		 * New: Dynamic message for permission denied for specific resource
		 * @param resource - The resource that access was denied to
		 * @param action - The action that was attempted
		 * @returns Formatted error message
		 * @example PERMISSION_DENIED_FOR_RESOURCE('user profile', 'delete') → "Permission denied: Cannot delete user profile."
		 */
		PERMISSION_DENIED_FOR_RESOURCE: (resource: string, action: string) => `Permission denied: Cannot ${action} ${resource}.`,

		/**
		 * New: Dynamic message for role requirement
		 * @param requiredRole - The role required for the operation
		 * @returns Formatted error message
		 * @example ROLE_REQUIRED('Administrator') → "Access denied: Administrator role required."
		 */
		ROLE_REQUIRED: (requiredRole: string) => `Access denied: ${requiredRole} role required.`,
	},

	/**
	 * Repository dynamic message functions (from team member's repo)
	 */
	Repository: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====

		/**
		 * Legacy: Dynamic message for invalid ID format
		 * @param id - The invalid ID value
		 * @returns Formatted error message
		 * @example InvalidIdFormat('abc123') → "Invalid Id format provided: abc123"
		 */
		InvalidIdFormat: (id: any) => `Invalid Id format provided: ${id}`,

		/**
		 * Legacy: Dynamic message for invalid ObjectId format
		 * @param id - The invalid ObjectId value
		 * @returns Formatted error message
		 * @example InvalidObjectIdFormat('xyz789') → "Invalid ObjectId format provided: xyz789"
		 */
		InvalidObjectIdFormat: (id: any) => `Invalid ObjectId format provided: ${id}`,

		/**
		 * Legacy: Dynamic message for model not initialized
		 * @param modelName - The name of the model that is not initialized
		 * @returns Formatted error message
		 * @example ModelNotInitialized('User') → "Model User is not properly initialized"
		 */
		ModelNotInitialized: (modelName: string) => `Model ${modelName} is not properly initialized`,

		/**
		 * Legacy: Dynamic message for document not found
		 * @param id - The ID of the document that was not found
		 * @returns Formatted error message
		 * @example DocumentIdNotFound('507f1f77bcf86cd799439011') → "Document with Id 507f1f77bcf86cd799439011 not found."
		 */
		DocumentIdNotFound: (id: string) => `Document with Id ${id} not found.`
	},

	/**
	 * Mutator dynamic message functions (from team member's repo)
	 */
	Mutator: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====

		/**
		 * Legacy: Dynamic message for entity not found
		 * @param id - The ID of the entity that was not found
		 * @returns Formatted error message
		 * @example EntityNotFound('507f1f77bcf86cd799439011') → "Entity with Id 507f1f77bcf86cd799439011 not found"
		 */
		EntityNotFound: (id: string) => `Entity with Id ${id} not found`,

		/**
		 * Legacy: Dynamic message for invalid entity ID
		 * @param id - The invalid entity ID
		 * @returns Formatted error message
		 * @example InvalidEntityId('abc123') → "Invalid entity Id: abc123"
		 */
		InvalidEntityId: (id: string) => `Invalid entity Id: ${id}`
	},

	/**
	 * Validation dynamic message functions (from team member's repo)
	 */
	Validation: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported - from team member's repo) =====

		/**
		 * Legacy: Dynamic message for entity not found
		 * @param entity - The name of the entity that was not found
		 * @returns Formatted error message
		 * @example EntityNotFound('User') → "User not found"
		 */
		EntityNotFound: (entity: string) => `${entity} not found`,

		/**
		 * Legacy: Dynamic message for invalid entity ID
		 * @param entity - The name of the entity with invalid ID
		 * @returns Formatted error message
		 * @example EntityIdInvalid('User') → "Invalid User Id"
		 */
		EntityIdInvalid: (entity: string) => `Invalid ${entity} Id`
	},

	/**
	 * Excel dynamic message functions
	 */
	Excel: {
		// ===== LEGACY DYNAMIC FUNCTIONS (Still supported) =====

		/**
		 * Legacy: Dynamic message for invalid sheet name
		 * @param sheetName - The name of the sheet that was not found
		 * @returns Formatted error message
		 * @example InvalidSheetName('Summary') → "Sheet 'Summary' not found in Excel file"
		 */
		InvalidSheetName: (sheetName: string) => `Sheet '${sheetName}' not found in Excel file`,

		/**
		 * Legacy: Dynamic message for invalid cell reference
		 * @param cell - The invalid cell reference
		 * @returns Formatted error message
		 * @example InvalidCellReference('Z999') → "Invalid cell reference: Z999"
		 */
		InvalidCellReference: (cell: string) => `Invalid cell reference: ${cell}`,

		/**
		 * Legacy: Dynamic message for missing required column
		 * @param column - The name of the missing required column
		 * @returns Formatted error message
		 * @example MissingRequiredColumn('Email') → "Required column 'Email' not found"
		 */
		MissingRequiredColumn: (column: string) => `Required column '${column}' not found`,

		/**
		 * Legacy: Dynamic message for sheet processed successfully
		 * @param sheetName - The name of the sheet that was processed
		 * @returns Formatted success message
		 * @example SheetProcessed('Data') → "Sheet 'Data' processed successfully"
		 */
		SheetProcessed: (sheetName: string) => `Sheet '${sheetName}' processed successfully`,

		/**
		 * Legacy: Dynamic message for rows processed successfully
		 * @param count - The number of rows processed
		 * @returns Formatted success message
		 * @example RowsProcessed(150) → "150 rows processed successfully"
		 */
		RowsProcessed: (count: number) => `${count} rows processed successfully`
	}
};

/**
 * Utility functions for common message patterns and template replacement
 */
export const MessageUtils = {
	/**
	 * Format a static message with template parameter replacement
	 * @param template - The message template with {param} placeholders
	 * @param params - Object with key-value pairs for replacement
	 * @returns Formatted message with parameters replaced
	 * @example format('Hello {name}, you have {count} messages', { name: 'John', count: '5' }) → "Hello John, you have 5 messages"
	 */
	format: (template: string, params: Record<string, string>) => {
		return Object.entries(params).reduce((msg, [key, value]) => {
			return msg.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
		}, template);
	},

	/**
	 * Common entity operation shortcuts using new SharedInternals standard
	 */
	entity: {
		/**
		 * Get entity created message
		 * @param entityName - The name of the entity
		 * @returns Formatted success message
		 * @example entity.created('User') → "User created successfully."
		 */
		created: (entityName: string) => MessageHelpers.Success.ENTITY_CREATED(entityName),

		/**
		 * Get entity updated message
		 * @param entityName - The name of the entity
		 * @returns Formatted success message
		 * @example entity.updated('User') → "User updated successfully."
		 */
		updated: (entityName: string) => MessageHelpers.Success.ENTITY_UPDATED(entityName),

		/**
		 * Get entity deleted message
		 * @param entityName - The name of the entity
		 * @param id - The ID of the deleted entity
		 * @returns Formatted success message
		 * @example entity.deleted('User', '123') → "User with ID 123 deleted successfully."
		 */
		deleted: (entityName: string, id: string) => MessageHelpers.Success.ENTITY_DELETED(entityName, id),

		/**
		 * Get entity not found message
		 * @param entityName - The name of the entity
		 * @returns Formatted error message
		 * @example entity.notFound('User') → "User not found"
		 */
		notFound: (entityName: string) => MessageHelpers.Validation.EntityNotFound(entityName),

		/**
		 * Get invalid entity ID message
		 * @param entityName - The name of the entity
		 * @returns Formatted error message
		 * @example entity.invalidId('User') → "Invalid User Id"
		 */
		invalidId: (entityName: string) => MessageHelpers.Validation.EntityIdInvalid(entityName)
	},

	/**
	 * Common repository operation shortcuts using legacy functions
	 */
	repository: {
		/**
		 * Get invalid ID format message
		 * @param id - The invalid ID value
		 * @returns Formatted error message
		 * @example repository.invalidId('abc123') → "Invalid Id format provided: abc123"
		 */
		invalidId: (id: any) => MessageHelpers.Repository.InvalidIdFormat(id),

		/**
		 * Get document not found message
		 * @param id - The ID of the document that was not found
		 * @returns Formatted error message
		 * @example repository.notFound('507f1f77bcf86cd799439011') → "Document with Id 507f1f77bcf86cd799439011 not found."
		 */
		notFound: (id: string) => MessageHelpers.Repository.DocumentIdNotFound(id),

		/**
		 * Get model not initialized message
		 * @param modelName - The name of the model that is not initialized
		 * @returns Formatted error message
		 * @example repository.modelNotInitialized('User') → "Model User is not properly initialized"
		 */
		modelNotInitialized: (modelName: string) => MessageHelpers.Repository.ModelNotInitialized(modelName)
	}
};

/**
 * Helper function to get a message by path from Messages constants
 * Supports both static messages and template replacement
 * @param path - Dot notation path to the message (e.g., 'Auth.Errors.INVALID_CREDENTIALS')
 * @param params - Optional parameters for template replacement
 * @returns The message string with parameters replaced
 *
 * @example
 * // Static message
 * getMessage('Generic.Errors.NoDataFound') → "No data found"
 *
 * // Template replacement
 * getMessage('File.Errors.FILE_TOO_LARGE', { maxSize: '10MB' }) → "File is too large. Maximum size allowed is 10MB."
 */
export function getMessage(path: string, params?: Record<string, string>): string {
	// Import Messages here to avoid circular dependency
	// eslint-disable-next-line @typescript-eslint/no-require-imports
	const { Messages } = require('../constants/messages.constants');

	const keys = path.split('.');
	let message: any = Messages;

	for (const key of keys) {
		message = message[key];
		if (message === undefined) {
			return `Message not found: ${path}`;
		}
	}

	// Handle static string messages
	if (typeof message === 'string') {
		// Replace template parameters in the message
		if (params) {
			return Object.entries(params).reduce((msg, [key, value]) => {
				return msg.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
			}, message);
		}
		return message;
	}

	// Functions should not be in Messages constants anymore
	if (typeof message === 'function') {
		return `Function found at path ${path}. Use MessageHelpers instead.`;
	}

	return `Invalid message type at path: ${path}`;
}

// Export default for compatibility
export default MessageHelpers;