/**
 * Helper functions for processing error responses
 * @module error-response.helper
 */
import { EnumEnv } from '../enums';
import { BaseError } from '../errors';
import { <PERSON><PERSON>r<PERSON>elper } from './error.helper';
import { HttpStatus, ErrorCodes } from '../constants';
import { redactSensitiveInformation } from './error-logging.helper';
import type { ErrorResponse, ErrorLoggingConfig, ErrorClassification } from '../types';

/**
 * Main error response processor - orchestrates error handling.
 * Delegates to specific processors based on error type, and adds stack trace if appropriate.
 *
 * @param error - The error object
 * @param config - Logging configuration
 * @param classification - Error classification
 * @returns { statusCode: number; responseBody: ErrorResponse }
 */
export function processErrorResponse(
	error: any,
	config: ErrorLoggingConfig,
	classification: ErrorClassification
): { statusCode: number; responseBody: ErrorResponse } {
	let result: { statusCode: number; responseBody: ErrorResponse };

	// Process error based on type
	if (classification.isBaseError) {
		result = processBaseErrorResponse(error, config);
	} else if (classification.isZodError) {
		result = processZodErrorResponse(error);
	} else if (classification.isCorsError) {
		result = processCorsErrorResponse(error);
	} else {
		result = processGenericErrorResponse(error);
	}

	// Add stack trace if appropriate
	addStackTraceToResponse(error, result.responseBody, config, classification);

	return result;
}


/**
 * Processes BaseError instances into standardized response format.
 * Sanitizes and redacts sensitive information as needed.
 *
 * @param error - The BaseError instance
 * @param config - Logging configuration
 */
export function processBaseErrorResponse(
	error: BaseError,
	config: ErrorLoggingConfig
): { statusCode: number; responseBody: ErrorResponse } {
	const responseBody: ErrorResponse = {
		success: false,
		message: error.message,
		error: error.toApiResponseError()
	};

	// For production, sanitize generic 500 errors
	const currentEnv = Bun.env.ENV || process.env.NODE_ENV || 'development';
	if (currentEnv !== EnumEnv.Development &&
		error.statusCode === HttpStatus.INTERNAL_SERVER_ERROR &&
		(error.errorCode === ErrorCodes.INTERNAL_SERVER_ERROR || error.errorCode === ErrorCodes.UNKNOWN_ERROR)
	) {
		responseBody.message = 'An internal server error occurred. Please try again later.';
		responseBody.error.message = 'An internal server error occurred.';

		// (optional) Remove details in production for security
		delete responseBody.error.details;
		responseBody.error.details = '[REDACTED]';
	}

	// Redact sensitive information if not in debug mode
	if (!config.shouldLogSensitiveInfo && responseBody.error.details) {
		responseBody.error.details = redactSensitiveInformation(responseBody.error.details);
	}

	return {
		statusCode: error.statusCode,
		responseBody
	};
}

/**
 * Processes Zod validation errors.
 *
 * @param error - The Zod validation error
 * @returns { statusCode: number; responseBody: ErrorResponse }
 */
export function processZodErrorResponse(error: any): { statusCode: number; responseBody: ErrorResponse } {
	const { statusCode, responseBody } = ErrorHelper.handleZodErrors(error);
	return { statusCode, responseBody };
}

/**
 * Processes CORS errors.
 *
 * @param error - The CORS error
 * @returns { statusCode: number; responseBody: ErrorResponse }
 */
export function processCorsErrorResponse(error: any): { statusCode: number; responseBody: ErrorResponse } {
	const { statusCode, responseBody } = ErrorHelper.handleCorsErrors(error);
	return { statusCode, responseBody };
}

/**
 * Processes generic/unexpected errors.
 *
 * @param error - The error object
 * @returns { statusCode: number; responseBody: ErrorResponse }
 */
export function processGenericErrorResponse(error: any): { statusCode: number; responseBody: ErrorResponse } {
	const { statusCode, responseBody } = ErrorHelper.handleUnexpectedErrors(error);
	return { statusCode, responseBody };
}

/**
 * Adds stack trace to response if appropriate.
 *
 * @param error - The error object
 * @param responseBody - The response body
 * @param config - Logging configuration
 * @param classification - Error classification
 */
export function addStackTraceToResponse(
	error: any,
	responseBody: ErrorResponse,
	config: ErrorLoggingConfig,
	classification: ErrorClassification
): void {
	if (config.isDebugMode && error?.stack &&
		!classification.isBaseError && !classification.isZodError
	) {
		// Ensure details object exists
		if (!responseBody.error.details) {
			responseBody.error.details = {};
		}

		// Only include stack trace if sensitive info logging is enabled
		if (config.shouldLogSensitiveInfo) {
			responseBody.error.details.stack = error.stack;
		} else {
			responseBody.error.details.stack = '[REDACTED] - Set LOG_SENSITIVE=true to view stack trace';
		}
	}
}