/**
 * OData-related types.
 * Used for defining and managing OData query options and responses.
 * @module odata.types
 */
import type { FilterQuery } from 'mongoose';

/**
 * Defines the shape for standard query options used in repository 'get' operations,
 * allowing for filtering, projection, sorting, and pagination.
 */
export interface GetQueryOptions<T> {
    query?: FilterQuery<T>;
    projection?: Record<string, 0 | 1>;
    sort?: Record<string, 1 | -1>;
    limit?: number;
    skip?: number;
}

/**
 * Defines the shape of the object returned by the OData parser.
 * It extends the standard GetQueryOptions with the OData-specific `$count` flag.
 */
export type ODataQueryOptions<T> = GetQueryOptions<T> & { count: boolean };

/**
 * Defines the shape of a standard OData response for a collection.
 * @T The type of the entities in the collection.
 */
export interface ODataResponse<T> {
    '@odata.context': string;
    value: T[];
    '@odata.count'?: number; // The count is optional
}