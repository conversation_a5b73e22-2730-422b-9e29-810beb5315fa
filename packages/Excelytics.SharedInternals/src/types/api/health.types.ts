/**
 * Health check related types.
 * Used for monitoring service health across microservices.
 * @module health.types
 */
import { ServiceNames } from '../../constants';

/**
 * Enum representing the health status of a service or dependency.
 * Used in health check results and responses.
 */
export enum HealthStatus {
    HEALTHY = 'healthy',
    UNKNOWN = 'unknown',
    DEGRADED = 'degraded',
    UNHEALTHY = 'unhealthy',
    UNAVAILABLE = 'unavailable'
}

/**
 * Union type created from the values of the ServiceNames object
 * Example: 'excelytics.finance' | 'excelytics.client' | ...
 */
export type ServiceNameType = (typeof ServiceNames)[keyof typeof ServiceNames];

/**
 * Result of a health check operation for a service.
 * Contains overall status and dependency information.
 */
export interface HealthCheckResult {
    service: string;
    timestamp: Date;
    version: string;
    status: HealthStatus;
    responseTime: number;
    dependencies: DependencyHealth[];
}

/**
 * Health status of a single dependency.
 * Used to track individual component health.
 */
export interface DependencyHealth {
    name: string;
    error?: string;
    status: HealthStatus;
    responseTime?: number;
}

/**
 * Configuration options for the health check helper.
 * Defines service information and check functions.
 */
export interface HealthCheckHelperOptions {
    serviceName: string;
    version: string;
    checks: HealthCheckFunction[];
    externalServices?: { name: ServiceNameType; url: string | undefined }[];
}

/**
 * Function signature for performing a single dependency health check.
 * Returns a promise resolving to dependency health status.
 */
export type HealthCheckFunction = () => Promise<DependencyHealth>;