/**
 * @module service.types
 */

/**
 * @interface ServiceClientOptions
 * @description Options for making a request with the ServiceClient.
 */
export interface ServiceClientOptions {
    /**
     * Custom headers to include in the request.
     * These will be merged with any forwarding headers.
     */
    headers?: Record<string, string>;

    /**
     * The original Express request from the gateway.
     * Used for extracting headers to forward.
     */
    originalRequest?: Request;

    /**
     * Custom timeout for the request in milliseconds.
     * Overrides the default timeout set in the ServiceClient constructor.
     */
    timeout?: number;
}