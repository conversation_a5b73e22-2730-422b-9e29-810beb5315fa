/**
 * API response type definitions.
 * Provides standardized response structures for all API endpoints.
 * @module response.types
 */
import type { PaginationMetadata } from '../metadata.types';
import type { AccessTokenPayload, RefreshTokenPayload } from '../auth';

/**
 * Base response interface that all responses will follow.
 * Provides consistent structure for client-side handling.
 */
export interface ApiResponse<T = any> {
	success: boolean;
	message: string;
	data?: T;
	error?: {
		code: string;
		message: string;
		details?: any;
	};
	pagination?: PaginationMetadata;
}

/**
 * Success response with generic data type.
 * Used for all successful API operations.
 */
export interface SuccessResponse<T = any> extends ApiResponse<T> {
	success: true;
	data: T;
}

/**
 * Authentication token response type.
 * Contains tokens and payload information for authenticated sessions.
 */
export interface TokenResponse {
	token: string;
	refreshToken?: string;
	tokenPayload: AccessTokenPayload | RefreshTokenPayload;
}

/**
 * New: API error object structure.
 * Used within error responses to provide detailed error information.
 */
export interface ApiErrorObject {
	code: string;
	message: string;
	details?: any;
}

/**
 * Updated: Error response type.
 * Provides standardized error information for failed operations.
 */
export interface ErrorResponse extends ApiResponse {
	success: false;
	error: ApiErrorObject;
}