/**
 * Audit-related types.
 * Used for logging and tracking user actions across microservices.
 * @module audit.types
 */
import type { BaseDocument } from './database.types';

/**
 * Audit log document interface.
 * Extends the BaseDocument interface for MongoDB documents.
 */
export interface AuditLog extends BaseDocument {
    userId?: string;
    action: AuditAction;
    resource: string;
    resourceId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
}

/**
 * Audit action enum.
 * Represents the different types of actions that can be logged.
 */
export enum AuditAction {
    READ = 'read',
    LOGIN = 'login',
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
    LOGOUT = 'logout',
    UPLOAD = 'upload',
    EXPORT = 'export',
    DOWNLOAD = 'download',
    CALCULATE = 'calculate'
}

/**
 * Audit context interface.
 * Represents the context in which an audit action occurred.
 */
export interface AuditContext {
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
}