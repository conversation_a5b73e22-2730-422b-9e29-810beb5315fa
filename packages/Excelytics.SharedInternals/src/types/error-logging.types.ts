/**
 * Types for error logging configuration and behavior
 * @module error-logging.types
 */

/**
 * Configuration for error logging behavior based on environment and debug flags
 */
export interface ErrorLoggingConfig {
	/** Enable verbose logging (development/test environments) */
	isVerbose: boolean;
	/** Enable debug mode logging (includes stack traces) */
	isDebugMode: boolean;
	/** Allow logging of sensitive information (development only) */
	shouldLogSensitiveInfo: boolean;
}

/**
 * Classification of error types for logging purposes
 */
export interface ErrorClassification {
	/** Error has HTTP status code < 500 (client errors) */
	isExpectedError: boolean;
	/** Error is an instance of BaseError */
	isBaseError: boolean;
	/** Error is a Zod validation error */
	isZodError: boolean;
	/** Error is a CORS-related error */
	isCorsError: boolean;
}

/**
 * Sensitive field patterns to redact from error details
 */
export const SENSITIVE_FIELD_PATTERNS = [
	'credential',
	'password',
	'session',
	'secret',
	'bearer',
	'token',
	'auth',
	'key',
	'jwt'
] as const;

/**
 * Type for sensitive field patterns
 */
export type SensitiveFieldPattern = typeof SENSITIVE_FIELD_PATTERNS[number];