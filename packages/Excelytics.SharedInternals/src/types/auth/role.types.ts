/**
 * Role-related types.
 * Used for defining and managing user roles across microservices.
 * @module role.types
 */
import { EnumUserRoles } from '../../enums';

/**
 * @type UserRole
 * @description A union type representing a single valid user role string.
 * Derived from the UserRoleMetadata enum.
 * @example
 * const myRole: UserRole = 'admin'; // Correct
 * const myOtherRole: UserRole = UserRoleMetadata.USER; // Also correct
 */
export type UserRole = `${EnumUserRoles}`;

/**
 * @interface HasRoles
 * @description An interface for objects that have a 'roles' property.
 * Useful for type guards or for objects representing user permissions.
 * @roles An array of UserRole strings representing the roles assigned to the entity.
 */
export interface HasRoles {
    roles: UserRole[];
}