/**
 * Auth-related types.
 * Used for token payloads and related structures across microservices.
 * @module token.types
 */
import { EnumClientOrigin, EnumClientPath, EnumTokenType } from '../../enums';

/**
 * Base interface for token payloads, containing common fields.
 */
export interface BaseTokenPayload {
    userId: string;
    email: string;
    clientId: string;
    clientOrigin: EnumClientOrigin;
    clientPath: EnumClientPath;
    isActive?: boolean;
    roles?: string[] | undefined;
}

/**
 * Interface for access token payload, used for API authentication and authorization.
 * Typically, has a short lifespan (15-60 minutes).
 * @permissions An array of strings representing the permissions granted to the token.
 * examples: ['read:files', 'write:calculations', 'admin:users']
 */
export interface AccessTokenPayload extends BaseTokenPayload {
    tokenType: EnumTokenType.ACCESS;
    permissions?: string[];
    issuedAt: Date;
    expiresAt?: Date;
}

/**
 * Interface for refresh token payload, used to obtain new access tokens.
 * Typically, has a longer lifespan (7-30 days).
 */
export interface RefreshTokenPayload extends BaseTokenPayload {
    tokenType: EnumTokenType.REFRESH;
    issuedAt: Date;
    expiresAt?: Date;
}

/** Union type representing any valid token payload. */
export type TokenPayload = AccessTokenPayload | RefreshTokenPayload;

/** Input type for token generation, omitting 'isActive' from BaseTokenPayload. */
export type TokenGenerationInput = Omit<BaseTokenPayload, 'isActive'> & {
    permissions?: string[];
    isActive?: boolean;
};