/**
 * File-related types.
 * Used for file processing and metadata across microservices.
 * @module file.types
 */
import type { EnumFileProcessingStatus } from '../enums';
import type { CalculationResults } from './calc';

/**
 * Data about an uploaded file.
 * Contains basic file information and processing status.
 */
export interface FileUploadData {
    id: string;
    originalName: string;
    filename: string;
    mimetype: string;
    size: number;
    uploadedAt: Date;
    processedAt?: Date;
    status: EnumFileProcessingStatus;
}

/**
 * Additional metadata extracted from a file.
 * Contains information about file contents and analysis results.
 */
export interface FileMetadata {
    rowCount?: number;
    columnCount?: number;
    processingTime?: number;
    worksheetCount?: number;
    calculationResults?: CalculationResults;
}