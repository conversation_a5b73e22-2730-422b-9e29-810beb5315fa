/// <reference types="bun-types" />

declare global {
	// This explicit declaration of 'Bun' might be redundant if 'bun-types'
	// is correctly included in your tsconfig.json's "types" array or
	// picked up automatically. 'bun-types' is supposed to provide these globals.
	// However, it doesn't hurt to have it if you've faced issues.
	const Bun: typeof import("bun").Bun;

	// If you want to be extra explicit about env
	namespace Bun {
		const env: typeof import("bun").Bun.env;
	}
}

export {};