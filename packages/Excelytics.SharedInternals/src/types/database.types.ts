/**
 * Database-related types.
 * Used for MongoDB document structures across microservices.
 * @module database.types
 */
import { EnumFileProcessingStatus } from '../enums';
import type { FileMetadata } from './file.types';

/**
 * Base document interface for MongoDB documents.
 * Provides common fields for all database entities.
 */
export interface BaseDocument {
    _id?: string;
    createdAt: Date;
    updatedAt: Date;
    isDeleted?: boolean;
}

/**
 * User preferences for application settings.
 * Controls UI appearance and notification delivery.
 */
export interface UserPreferences {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: NotificationSettings;
}

/**
 * Settings for different notification channels.
 * Controls how users receive system notifications.
 */
export interface NotificationSettings {
    email: boolean;
    push: boolean;
    sms: boolean;
}

/**
 * Document representing a file stored in the database.
 * Contains metadata and processing status information.
 */
export interface FileDocument extends BaseDocument {
    userId: string;
    originalName: string;
    filename: string;
    mimetype: string;
    size: number;
    status: EnumFileProcessingStatus;
    processingStartedAt?: Date;
    processingCompletedAt?: Date;
    errorMessage?: string;
    metadata: FileMetadata;
}