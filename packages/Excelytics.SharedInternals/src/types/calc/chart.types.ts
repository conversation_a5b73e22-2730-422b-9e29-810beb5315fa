/**
 * Chart-related types.
 * Used for data visualization across the application.
 * @module chart.types
 */

/**
 * Represents chart data generated from file analysis.
 * Contains configuration and data for rendering visualizations.
 */
export interface ChartData {
    id: string;
    type: string;
    title: string;
    data: any; // Consider creating a more specific type
    options?: any; // Consider creating a more specific type
}