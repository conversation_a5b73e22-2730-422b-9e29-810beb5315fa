/**
 * Calculation-related types.
 * Used for processing and analyzing file data.
 * @module calc.types
 */
import type { ChartData } from './chart.types';

/**
 * Contains the results of calculations performed on file data.
 * Aggregates statistics, visualizations, and insights.
 */
export interface CalculationResults {
    summaryStats: SummaryStatistics;
    charts: ChartData[];
    insights: string[];
}

/**
 * Statistical summary of the analyzed data.
 * Provides overview of dataset characteristics.
 */
export interface SummaryStatistics {
    totalRows: number;
    totalColumns: number;
    numericColumns: string[];
    categoricalColumns: string[];
    missingDataPercentage: number;
}