/**
 * Mongoose-related types and interfaces.
 * Used for MongoDB operations and error handling across microservices.
 * @module mongoose.interface
 */

/**
 * Represents the structure of a MongoDB duplicate key error object.
 * This interface captures the specific properties returned by MongoDB when a unique constraint violation occurs (error code 11000).
 */
export interface MongoDuplicateKeyError extends Error {
    /** MongoDB error code 11000 indicates a duplicate key error */
    code: number;
    /** Identifies which index caused the duplicate key error (e.g. {email: 1}) */
    keyPattern: Record<string, number>;
    /** Contains the actual value that caused the duplicate key violation */
    keyValue: Record<string, unknown>;
}

/**
 * Represents the structure of a Mongoose CastError, specifically for ObjectId failures.
 * This occurs when a string provided for a field (like _id) cannot be converted into a valid MongoDB ObjectId.
 */
export interface MongoCastError extends Error {
    /** The name of the error, which will be 'CastError' */
    name: 'CastError';
    /** The type Mongoose was attempting to cast to, e.g., 'ObjectId' */
    kind: string;
    /** The invalid value that caused the casting failure */
    value: any;
    /** The path or field name that failed validation */
    path: string;
}