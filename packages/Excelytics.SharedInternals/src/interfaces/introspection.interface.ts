/**
 * Introspection-related types.
 * Used for token introspection and related operations across microservices.
 * @module introspection.types
 */

/**
 * Defines the structure of a successful response from an OAuth 2.0 Token Introspection endpoint, as specified by RFC 7662.
 *
 * This response provides metadata about a token, most importantly whether it is currently active.
 * If `active` is false, the token is invalid, and all other properties in the response are optional and should not be relied upon.
 *
 * @see {@link https://datatracker.ietf.org/doc/html/rfc7662}
 */
export interface IntrospectionResponse {
    /**
     * Indicates whether the token is currently active. This property is always present.
     * If `false`, the token has expired, been revoked, or is otherwise invalid.
     */
    active: boolean;

    /**
     * A space-separated list of scopes (permissions) associated with the token.
     * Defines what actions the token holder is authorized to perform.
     * @example "read:users write:reports"
     */
    scope?: string;

    /** The client identifier for the OAuth 2.0 client that requested the token. */
    client_id?: string;

    /**
     * A custom claim specific to Introspection Consulting, identifying the origin of the client request (e.g., 'web', 'mobile').
     * Corresponds to `EnumClientOrigin`.
     */
    client_origin?: number;

    /**
     * A custom claim specific to Introspection Consulting, identifying the path of the client request.
     * Corresponds to `EnumClientPath`.
     */
    client_path?: string;

    /**
     * The username of the authenticated user, typically their email address.
     * This is a human-readable identifier. It may not be present for tokens granted via the client credentials flow.
     */
    username?: string;

    /**
     * The type of the token, as defined in RFC 6749, section 7.1.
     * For Excelytics, this is typically "Bearer".
     * Corresponds to `EnumTokenType`.
     */
    token_type?: string;

    /**
     * Expiration time on or after which the token MUST NOT be accepted.
     * Represented as a NumericDate (seconds since the 1970-01-01T00:00:00Z UTC).
     */
    exp?: number;

    /**
     * Time at which the token was issued.
     * Represented as a NumericDate (seconds since the 1970-01-01T00:00:00Z UTC).
     */
    iat?: number;

    /** The issuer of the token. It's a URL that uniquely identifies the authorization server (e.g., your Introspection.Finance.Identity service). */
    iss?: string;

    /** Subject of the token. For tokens representing a user, this is typically a unique and non-reassignable identifier for that user (e.g., a user ID). */
    sub?: string;
}