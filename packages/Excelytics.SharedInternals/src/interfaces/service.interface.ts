/**
 * Service-related interfaces.
 * Used for defining service contracts and dependencies across microservices.
 * @module service.interface
 */
import type { ClientSession, UpdateQuery, FilterQuery } from 'mongoose';
import type { DocumentType } from '@typegoose/typegoose';
import type { GetQueryOptions } from '../types';

/**
 * Defines the contract for a Redis service.
 * This allows the RepositoryService to depend on an abstraction, not a
 * concrete implementation, making it more testable and flexible.
 */
export interface IRedisService {
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    exists(key: string): Promise<boolean>;
    setCache(key: string, value: any, ttl?: number): Promise<void>;
    getCache<T>(key: string): Promise<T | null>;
    deleteCache(key: string): Promise<void>;
}

/**
 *! Defines the contract for a generic data repository DAL (Data Access Layer).
 * This is the core interface for all direct database and cache interactions, including methods for transaction management.
 */
export interface IRepositoryService<T> {
    /**
     * Starts a new database session for transaction management.
     * @returns A promise that resolves to a Mongoose ClientSession.
     */
    startSession(): Promise<ClientSession>;

    /**
     * Creates a new document in the database.
     * @param entity The data for the new document.
     * @param options Optional parameters, including a session for transactions.
     * @returns The created Mongoose document.
     */
    create(entity: Partial<T>, options?: { session?: ClientSession }): Promise<DocumentType<T>>;

    /**
     * Finds and updates a document by its Id.
     * @param id The Id of the document to update.
     * @param entity The update payload.
     * @param options Optional parameters, including a session for transactions.
     * @returns The updated Mongoose document.
     */
    update(
        id: string,
        entity: UpdateQuery<T>,
        options?: { session?: ClientSession }
    ): Promise<DocumentType<T>>;

    /**
     * Finds and deletes a document by its Id.
     * @param id The Id of the document to delete.
     * @param options Optional parameters, including a session for transactions.
     */
    delete(id: string, options?: { session?: ClientSession }): Promise<void>;

    // --- Read methods remain unchanged as they don't participate in write transactions ---
    /** Retrieves a list of plain objects based on query options. */
    get(options: GetQueryOptions<T>): Promise<T[]>;

    /** Finds multiple documents matching a filter, returning full Mongoose documents. */
    find(filter: FilterQuery<T>): Promise<DocumentType<T>[]>;

    /** Retrieves a single full Mongoose document by its Id, or null if not found. */
    findById(id: string): Promise<DocumentType<T> | null>;

    /**
     * Finds a single document matching a filter, returning a full Mongoose document.
     * @param filter The filter criteria to apply.
     * @returns A promise that resolves to a single Mongoose document or null if not found.
     */
    findOne(filter: FilterQuery<T>): Promise<DocumentType<T> | null>;

    /** Checks if a document with the given Id exists. */
    existsById(id: string): Promise<boolean>;

    /** Counts the number of documents matching a filter. */
    count(filter?: FilterQuery<T>): Promise<number>;

    /**
     * Gets the model name for the entity this repository manages.
     * @returns The entity name as a string.
     */
    getEntityName(): string;
}

/**
 *! Defines the public contract for an advanced, transactional, and audited service.
 * This is the primary interface that controllers should depend on.
 *
 * It mandates the inclusion of an `actorId` for all write operations to ensure accountability and enables a rich, hook-based workflow internally.
 */
export interface IMutatorService<T> {
    /**
     * Orchestrates the complete, transactional creation of a new entity,
     * including validation, auditing, and lifecycle hooks.
     * @param entity The entity to be created.
     * @param actorId The ID of the user performing the action.
     * @returns A promise that resolves to the created entity as a plain object.
     */
    create(entity: T, actorId: string): Promise<T>;

    /**
     * Orchestrates the complete, transactional update of an existing entity.
     * @param id The ID of the entity to update.
     * @param entity The update payload.
     * @param actorId The ID of the user performing the action.
     * @returns A promise that resolves to the updated entity as a plain object.
     * Throws {NotFoundError} if the entity with the given ID does not exist.
     */
    update(id: string, entity: UpdateQuery<T>, actorId: string): Promise<T>;

    /**
     * Orchestrates the complete, transactional deletion of an existing entity.
     * @param id The ID of the entity to delete.
     * @param actorId The ID of the user performing the action.
     * Throws {NotFoundError} if the entity with the given ID does not exist.
     */
    delete(id: string, actorId: string): Promise<void>;

    // --- Read methods are also part of the public contract ---
    /** Retrieves a list of entities. */
    get(options: GetQueryOptions<T>): Promise<T[]>;

    /** Counts entities matching a filter. */
    count(filter?: FilterQuery<T>): Promise<number>;

    /**
     * Gets the model name for the entity this service manages.
     * @returns The entity name as a string.
     */
    getEntityName(): string;
}