/**
 * Global error handling middleware for Express applications.
 * Provides standardized error responses across all microservices.
 * @module error-handler.middleware
 */
import type { Request, Response, NextFunction } from '../config';
import {
	getErrorLoggingConfig,
	processErrorResponse,
	classifyError,
	logError
} from '../helpers';

/**
 * Express middleware that catches and formats all errors thrown during request processing.
 * Handles different error types (BaseError, ZodError, etc.) and returns appropriate responses.
 *
 * Complexity: Low (< 15 lines) - All logic extracted to helper functions
 * @param error - The error object caught by Express
 * @param request - Express request object
 * @param response - Express response object
 * @param next - Express next function (included for middleware signature)
 */
export const GlobalErrorHandler = (
	error: any,
	request: Request,
	response: Response,
	next: NextFunction
): void => {
	// 1. Get configuration set by environment and classify error
	const loggingConfig = getErrorLoggingConfig();
	const errorClassification = classifyError(error);

	// 2. Log the error based on configuration and error type
	logError(error, request, loggingConfig, errorClassification);

	// 3. Process error and generate the appropriate response
	const { statusCode, responseBody } = processErrorResponse(error, loggingConfig, errorClassification);

	// 4. Send response
	response.status(statusCode).json(responseBody);
};