/**
 * Health check middleware for Express applications.
 * Provides standardized health check endpoints for all microservices.
 * @module health-check.middleware
 */
import type { Request, Response, NextFunction } from '../config';
import { ErrorCodes, HttpStatus } from '../constants';
import { HealthCheckHelper } from '../helpers';
import { HealthStatus } from '../types';
import { BaseError } from '../errors';

/**
 * @factory createHealthCheckHandler
 * @description Creates an Express middleware function for handling health checks.
 * @param healthHelper An initialized instance of HealthCheckHelper.
 * @param deep If true, performs a deep health check. Defaults to false.
 * @returns An Express request handler.
 */
export const createHealthCheckHandler = (
	healthHelper: HealthCheckHelper,
	deep: boolean = false
) => {
	return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
		try {
			const healthResult = await (deep
				? healthHelper.getDeepHealth()
				: healthHelper.getHealth());

			const httpStatusCode =
                healthResult.status === HealthStatus.HEALTHY
                	? HttpStatus.OK
                	: HttpStatus.SERVICE_UNAVAILABLE;

			res.status(httpStatusCode).json({
				success: healthResult.status === HealthStatus.HEALTHY,
				message: `Health status: ${healthResult.status}`,
				data: healthResult
			});
		} catch (error: any) {
			next(
				new BaseError(
					'Health check data generation failed unexpectedly.',
					HttpStatus.INTERNAL_SERVER_ERROR,
					ErrorCodes.HEALTH_CHECK_FAILED,
					undefined,
					error
				)
			);
		}
	};
};