/**
 * Custom middleware for graceful server shutdown and process management.
 * @module custom.middleware
 */
import http from 'http';
import type { Mongoose } from 'mongoose';
import { env_ } from '../utils/env_internal';
import type { IRedisService } from '../interfaces';
import { MongooseStates, ProcessSignals, ServerConstants } from '../constants';

// A flag to ensure shutdown logic only runs once. Kept in the module scope.
let isShuttingDown = false;

/**
 * Initializes graceful shutdown handlers for the HTTP server and its dependencies.
 * @param server - HTTP server instance to gracefully close.
 * @param mongooseInstance - Mongoose instance to disconnect.
 * @param redisService - Optional Redis service instance to disconnect.
 * @param serviceName - Name of the service for logging purposes.
 */
export const InitializeGracefulShutdown = (
	server: http.Server,
	mongooseInstance: Mongoose,
	redisService?: IRedisService,
	serviceName: string = env_.SERVICE_NAME as string
) => {
	const shutdown = async (signal: string) => {
		if (isShuttingDown) {
			console.log(`Shutdown already in progress. Ignoring duplicate ${signal} signal.`);
			return;
		}
		isShuttingDown = true;
		console.log(`\n${signal} received. Starting graceful shutdown for ${serviceName}...`);

		const shutdownTimeout = setTimeout(() => {
			console.error(`[${serviceName}] Graceful shutdown timed out. Forcing exit.`);
			process.exit(1);
		}, 15000);

		// 1. Stop accepting new connections and trigger cleanup
		server.close(async (error?: Error & { code?: string }) => {
			if (error && error.code !== ServerConstants.SERVER_NOT_RUNNING) {
				console.error(`Error during ${serviceName} server shutdown:`, error);
			}
			console.log(`[${serviceName}] HTTP server closed.`);

			// 2. Close all other connections
			await closeConnections(mongooseInstance, redisService, serviceName);

			console.log(`[${serviceName}] Graceful shutdown complete. Exiting.`);
			clearTimeout(shutdownTimeout);
			process.exit(error ? 1 : 0);
		});
	};

	// 3. Attach the shutdown logic to the process events
	setupProcessEventListeners(shutdown, serviceName);
};

/**
 * Closes all active service connections (DB, Cache, etc.) in parallel.
 */
const closeConnections = async (
	mongooseInstance: Mongoose,
	redisService: IRedisService | undefined,
	serviceName: string
): Promise<void> => {
	const disconnectionPromises: Promise<any>[] = [];

	if (mongooseInstance.connection.readyState === MongooseStates.CONNECTED) {
		disconnectionPromises.push(mongooseInstance.disconnect());
	}
	if (redisService) {
		disconnectionPromises.push(redisService.disconnect());
	}

	try {
		await Promise.all(disconnectionPromises);
		console.log(`[${serviceName}] Database and Cache connections closed.`);
	} catch (error) {
		console.error(`[${serviceName}] Error during connection cleanup:`, error);
	}
};

/**
 * Sets up listeners for critical process events to ensure the application
 * doesn't exit unexpectedly without cleanup.
 */
const setupProcessEventListeners = (
	shutdownHandler: (signal: string) => void,
	serviceName: string
): void => {
	// Hot-Reloading Safety: Prevents attaching duplicate listeners.
	process.removeAllListeners(ProcessSignals.SIGNAL_TERMINATE);
	process.removeAllListeners(ProcessSignals.SIGNAL_INTERRUPT);

	process.on(ProcessSignals.SIGNAL_TERMINATE, () => shutdownHandler(ProcessSignals.SIGNAL_TERMINATE));
	process.on(ProcessSignals.SIGNAL_INTERRUPT, () => shutdownHandler(ProcessSignals.SIGNAL_INTERRUPT));

	process.on(ServerConstants.UNHANDLED_REJECTION, (reason, promise) => {
		console.error(`Unhandled Rejection in ${serviceName} at:`, promise, 'reason:', reason);
		process.exit(1);
	});

	process.on(ServerConstants.UNCAUGHT_EXCEPTION, (error) => {
		console.error(`Uncaught Exception in ${serviceName}:`, error);
		process.exit(1);
	});
};