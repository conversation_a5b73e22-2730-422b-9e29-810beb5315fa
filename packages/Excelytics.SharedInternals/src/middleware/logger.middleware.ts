/**
 * Logging middleware and utilities for Express applications.
 * Provides file-based logging capabilities for HTTP requests.
 * @module logger.middleware
 */
import path from 'path';
import fs from 'fs';

/**
 * Directory where log files will be stored.
 * Creates the directory if it doesn't exist.
 */
const logDirectory = path.join(process.cwd(), 'logs');

// Create the logs directory if it doesn't exist
if (!fs.existsSync(logDirectory)) {
	fs.mkdirSync(logDirectory);
}

/**
 * Write stream for HTTP access logs.
 * Used with morgan or other HTTP logging middleware.
 */
export const accessLogStream = fs.createWriteStream(
	path.join(logDirectory, 'access.log'),
	{ flags: 'a' } // Append flag, ensuring logs are not overwritten
);