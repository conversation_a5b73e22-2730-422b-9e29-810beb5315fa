/**
 * Microservice configuration.
 * Defines service discovery and connectivity information.
 * @module services.config
 */
import type { ServiceNameType } from '../types';
import { env_ } from '../utils/env_internal';
import { ServiceNames } from '../constants';

/**
 * Definition of a microservice in the Excelytics ecosystem.
 * Contains service name and connection URL.
 */
export interface MicroserviceDefinition {
    name: ServiceNameType;
    url: string | undefined;
}

/**
 * A centralized list of all microservices in the Excelytics ecosystem.
 * This is the single source of truth for service discovery in health checks.
 */
export const ALL_MICROSERVICES: MicroserviceDefinition[] = [
	{ name: ServiceNames.FINANCE, url: env_.FINANCE_SERVICE_URL },
	{ name: ServiceNames.CLIENT, url: env_.CLIENT_SERVICE_URL },
	{ name: ServiceNames.IDENTITY, url: env_.IDP_SERVICE_URL },
	{ name: ServiceNames.CALC, url: env_.CALC_SERVICE_URL }
];