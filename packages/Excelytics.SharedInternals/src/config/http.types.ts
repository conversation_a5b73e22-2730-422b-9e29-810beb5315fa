/**
 * This file augments the Express types to include our custom
 * authentication and authorization logic.
 * @module http.types
 */

// Import the payload type we want to attach to the request.
import type { AccessTokenPayload } from '../types';
// Import the original types from Express that we want to use or extend.
import type {
	Response as ExpressResponse,
	Request as ExpressRequest,
	NextFunction
} from 'express';

/**
 * An augmented version of the Express Request object that includes the authenticated user's payload.
 * Specific to introspection api architecture
 */
export interface AuthenticatedRequest extends ExpressRequest {
	/**
	 * The decoded payload of the JWT access token.
	 * This property is attached by the `authenticateRequest` middleware.
	 * It will be undefined on unauthenticated or public routes.
	 */
	user?: AccessTokenPayload;
}

/**
 * Re-export the types for convenience. We create a simple alias named "Request"
 * 		so that consuming services can import it easily.
 */
export type Request = AuthenticatedRequest;
export type Response = ExpressResponse;
export { type NextFunction };