/**
 * Token verification and decoding service.
 * @module token.service
 */
import jwt, { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { BaseError, UnauthorizedError } from '../errors';
import { ErrorCodes, HttpStatus } from '../constants';
import type { AccessTokenPayload } from '../types';
import { EnumTokenType } from '../enums';

/**
 * A service for handling JWT verification and decoding.
 * This should be instantiated in each backend service that needs to validate tokens.
 */
export class TokenService {
	private readonly jwtSecret: string;

	/**
     * Creates an instance of TokenService.
     * @param jwtSecret The secret key used to verify the JWT signature.
     * This MUST be the same secret used by the IdP to sign the token.
     */
	constructor(jwtSecret: string) {
		if (!jwtSecret) {
			throw new Error('JWT secret is required for TokenService.');
		}

		this.jwtSecret = jwtSecret;
	}

	/**
     * Verifies an access token's signature and decodes its payload.
     * It also validates that the token is specifically an ACCESS token.
     * @param token The JWT string to verify.
     * @returns The decoded AccessTokenPayload if the token is valid.
     * @throws { UnauthorizedError } if the token is expired, invalid, or not an access token.
     * @throws { BaseError } for other unexpected errors during verification.
     */
	public verifyAndDecodeAccessToken(token: string): AccessTokenPayload {
		try {
			// Verify the token using the secret
			const decoded = jwt.verify(token, this.jwtSecret) as AccessTokenPayload;

			// Security Check: Ensure this is an access token and not a refresh token
			if (decoded.tokenType !== EnumTokenType.ACCESS) {
				throw new UnauthorizedError(
					'Invalid token type provided. Expected access token.',
					ErrorCodes.INVALID_TOKEN
				);
			}

			return decoded;
		} catch (error) {
			if (error instanceof TokenExpiredError) {
				throw new UnauthorizedError(
					'Access token has expired.',
					ErrorCodes.TOKEN_EXPIRED,
					error
				);
			}

			if (error instanceof JsonWebTokenError) {
				throw new UnauthorizedError(
					'Invalid access token provided.',
					ErrorCodes.INVALID_TOKEN,
					error
				);
			}

			if (error instanceof Error) {
				throw error;
			}

			throw new BaseError(
				'An unexpected error occurred during access token verification.',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.TOKEN_VERIFICATION_FAILED,
				error
			);
		}
	}
}