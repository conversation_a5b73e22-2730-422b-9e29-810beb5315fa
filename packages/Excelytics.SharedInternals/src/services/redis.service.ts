/**
 * Redis service for caching and data storage.
 * @module redis.service
 */
import { ErrorCodes, HttpStatus, ServerConstants } from '../constants';
import Redis, { type RedisOptions } from 'ioredis';
import type { IRedisService } from '../interfaces';
import { env_ } from '../utils/env_internal';
import { BaseError } from '../errors';

/**
 * A robust, dependency-injection-friendly service for interacting with Redis.
 * This class manages the client lifecycle and provides structured error handling.
 * It is intended to be instantiated once per application (as a singleton managed by your DI setup) and shared across services.
 */
export class RedisService implements IRedisService {
	private static instance: RedisService;
	private readonly redisClient: Redis;

	/**
	 * Initializes the RedisService but does not connect immediately.
	 * Call the connect() method to establish the connection.
	 * Ensure your env file includes the REDIS_URI_UNRAID and REDIS_PORT variables.
	 */
	constructor() {
		const redisOptions: RedisOptions = {
			host: env_.REDIS_URI,
			port: env_.REDIS_PORT,
			db: env_.REDIS_DB,

			// Recommended options for production
			maxRetriesPerRequest: 3,
			enableReadyCheck: true,
			lazyConnect: true, // IMPORTANT: We will connect explicitly
		};

		this.redisClient = new Redis(redisOptions);

		// This handles background errors after connection is established
		this.redisClient.on(ServerConstants.ERROR, (err) => {
			console.error('[Redis] Background Error:', err);
		});

		this.redisClient.on(ServerConstants.CONNECT, () => {
			console.log(`[Redis] Connection established to ${redisOptions.host}:${redisOptions.port}`);
		});
	}

	/**
	 * Establishes the connection to the Redis server.
	 * This should be called during application startup.
	 */
	public async connect(): Promise<void> {
		try {
			// The connect method is specific to ioredis when lazyConnect is true
			await this.redisClient.connect();
			console.log('[Redis] Service connected successfully.');
		} catch (error) {
			console.error('[Redis] Failed to connect to Redis:', error);

			// Propagate the error to prevent the app from starting in a bad state
			throw new BaseError(
				'Failed to connect to Redis',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.EXTERNAL_SERVICE_ERROR,
				null,
				error
			);
		}
	}

	/**
	 * Disconnects from the Redis server.
	 * This should be called during graceful application shutdown.
	 */
	public async disconnect(): Promise<void> {
		await this.redisClient.quit();
		console.log('[Redis] Service disconnected.');
	}

	/**
	 * Checks if a key exists in Redis.
	 * Returns True if the key exists, false otherwise.
	 * @param key The key to check.
	 */
	public async exists(key: string): Promise<boolean> {
		try {
			const result = await this.redisClient.exists(key);
			return result === 1;
		} catch (error) {
			// In case of error, safer to assume cache doesn't exist
			console.error(`[Redis] Error checking existence for key "${key}":`, error);
			return false;
		}
	}

	/**
	 * Sets a value in the cache with a default TTL.
	 * @param key The cache key.
	 * @param value The value to store (will be JSON stringified).
	 * @param ttl Time-to-live in seconds. Defaults to 10 minutes (600s).
	 */
	public async setCache(key: string, value: any, ttl: number = 600): Promise<void> {
		try {
			const stringifiedValue = JSON.stringify(value);
			await this.redisClient.set(key, stringifiedValue, 'EX', ttl);
		} catch (error) {
			// A cache-write failure should not crash the request.
			console.error(`[Redis] Failed to set cache for key "${key}":`, error);
		}
	}

	/**
	 * Retrieves and parses a value from the cache.
	 * @param key The cache key.
	 * @returns The parsed value, or null if not found or on error.
	 */
	public async getCache<T>(key: string): Promise<T | null> {
		try {
			const data = await this.redisClient.get(key);
			return data ? (JSON.parse(data) as T) : null;
		} catch (error) {
			console.error(`[Redis] Failed to get/parse cache for key "${key}":`, error);

			// If data is corrupted or a Redis error occurs, return null.
			return null;
		}
	}

	/**
	 * Deletes a key from the cache.
	 * @param key The cache key.
	 */
	public async deleteCache(key: string): Promise<void> {
		try {
			await this.redisClient.del(key);
		} catch (error) {
			console.error(`[Redis] Failed to delete cache for key "${key}":`, error);
		}
	}

	/**
	 * Retrieves the singleton instance of the RedisService.
	 * This is the ONLY way to get an instance of this class.
	 */
	public static getInstance(): RedisService {
		if (!RedisService.instance) {
			RedisService.instance = new RedisService();
		}
		return RedisService.instance;
	}

	/**
	 * Provides direct access to the underlying ioredis client for advanced use cases.
	 * @returns The ioredis client instance.
	 */
	public getClient(): Redis {
		return this.redisClient;
	}
}