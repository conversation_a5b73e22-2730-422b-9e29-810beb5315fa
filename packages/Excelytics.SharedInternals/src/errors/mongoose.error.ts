/**
 * Mongoose-specific error handling.
 * Provides type guards for common Mongoose errors.
 * @module mongoose.error
 */
import { MongoCastError, MongoDuplicateKeyError } from '../interfaces';

/**
 * Type guard to check if an error is a MongoDB duplicate key error.
 * This function safely narrows the type, of an unknown error to MongoDuplicateKeyError when the error has the distinctive properties of a MongoDB duplicate key error.
 *
 * @param error The caught error, of type 'unknown'.
 * @returns True if the error is a MongoDuplicateKeyError with code 11000.
 */
export function isMongoDuplicateKeyError(
	error: unknown
): error is MongoDuplicateKeyError {
	return (
		typeof error === "object"
        && error !== null
        && "code" in error
        && (error as { code: unknown }).code === 11000
	);
}

/**
 * Type guard to check if an error is a Mongoose CastError for an ObjectId.
 * This function safely narrows the type of, an unknown error to MongoCastError.
 *
 * @param error The caught error, of type 'unknown'.
 * @returns True if the error is a CastError for an ObjectId.
 */
export function isMongoCastError(error: unknown): error is MongoCastError {
	return (
		typeof error === 'object'
        && error !== null
        && 'name' in error
        && (error as { name: unknown }).name === 'CastError'
        && 'kind' in error
        && (error as { kind: unknown }).kind === 'ObjectId'
	);
}