/**
 * Custom error for validation failures.
 * @module validation.error
 */
import { HttpStatus, ErrorCodes } from '../constants';
import { BaseError, type ErrorDetails } from './base.error';

/**
 * Custom error for validation failures.
 */
export class ValidationError extends BaseError {
	constructor(
		message: string = 'Validation failed',
		details?: ErrorDetails | any,
		originalZodError?: any
	) {
		super(
			message,
			HttpStatus.BAD_REQUEST,     // 400
			ErrorCodes.VALIDATION_ERROR,
			details,
			originalZodError
		);
		this.name = 'ValidationError';
	}
}