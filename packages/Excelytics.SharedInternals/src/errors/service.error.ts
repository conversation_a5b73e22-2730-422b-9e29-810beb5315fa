/**
 * Custom error for failures in inter-service communication.
 * @module service.error
 */
import { BaseError, type ErrorCode, type HttpStatusCode } from './base.error';
import { ErrorCodes, HttpStatus } from '../constants';

/**
 * @class ServiceError
 * @extends BaseError
 * @description A custom error for failures in inter-service communication.
 * It extends BaseError to be handled consistently by the GlobalErrorHandler.
 */
export class ServiceError extends BaseError {
	constructor(
		message: string,
		statusCode: HttpStatusCode = HttpStatus.SERVICE_UNAVAILABLE, // 503 is a good default
		errorCode: ErrorCode = ErrorCodes.EXTERNAL_SERVICE_ERROR,
		details?: any,
		originalError?: Error | any
	) {
		super(message, statusCode, errorCode, details, originalError);
		this.name = 'ServiceError';
	}
}