/**
 * Custom base error class for standardized error handling across services.
 * @module base.error
 */
import { HttpStatus, ErrorCodes } from '../constants';
import type { ApiErrorObject } from '../types';

/** Interface for optional structured error details. */
export interface ErrorDetails {
    [key: string]: any;
}

/** Type representing valid HTTP status codes. */
export type HttpStatusCode = (typeof HttpStatus)[keyof typeof HttpStatus];

/** Type representing valid error codes. */
export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes];

/**
 * Custom base error class for standardized error handling across services.
 * Includes HTTP Status Code, specific Error Code, and optional details.
 */
export class BaseError extends Error {
	public readonly statusCode: HttpStatusCode;     // (derived type)
	public readonly errorCode: ErrorCode;           // (derived type)
	public readonly details?: ErrorDetails | any;   // Allow for flexible details
	public readonly originalError?: Error | any;    // To store the original caught error for logging

	/**
     * @param message Human-readable error message
     * @param statusCode HTTP status code associated with this error
     * @param errorCode Specific error code string from ErrorCodes
     * @param details Optional structured details about the error
     * @param originalError Optional original error that was caught
     */
	constructor(
		message: string,                    // The human-readable error message
		statusCode: HttpStatusCode,         // The HTTP status code associated with this error (derived type)
		errorCode: ErrorCode,               // A specific error code string (from ErrorCodes)  (derived type)
		details?: ErrorDetails | any,       // Optional structured details about the error
		originalError?: Error | any         // Optional original error that was caught
	) {
		// Call the parent Error constructor
		super(message);

		this.name = this.constructor.name;  // Set the error name to the class name
		this.statusCode = statusCode;
		this.errorCode = errorCode;
		this.details = details;
		this.originalError = originalError;

		// This line is important for proper stack trace in V8 environments (Node.js, Chrome)
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, this.constructor);
		}
	}

	/**
     * Converts this BaseError instance into the error structure expected by ApiResponse.
     * @returns An object containing the error code, message, and optional details.
     */
	public toApiResponseError(): ApiErrorObject {
		return {
			code: this.errorCode,
			message: this.message,
			details: this.details
		};
	}
}