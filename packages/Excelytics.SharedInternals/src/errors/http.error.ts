/**
 * Custom errors for HTTP status codes.
 * @module http.error
 */
import { BaseError, type ErrorCode, type ErrorDetails } from './base.error';
import { HttpStatus, ErrorCodes } from '../constants';

// --- 4xx Client Errors ---

/**
 * Use when the server cannot or will not process the request due to something that is perceived to be a client error
 * (e.g., malformed request syntax, invalid request message framing, or deceptive request routing).
 * Ideal for Zod validation failures.
 */
export class BadRequestError extends BaseError {
	constructor(
		message: string = 'Bad Request',
		errorCode: ErrorCode = ErrorCodes.BAD_REQUEST, // 404
		details?: ErrorDetails,
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.BAD_REQUEST,
			errorCode,
			details,
			originalError
		);

		this.name = 'BadRequestError';
	}
}

/**
 * Use when authentication is required and has failed or has not yet
 * been provided. The user is not authenticated.
 */
export class UnauthorizedError extends BaseError {
	constructor(
		message: string = 'Authentication required or failed',
		errorCode: ErrorCode = ErrorCodes.UNAUTHORIZED, // 401
		originalError?: unknown
	) {
		super(
			message,
			HttpStatus.UNAUTHORIZED,
			errorCode,
			undefined, // No specific details by default
			originalError
		);

		this.name = 'UnauthorizedError';
	}
}

/**
 * Use when the server understands the request but refuses to authorize it.
 * The user is authenticated, but they do not have permission for the action.
 */
export class ForbiddenError extends BaseError {
	constructor(
		message: string = 'Access to this resource is forbidden',
		errorCode: ErrorCode = ErrorCodes.FORBIDDEN, // 403
		originalError?: unknown
	) {
		super(
			message,
			HttpStatus.FORBIDDEN,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'ForbiddenError';
	}
}

/** Use when the server cannot find the requested resource. */
export class NotFoundError extends BaseError {
	constructor(
		message: string = 'Resource not found',
		resourceName?: string
	) {
		const details: ErrorDetails | undefined = resourceName
			? { resource: resourceName }
			: undefined;

		super(
			message,
			HttpStatus.NOT_FOUND, // 404
			ErrorCodes.NOT_FOUND,
			details
		);

		this.name = 'NotFoundError';
	}
}

/**
 * Use when a request conflicts with the current state of the server.
 * Common use case: Trying to create a resource that already exists (e.g., registering a user with an email that is already taken).
 */
export class ConflictError extends BaseError {
	constructor(
		message: string = 'Resource conflict',
		errorCode: ErrorCode = ErrorCodes.CONFLICT, // 409
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.CONFLICT,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'ConflictError';
	}
}

/**
 * Use when the server understands the content type of the request entity, and the syntax of the request entity is correct,
 * but it was unable to process the contained instructions.
 * Example: An Excel file is uploaded and parsed correctly, but it's missing a required "Revenue" column for a specific calculation.
 */
export class UnprocessableEntityError extends BaseError {
	constructor(
		message: string = 'Unprocessable Entity',
		errorCode: ErrorCode = ErrorCodes.UNPROCESSABLE_ENTITY, // 422
		details?: ErrorDetails,
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.UNPROCESSABLE_ENTITY,
			errorCode,
			details,
			originalError,
		);

		this.name = 'UnprocessableEntityError';
	}
}

// --- 5xx Server Errors ---

/**
 * Use when one server on the internet received an invalid response from another server.
 * Perfect for our microservices: `Introspection.Finance` would throw this if `Introspection.Calc` returns an unexpected error or fails to respond.
 */
export class BadGatewayError extends BaseError {
	constructor(
		message: string = 'Bad Gateway',
		errorCode: ErrorCode = ErrorCodes.BAD_GATEWAY, // 502
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.BAD_GATEWAY,
			errorCode,
			undefined,
			originalError
		);

		this.name = 'BadGatewayError';
	}
}

/**
 * Use when the server is not ready to handle the request. Common causes are a server that is down for maintenance or that is overloaded.
 * Perfect for when a critical dependency like your MongoDB or Redis instance on Unraid is unreachable.
 */
export class ServiceUnavailableError extends BaseError {
	constructor(
		message: string = 'Service Unavailable',
		errorCode: ErrorCode = ErrorCodes.SERVICE_UNAVAILABLE, // 503
		originalError?: unknown,
	) {
		super(
			message,
			HttpStatus.SERVICE_UNAVAILABLE,
			errorCode,
			undefined,
			originalError,
		);

		this.name = 'ServiceUnavailableError';
	}
}