/**
 * Standardized API success response handling for Excelytics microservices.
 * Provides consistent success response formatting across all services that consume this shared package.
 * @module api-success.notifications
 */
import type { Response } from '../config';
import { HttpStatus } from '../constants';
import type { SuccessResponse, TokenResponse } from '../types';

/**
 * Handles formatting and sending standardized success responses for REST APIs.
 * Supports various HTTP status codes and response types including token responses.
 */
export class CustomSuccessResponse {
	/**
     * Sends a generic success response
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Data payload to include in response
     * @param statusCode - HTTP status code (defaults to 200 OK)
     * @returns Express response with formatted success data
     */
	SendSuccessResponse<TData = any>(
		response: Response,
		message: string = 'Operation successful',
		data: TData,
		statusCode: number = HttpStatus.OK
	): Response {
		const successResponse: SuccessResponse<TData> = {
			success: true,
			message,
			data
		};

		return response.status(statusCode).json(successResponse);
	}

	/**
     * Sends a success response specifically for token-related operations
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Token data including access and refresh tokens
     * @param statusCode - HTTP status code (defaults to 200 OK)
     * @returns Express response with formatted token response
     */
	SendTokenSuccessResponse(
		response: Response,
		message: string = 'Token operation successful',
		data: TokenResponse,
		statusCode: number = HttpStatus.OK
	): Response {
		const successResponse: SuccessResponse<TokenResponse> = {
			success: true,
			message,
			data
		};

		return response.status(statusCode).json(successResponse);
	}

	/**
     * Sends a 201 Created success response indicating a resource was created
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Data payload of the created resource
     * @returns Express response with 201 status and resource data
     */
	public SendResourceCreatedResponse<TData = any>(
		response: Response,
		message: string = 'Resource created successfully',
		data: TData
	): Response {
		return this.SendSuccessResponse(response, message, data, HttpStatus.CREATED);
	}

	/**
     * Sends a 202 Accepted response indicating an operation was accepted for processing
     * @param response - Express response object
     * @param message - Success message to include in response
     * @param data - Optional data payload related to the accepted request
     * @returns Express response with 202 status
     */
	public SendAcceptedResponse<TData = any>(
		response: Response,
		message: string = 'Request accepted for processing',
		data?: TData // Data is optional for 202
	): Response {
		const successResponse: SuccessResponse<TData | undefined> = {
			success: true,
			message,
			data
		};

		return response.status(HttpStatus.ACCEPTED).json(successResponse);
	}

	/**
     * Sends a 204 No Content response indicating successful operation with no response body
     * @param response - Express response object
     * @returns Express response with 204 status and no content
     */
	public SendNoContentResponse(response: Response): Response {
		return response.status(HttpStatus.NO_CONTENT).send();
	}
}