{"name": "excelytics.shared-internals", "version": "0.2.8", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "excelytics.shared-internals", "version": "0.2.8", "license": "ISC", "dependencies": {"express": "^5.1.0", "zod": "^3.25.46"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.29", "bun": "^1.2.15", "bun-types": "^1.2.15", "typescript": "^5.8.3"}}, "node_modules/@oven/bun-darwin-aarch64": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-darwin-aarch64/-/bun-darwin-aarch64-1.2.15.tgz", "integrity": "sha1-mdta9ymLpXb2kqwj2qDtaIF5/nE=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@oven/bun-darwin-x64": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-darwin-x64/-/bun-darwin-x64-1.2.15.tgz", "integrity": "sha1-pn2IAiAIponMhInmb9bBaPkSjbY=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@oven/bun-darwin-x64-baseline": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-darwin-x64-baseline/-/bun-darwin-x64-baseline-1.2.15.tgz", "integrity": "sha1-TZpmKYxrshW/ha6Ku1Y15nldjCs=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@oven/bun-linux-aarch64": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-aarch64/-/bun-linux-aarch64-1.2.15.tgz", "integrity": "sha1-MtF7RzIrV/ttlIIIxe97jXcQW90=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-linux-aarch64-musl": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-aarch64-musl/-/bun-linux-aarch64-musl-1.2.15.tgz", "integrity": "sha1-9ezpvb/gvzJfjh8RHycpTMHPAI0=", "cpu": ["aarch64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-linux-x64": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-x64/-/bun-linux-x64-1.2.15.tgz", "integrity": "sha1-7GscVTEEDQ4NN9UnW5Hj38+hzOg=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-linux-x64-baseline": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-x64-baseline/-/bun-linux-x64-baseline-1.2.15.tgz", "integrity": "sha1-DHVbsfRxFssmCOmR+vCOzYd+hZk=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-linux-x64-musl": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-x64-musl/-/bun-linux-x64-musl-1.2.15.tgz", "integrity": "sha1-ShEHlqT0QEhZhKTrUT9uWhczIkA=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-linux-x64-musl-baseline": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-linux-x64-musl-baseline/-/bun-linux-x64-musl-baseline-1.2.15.tgz", "integrity": "sha1-kYoM8oq1NDz2oPO9iVAs0/TIJBM=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@oven/bun-windows-x64": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-windows-x64/-/bun-windows-x64-1.2.15.tgz", "integrity": "sha1-Atq7i4AKjwbZ6sNpVg/cs9Ye7T4=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@oven/bun-windows-x64-baseline": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@oven/bun-windows-x64-baseline/-/bun-windows-x64-baseline-1.2.15.tgz", "integrity": "sha1-J1thRyWMs1jlQliLDTUXBU41k5A=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@types/body-parser": {"version": "1.19.5", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/body-parser/-/body-parser-1.19.5.tgz", "integrity": "sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/express": {"version": "5.0.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/express/-/express-5.0.2.tgz", "integrity": "sha1-e+njN6V0XWtD71sMNS2tlKfwwlY=", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.6", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "integrity": "sha1-Qf7E6iDpx7IvAkq4ipXGuyiPUbg=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/http-errors/-/http-errors-2.0.4.tgz", "integrity": "sha1-frR3JsORtzRabsNa1/TeRpz1uk8=", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.29", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/send/-/send-0.17.4.tgz", "integrity": "sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/@types/serve-static/-/serve-static-1.15.7.tgz", "integrity": "sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/accepts": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/accepts/-/accepts-2.0.0.tgz", "integrity": "sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/bun": {"version": "1.2.15", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/bun/-/bun-1.2.15.tgz", "integrity": "sha1-CMg+mVnk75ox2O/VRNro/uQKPMM=", "cpu": ["arm64", "x64", "aarch64"], "dev": true, "hasInstallScript": true, "license": "MIT", "os": ["darwin", "linux", "win32"], "bin": {"bun": "bin/bun.exe", "bunx": "bin/bun.exe"}, "optionalDependencies": {"@oven/bun-darwin-aarch64": "1.2.15", "@oven/bun-darwin-x64": "1.2.15", "@oven/bun-darwin-x64-baseline": "1.2.15", "@oven/bun-linux-aarch64": "1.2.15", "@oven/bun-linux-aarch64-musl": "1.2.15", "@oven/bun-linux-x64": "1.2.15", "@oven/bun-linux-x64-baseline": "1.2.15", "@oven/bun-linux-x64-musl": "1.2.15", "@oven/bun-linux-x64-musl-baseline": "1.2.15", "@oven/bun-windows-x64": "1.2.15", "@oven/bun-windows-x64-baseline": "1.2.15"}}, "node_modules/bun-types": {"version": "1.2.15", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/content-type/-/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/cookie/-/cookie-0.7.2.tgz", "integrity": "sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/debug/-/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express": {"version": "5.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/express/-/express-5.1.0.tgz", "integrity": "sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/fresh/-/fresh-2.0.0.tgz", "integrity": "sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=", "license": "MIT"}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha1-ardLjy0zIPIGSyqHo455Mf86VWE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mime-db": {"version": "1.54.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/negotiator": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/qs/-/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/router": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/router/-/router-2.2.0.tgz", "integrity": "sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/send": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/send/-/send-1.2.0.tgz", "integrity": "sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/type-is": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/type-is/-/type-is-2.0.1.tgz", "integrity": "sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/typescript/-/typescript-5.8.3.tgz", "integrity": "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.21.0", "dev": true, "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/zod": {"version": "3.25.46", "resolved": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/zod/-/zod-3.25.46.tgz", "integrity": "sha1-qHYdGI5JK4ab+0+FW/D3FtVNrxk=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}