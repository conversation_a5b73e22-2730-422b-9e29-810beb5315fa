/**
 * @module environments
 * @description
 * This module provides Zod schemas for validating environment variables across all Introspection Consulting microservices.
 *
 * The architecture uses a `BaseEnvironmentSchema` for common configuration and service-specific schemas (e.g., `IdpEnvironmentSchema`) that extend the base.
 * USAGE: In each microservice, import the specific schema you need and parse the environment at the start of your application.
 */
import { z } from 'zod';
import { EnumApiVersion, EnumEnv, EnumLogLevel } from '../enums';

/**
 * Zod schema for validating environment variables.
 * Provides default values and type coercion for configuration settings.
 */
const BaseFieldsSchema = z.object({
	// --- Service Identity ---
	/** The current application environment (e.g., 'development', 'production'). */
	ENV: z
		.preprocess(
			() => process.env.NODE_ENV || process.env.ENV,
			z.nativeEnum(EnumEnv),
		)
		.default(EnumEnv.Development),
	/** The unique name of the current service (e.g., 'excelytics.finance'). */
	SERVICE_NAME: z.string(),
	/** The semantic version of the service. */
	VERSION: z.string().default('1.0.0'),
	/** The API version the service exposes. */
	API_VERSION: z.nativeEnum(EnumApiVersion).default(EnumApiVersion.V1),

	// --- Network & Logging ---
	/** The port number for the service to listen on. No default to prevent conflicts. */
	PORT: z.coerce.number().int().positive(),
	/** The level of detail for application logs. */
	LOG_LEVEL: z.nativeEnum(EnumLogLevel).default(EnumLogLevel.Info),
	/** Flag to indicate if the service is running in a VPN-enabled environment. */
	VPN: z.preprocess(
		(val) => String(val).toLowerCase() === 'true',
		z.boolean(),
	).default(false),

	// --- Database & Redis (Raw Components) ---
	// We define components and build the final URI in the transform step.
	/** The username for the MongoDB connection. */
	MONGO_USER: z.string(),
	/** The password for the MongoDB connection. */
	MONGO_PASS: z.string(),
	/** The host for the MongoDB connection. */
	MONGO_HOST: z.string(),
	/** The port for the MongoDB connection. */
	MONGO_PORT: z.coerce.number().default(27017),
	/** The database name for the MongoDB connection. */
	MONGO_DB_NAME: z.string(),
	/** The authentication source for the MongoDB connection. */
	MONGO_AUTH_SOURCE: z.string().optional(),
	/** The host for the Redis connection. */
	REDIS_HOST: z.string(),
	/** The port for the Redis connection. */
	REDIS_PORT: z.coerce.number().default(6379),
	/** The password for the Redis connection. */
	REDIS_PASSWORD: z.string().optional(),

	/** Tailscale IP address for private network communication */
	TAILSCALE_IP: z.string().ip({ version: 'v4' }).default('********').optional(),
	/** Redis server IP address on Unraid */
	REDIS_URI_UNRAID: z.string().ip({ version: 'v4' }).default('********').optional(),

	// DATABASE CONFIGURATION
	MONGODB_URI_UNRAID: z.string().optional(),
	MONGODB_URI_LOCAL: z.string().optional(),

	// MICROSERVICE URLs
	/** URL for the Identity Provider service */
	IDP_SERVICE_URL: z.string().url().default('http://localhost:6002'),
	/** URL for the Calculation service */
	CALC_SERVICE_URL: z.string().url().default('http://localhost:6003'),
	/** URL for the Client service */
	CLIENT_SERVICE_URL: z.string().url().default('http://localhost:4200'),
	/** URL for the Finance service */
	FINANCE_SERVICE_URL: z.string().url().default('http://localhost:6001')
});

// --- 2. Reusable Transformation ---
// This function will be applied to each final schema.
export const InternalMongoUriTransform = (data: z.infer<typeof BaseFieldsSchema>) => {
	const user = encodeURIComponent(data.MONGO_USER);
	const pass = encodeURIComponent(data.MONGO_PASS);
	const authSource = data.MONGO_AUTH_SOURCE
		? `?authSource=${data.MONGO_AUTH_SOURCE}`
		: "";
	return {
		...data,
		MONGODB_URI: `mongodb://${user}:${pass}@${data.MONGO_HOST}:${data.MONGO_PORT}/${data.MONGO_DB_NAME}${authSource}`,
	};
};

/** Environment schema for the Finance service, do not export as parsed. */
export const ClientEnvironmentSchema = BaseFieldsSchema.extend({})
	.transform(InternalMongoUriTransform);

export const BaseEnvironmentSchema = BaseFieldsSchema;

// ❌ Remove these exports (they cause the Bun error):
// export const env_ = BaseFieldsSchema.parse(Bun.env);
// export const env_client = ClientEnvironmentSchema.parse(Bun.env);