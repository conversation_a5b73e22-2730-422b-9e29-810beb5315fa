import { generateTree, getScriptFlags, IGNORE_LIST } from './file-structure-creator';
import { execSync } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import {
	MapStructureOptions,
	CODE_EXTENSIONS,
	ProjectMetrics,
	ScriptFlags
} from './script.types';

async function countLinesInFile(filePath: string): Promise<number> {
	try {
		const content = await fs.readFile(filePath, 'utf-8');
		return content.split('\n').length;
	} catch {
		return 0;
	}
}

function formatFileSize(bytes: number): string {
	const units = ['B', 'KB', 'MB', 'GB'];
	let size = bytes;
	let unitIndex = 0;

	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}

	return `${size.toFixed(1)} ${units[unitIndex]}`;
}

async function getGitInfo(rootPath: string): Promise<string> {
	try {
		const branch = execSync('git rev-parse --abbrev-ref HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		const commit = execSync('git rev-parse --short HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		const commitCount = execSync('git rev-list --count HEAD',
			{ cwd: rootPath, encoding: 'utf8' }).trim();
		return `**Branch:** ${branch} | **Commit:** ${commit} | **Total Commits:** ${commitCount}`;
	} catch {
		return 'Git information not available';
	}
}

async function analyzePackageJson(rootPath: string): Promise<string> {
	try {
		const packagePath = path.join(rootPath, 'package.json');
		const content = await fs.readFile(packagePath, 'utf-8');
		const pkg = JSON.parse(content);

		const deps = Object.keys(pkg.dependencies || {}).length;
		const devDeps = Object.keys(pkg.devDependencies || {}).length;

		return `**Dependencies:** ${deps} | **Dev Dependencies:** ${devDeps} | **Version:** ${pkg.version || 'N/A'}`;
	} catch {
		return 'Package.json not found or invalid';
	}
}

async function collectMetrics(
	directoryPath: string,
	flags: ScriptFlags,
	metrics: ProjectMetrics
): Promise<void> {
	try {
		const files = await fs.readdir(directoryPath);
		const filteredFiles = files.filter((file) => !IGNORE_LIST.has(file));

		for (const file of filteredFiles) {
			const filePath = path.join(directoryPath, file);
			const stats = await fs.stat(filePath);

			if (stats.isDirectory()) {
				metrics.totalDirectories++;
				await collectMetrics(filePath, flags, metrics);
			} else {
				metrics.totalFiles++;
				metrics.totalSize += stats.size;

				// File extension analysis
				const ext = path.extname(file).toLowerCase();
				metrics.filesByExtension.set(ext, (metrics.filesByExtension.get(ext) || 0) + 1);

				// Lines of code counting
				if (CODE_EXTENSIONS.has(ext)) {
					const lines = await countLinesInFile(filePath);
					metrics.linesOfCode += lines;
					metrics.locByExtension.set(ext, (metrics.locByExtension.get(ext) || 0) + lines);
				}

				// Track largest files
				metrics.largestFiles.push({
					name: file,
					size: stats.size,
					path: path.relative(process.cwd(), filePath)
				});

				// Track file ages
				metrics.oldestFiles.push({
					name: file,
					modified: stats.mtime,
					path: path.relative(process.cwd(), filePath)
				});

				metrics.newestFiles.push({
					name: file,
					modified: stats.mtime,
					path: path.relative(process.cwd(), filePath)
				});
			}
		}
	} catch (error) {
		console.error(`Error analyzing directory ${directoryPath}:`, error);
	}
}


/**
 * Creates a Markdown file representing the structure of a given directory.
 * This is the core library function, decoupled from the command line.
 *
 * @param rootPath The absolute path to the directory to map.
 * @param options Configuration options for generating the map.
 * @returns A promise that resolves when the file has been written.
 */
export async function createSonnetFileStructureMap(
	rootPath: string,
	options: MapStructureOptions = {}
) {

	// 2. Set up flags for the generateTree function based on options
	const flags = await getScriptFlags(options, rootPath);

	// Initialize metrics
	const metrics: ProjectMetrics = {
		totalFiles: 0,
		totalDirectories: 0,
		totalSize: 0,
		linesOfCode: 0,
		filesByExtension: new Map(),
		locByExtension: new Map(),
		largestFiles: [],
		oldestFiles: [],
		newestFiles: []
	};

	// Collect metrics
	console.log("📈 Analyzing project metrics...");
	await collectMetrics(rootPath, flags, metrics);

	// Sort and limit arrays
	metrics.largestFiles.sort((a, b) => b.size - a.size).splice(10);
	metrics.oldestFiles.sort((a, b) => a.modified.getTime() - b.modified.getTime()).splice(10);
	metrics.newestFiles.sort((a, b) => b.modified.getTime() - a.modified.getTime()).splice(10);

	// Generate tree structure
	console.log("🌳 Generating directory tree...");
	const treeStructure = await generateTree(rootPath, flags);
	const rootFolderName = path.basename(rootPath);
	const finalTree = `${rootFolderName}/\n${treeStructure}`;

	// Get additional info
	const gitInfo = await getGitInfo(rootPath);
	const packageInfo = await analyzePackageJson(rootPath);

	// Create output
	const date = new Date();
	const dateString = date.toISOString().slice(0, 10);
	const outputFileName = `${dateString}_${rootFolderName}_structure_deep.md`;

	// Build file extension table
	const extensionTable = Array.from(metrics.filesByExtension.entries())
		.sort((a, b) => b[1] - a[1])
		.slice(0, 15)
		.map(([ext, count]) => {
			const loc = metrics.locByExtension.get(ext) || 0;
			return `| ${ext || 'no ext'} | ${count} | ${loc.toLocaleString()} |`;
		})
		.join('\n');

	// Build largest files table
	const largestFilesTable = metrics.largestFiles
		.slice(0, 10)
		.map(file => `| ${file.name} | ${formatFileSize(file.size)} | ${file.path} |`)
		.join('\n');

	// Create enhanced markdown content
	const markdownContent = `# 📊 Project Analysis: ${rootFolderName}

## 📋 Summary
- **Generated:** ${date.toUTCString()}
- **Analysis Duration:** ${Date.now() - date.getTime()}ms
- **Root Path:** \`${rootPath}\`

## 🔍 Git Information
${gitInfo}

## 📦 Package Information  
${packageInfo}

## 📈 Project Metrics

### 📊 Overview
- **Total Files:** ${metrics.totalFiles.toLocaleString()}
- **Total Directories:** ${metrics.totalDirectories.toLocaleString()}
- **Total Size:** ${formatFileSize(metrics.totalSize)}
- **Lines of Code:** ${metrics.linesOfCode.toLocaleString()}

### 📋 File Distribution
| Extension | Files | Lines of Code |
|-----------|-------|---------------|
${extensionTable}

### 📂 Largest Files
| File | Size | Path |
|------|------|------|
${largestFilesTable}

### 🕐 Recently Modified Files
| File | Modified | Path |
|------|----------|------|
${metrics.newestFiles.slice(0, 5).map(file =>
		`| ${file.name} | ${file.modified.toLocaleDateString()} | ${file.path} |`
	).join('\n')}

## 🌳 Directory Structure
\`\`\`
${finalTree}
\`\`\`

## 🏗️ Architecture Notes
This is part of the **Introspection Consulting** microservices architecture:
- **Backend (BE):** Introspection.Finance
- **Identity Provider (IdP):** Introspection.Finance.Identity  
- **Calculation Engine:** Introspection.Finance.Calc
- **Frontend (FE):** Introspection.Finance.Client

**Tech Stack:** MongoDB, TypeScript, Shadcn/ui, Bun
**Deployment:** Unraid Server with Nginx Gateway

---
*Generated by Enhanced Project Structure Analyzer*
`;

	// Write file
	try {
		await fs.writeFile(outputFileName, markdownContent);
		console.log(`✅ Enhanced project analysis saved to: ${outputFileName}`);
		return outputFileName;
	} catch (error) {
		console.error("❌ Error writing output file:", error);
		process.exit(1);
	}
}