/**
 * API response type definitions.
 * Provides standardized response structures for all API endpoints.
 * @module response.types
 */
import type { AccessTokenPayload, RefreshTokenPayload } from './token.types';

/**
 * Metadata-related types.
 * Used for standardized data across microservices.
 * @module metadata.types
 */
export interface PaginationMetadata {
	page: number;
	limit: number;
	total: number;
	hasNext: boolean;
	totalPages: number;
	hasPrevious: boolean;
}

/**
 * Base response interface that all responses will follow.
 * Provides consistent structure for client-side handling.
 */
export interface ApiResponse<T = any> {
	success: boolean;
	message: string;
	data?: T;
	error?: {
		code: string;
		message: string;
		details?: any;
	};
	pagination?: PaginationMetadata;
}

/**
 * Success response with generic data type.
 * Used for all successful API operations.
 */
export interface SuccessResponse<T = any> extends ApiResponse<T> {
	success: true;
	data: T;
}

/**
 * Authentication token response type.
 * Contains tokens and payload information for authenticated sessions.
 */
export interface TokenResponse {
	token: string;
	refreshToken?: string;
	tokenPayload: AccessTokenPayload | RefreshTokenPayload;
}

/**
 * Error response type.
 * Provides standardized error information for failed operations.
 */
export interface ErrorResponse extends ApiResponse {
	success: false;
	error: {
		code: string;
		message: string;
		details?: any;
	};
}