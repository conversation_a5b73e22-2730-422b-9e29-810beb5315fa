{"description": "Shared DTOs for introspection microservices not reliant on typegoose and mongoose", "name": "excelytics.shared-dtos", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "version": "0.1.7", "type": "module", "author": "<PERSON><PERSON>", "license": "UNLICENSED", "scripts": {"clean": "rm -rf dist", "build": "bun run clean && tsc", "prepublishOnly": "bun run build", "publish:package": "bun install && bun run build && bun publish", "script": "bun run ./src/scripts/cli.ts", "map": "bun run ./src/scripts/cli.ts script map . --show-all-with-hide-list", "map:root": "bun run src/scripts/cli.ts map ./../.. --show-all-with-hide-list", "map:root:sonnet": "bun run src/scripts/cli.ts map:sonnet ./../.. --show-all-with-hide-list"}, "exports": {".": "./dist/index.js", "./auth": "./dist/auth/index.js", "./enums": "./dist/enums/index.js", "./scripts": "./dist/scripts/index.js", "./types": "./dist/types/index.js", "./utils": "./dist/utils/index.js"}, "keywords": ["enums", "auth", "utils", "types", "scripts"], "files": ["dist", "src"], "devDependencies": {"@types/bun": "latest", "bun": "^1.2.15", "bun-types": "^1.2.15"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"zod": "^3.25.57"}, "engines": {"bun": ">=1.0.0"}, "publishConfig": {"registry": "https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/"}}