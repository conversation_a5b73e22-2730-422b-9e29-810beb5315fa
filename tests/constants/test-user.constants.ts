import { EnumClientOrigin, UserRoles } from 'excelytics.shared-internals';
import type { TestUser } from '@/constants/test.types';
import mongoose from 'mongoose';

// --- Used in Identity-Management Tests ---
const timestamp = Date.now();

// Admin user for testing identity management
export const adminUser: TestUser = {
	email: `admin-test-${timestamp}@example.com`,
	clientOrigin: EnumClientOrigin.Excelytics,
	clientId: new mongoose.Types.ObjectId(),
	password: 'AdminTest@123',
	roles: [UserRoles.USER, UserRoles.ADMIN], // Both user and admin roles
	isActive: true
};

// Regular user for testing
export const regularUser: TestUser = {
	email: `regular-user-${timestamp}@example.com`,
	clientOrigin: EnumClientOrigin.Excelytics,
	clientId: new mongoose.Types.ObjectId(),
	password: 'RegularUser@123',
	roles: [UserRoles.USER], // Only user role
	isActive: true
};

// Test user to be managed
export const managedUser: TestUser = {
	clientId: new mongoose.Types.ObjectId(),
	clientOrigin: EnumClientOrigin.Excelytics,
	email: `managed-user-${timestamp}@example.com`,
	password: 'ManagedUser@123',
	roles: [UserRoles.USER], // Regular user role
	isActive: true
};