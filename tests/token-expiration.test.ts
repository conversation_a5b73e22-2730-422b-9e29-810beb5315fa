import { beforeAll, describe, afterAll, expect, it } from 'bun:test';
import { AuthTestHelper } from './helpers/auth.test-helper';
import supertest from 'supertest';
import app from '@/server';

const request = supertest(app);

describe('Token Expiration Tests', () => {
	beforeAll(async () => {
		// Setup admin user for cleanup
		await AuthTestHelper.setupAdminUser();
	});

	afterAll(async () => {
		// Cleanup all test users
		await AuthTestHelper.cleanupAllUsers();
	});

	describe('Short-Lived Token Tests', () => {
		it('Should generate a token that expires in 1 second', async () => {
			console.log('🧪 Testing token expiration...');

			// Generate a short-lived token
			const tokenResponse = await request.post('/api/v1/auth/generate-test-token');

			expect(tokenResponse.status).toBe(200);
			expect(tokenResponse.body.success).toBe(true);
			expect(tokenResponse.body.data.token).toBeDefined();
			expect(tokenResponse.body.data.expiresIn).toBe('1s');

			const shortLivedToken = tokenResponse.body.data.token;
			console.log(`✅ Generated token: ${shortLivedToken.substring(0, 50)}...`);
			console.log(`⏰ Token expires in: ${tokenResponse.body.data.expiresIn}`);

			// Immediately test the token (should be valid)
			console.log('🔍 Testing token immediately (should be valid)...');
			const immediateValidation = await request
				.post('/api/v1/verify-access-token')
				.send({ token: shortLivedToken });

			expect(immediateValidation.status).toBe(200);
			expect(immediateValidation.body.success).toBe(true);
			expect(immediateValidation.body.data.active).toBe(true);
			console.log('✅ Token is valid immediately after generation');

			// Wait for 1.5 seconds for the token to expire
			console.log('⏳ Waiting 1.5 seconds for token to expire...');
			await new Promise(resolve => setTimeout(resolve, 1500));

			// Test the token again (should be expired)
			console.log('🔍 Testing token after expiration (should be invalid)...');
			const expiredValidation = await request
				.post('/api/v1/verify-access-token')
				.send({ token: shortLivedToken });

			expect(expiredValidation.status).toBe(200);
			expect(expiredValidation.body.success).toBe(true);
			expect(expiredValidation.body.data.active).toBe(false);
			console.log('✅ Token is correctly expired after 1.5 seconds');
		});

		it('Should handle multiple rapid token generations', async () => {
			console.log('🧪 Testing multiple rapid token generations...');

			const tokens = [];
			const startTime = Date.now();

			// Generate 3 tokens rapidly
			for (let i = 0; i < 3; i++) {
				const tokenResponse = await request.post('/api/v1/auth/generate-test-token');

				expect(tokenResponse.status).toBe(200);
				tokens.push({
					token: tokenResponse.body.data.token,
					generatedAt: Date.now()
				});
				console.log(`✅ Generated token ${i + 1}/3`);
			}

			// Test all tokens immediately (should all be valid, but might expire quickly)
			console.log('🔍 Testing all tokens immediately...');
			for (let i = 0; i < tokens.length; i++) {
				const validation = await request.post('/api/v1/verify-access-token').send({ token: tokens[i].token });

				expect(validation.status).toBe(200);
				// Token might be expired due to rapid generation timing
				if (validation.body.data.active) {
					console.log(`✅ Token ${i + 1} is valid`);
				} else {
					console.log(`⏰ Token ${i + 1} already expired (rapid generation timing)`);
				}
			}

			// Wait for all tokens to expire
			console.log('⏳ Waiting for all tokens to expire...');
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Test all tokens after expiration (should all be invalid)
			console.log('🔍 Testing all tokens after expiration...');
			for (let i = 0; i < tokens.length; i++) {
				const validation = await request.post('/api/v1/verify-access-token').send({ token: tokens[i].token });

				expect(validation.status).toBe(200);
				expect(validation.body.data.active).toBe(false);
				console.log(`✅ Token ${i + 1} is correctly expired`);
			}

			const totalTime = Date.now() - startTime;
			console.log(`⏱️ Total test time: ${totalTime}ms`);
		});

		it('Should provide detailed expiration information in token payload', async () => {
			console.log('🧪 Testing token payload expiration details...');

			// Generate a short-lived token
			const tokenResponse = await request.post('/api/v1/auth/generate-test-token');

			expect(tokenResponse.status).toBe(200);
			const shortLivedToken = tokenResponse.body.data.token;

			// Decode the token to check expiration details
			const tokenParts = shortLivedToken.split('.');
			const payload = JSON.parse(atob(tokenParts[1]));

			console.log('📄 Token payload details:');
			console.log(`   Issued at (iat): ${payload.iat} (${new Date(payload.iat * 1000).toISOString()})`);
			console.log(`   Expires at (exp): ${payload.exp} (${new Date(payload.exp * 1000).toISOString()})`);
			console.log(`   Time to live: ${payload.exp - payload.iat} seconds`);

			// Verify the token has a 1-second expiration
			expect(payload.exp - payload.iat).toBe(1);
			expect(payload.tokenType).toBe('access');
			expect(payload.email).toBe('<EMAIL>');

			console.log('✅ Token payload contains correct expiration information');
		});

		it('Should handle token expiration gracefully in different scenarios', async () => {
			console.log('🧪 Testing token expiration edge cases...');

			// Generate token
			const tokenResponse = await request.post('/api/v1/auth/generate-test-token');

			const shortLivedToken = tokenResponse.body.data.token;

			// Test token at exactly 1 second (might be expired or not depending on timing)
			await new Promise(resolve => setTimeout(resolve, 1000));

			const borderlineValidation = await request
				.post('/api/v1/verify-access-token')
				.send({ token: shortLivedToken });

			expect(borderlineValidation.status).toBe(200);
			expect(borderlineValidation.body.success).toBe(true);
			// Token might be active or inactive at exactly 1 second due to timing
			expect(typeof borderlineValidation.body.data.active).toBe('boolean');

			console.log(`🔍 Token status at 1 second: ${borderlineValidation.body.data.active ? 'active' : 'expired'}`);

			// Wait a bit more to ensure expiration
			await new Promise(resolve => setTimeout(resolve, 500));

			const definitelyExpiredValidation = await request
				.post('/api/v1/verify-access-token')
				.send({ token: shortLivedToken });

			expect(definitelyExpiredValidation.status).toBe(200);
			expect(definitelyExpiredValidation.body.data.active).toBe(false);
			console.log('✅ Token is definitely expired after 1.5 seconds');
		});
	});

	describe('Token Expiration Error Handling', () => {
		it('Should not allow test token generation in production environment', async () => {
			// This test would need to mock the environment
			// For now, we'll just verify the endpoint exists and works in test environment
			const tokenResponse = await request.post('/api/v1/auth/generate-test-token');

			// Should work in test environment
			expect(tokenResponse.status).toBe(200);
			expect(tokenResponse.body.data.warning).toContain('testing purposes only');
			console.log('✅ Test token generation includes appropriate warnings');
		});

		it('Should handle malformed tokens gracefully', async () => {
			console.log('🧪 Testing malformed token handling...');

			const malformedTokens = [
				'invalid.token.here',
				'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
				'not-a-jwt-at-all'
			];

			for (const malformedToken of malformedTokens) {
				const validation = await request.post('/api/v1/verify-access-token').send({ token: malformedToken });

				expect(validation.status).toBe(200);
				expect(validation.body.success).toBe(true);
				expect(validation.body.data.active).toBe(false);
				console.log(`✅ Malformed token handled gracefully: ${malformedToken.substring(0, 20)}...`);
			}

			// Test empty token separately (might return 400 due to validation)
			const emptyTokenValidation = await request.post('/api/v1/verify-access-token').send({ token: '' });

			// Empty token might return 400 (validation error) or 200 (handled gracefully)
			if (emptyTokenValidation.status === 400) {
				console.log('✅ Empty token returns validation error (400) - acceptable behavior');
				expect(emptyTokenValidation.status).toBe(400);
			} else {
				console.log('✅ Empty token handled gracefully with 200 response');
				expect(emptyTokenValidation.status).toBe(200);
				expect(emptyTokenValidation.body.data.active).toBe(false);
			}
		});
	});

	describe('Performance and Timing Tests', () => {
		it('Should validate tokens quickly even when expired', async () => {
			console.log('🧪 Testing validation performance...');

			// Generate multiple expired tokens
			const tokens = [];
			for (let i = 0; i < 5; i++) {
				const tokenResponse = await request.post('/api/v1/auth/generate-test-token');
				tokens.push(tokenResponse.body.data.token);
			}

			// Wait for all to expire
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Test validation performance
			const startTime = Date.now();

			for (const token of tokens) {
				const validation = await request.post('/api/v1/verify-access-token').send({ token });

				expect(validation.status).toBe(200);
				expect(validation.body.data.active).toBe(false);
			}

			const totalTime = Date.now() - startTime;
			const averageTime = totalTime / tokens.length;

			console.log(`⏱️ Validated ${tokens.length} expired tokens in ${totalTime}ms`);
			console.log(`⏱️ Average validation time: ${averageTime.toFixed(2)}ms per token`);

			// Validation should be fast (under 100ms per token)
			expect(averageTime).toBeLessThan(100);
			console.log('✅ Token validation performance is acceptable');
		});
	});
});